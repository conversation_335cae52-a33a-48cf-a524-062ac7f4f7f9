using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.SIM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("SIM_B_WORKING_HOUR")]
    public class WorkingHourEntity : EntityBase
    {
        public WorkingHourEntity()
        {
        }
        /// <summary>
        /// Desc:日期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DATE")]
        public string Date { get; set; }
        /// <summary>
        /// Desc:员工ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EMPLOYEE_ID")]
        public string EmployeeId { get; set; }
        /// <summary>
        /// Desc:三级部门
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PLANT_NAME")]
        public string PlantName { get; set; }
        /// <summary>
        /// Desc:组别
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "GROUP_NAME")]
        public string GroupName { get; set; }
        /// <summary>
        /// Desc:实际工作时数
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_WORKING_HOUR")]
        public decimal ActualWorkingHour { get; set; }

    }
}