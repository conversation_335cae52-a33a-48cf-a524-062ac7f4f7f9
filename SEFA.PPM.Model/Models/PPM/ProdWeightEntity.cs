using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PPM_M_PROD_WEIGHT")]
    public class ProdWeightEntity : EntityBase
    {
        public ProdWeightEntity () {
        }
        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:配方号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FORMULA_CODE")]
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:规格代码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SALE_CONTAINER_CODE")]
        public string SaleContainerCode { get; set; }
        /// <summary>
        /// Desc:规格名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SALE_CONTAINER_NAME")]
        public string SaleContainerName { get; set; }
        /// <summary>
        /// Desc:重量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WEIGHT")]
        public decimal? Weight { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

    }
}