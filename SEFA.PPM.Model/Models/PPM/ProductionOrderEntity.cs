using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.PPM.Model.Models.PTM;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_B_PRODUCTION_ORDER")]
	public class ProductionOrderEntity : EntityBase
	{
		public ProductionOrderEntity()
		{
		}
		/// <summary>
		/// Desc:订单号
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_NO")]
		public string ProductionOrderNo { get; set; }
		/// <summary>
		/// Desc:物料版本ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_VERSION_ID")]
		public string MaterialVersionId { get; set; }

		/// <summary>
		/// Desc:物料ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_ID")]
		public string MaterialId { get; set; }
		/// <summary>
		/// Desc:类型：Batch，WorkOrder，PlanOrder
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "Type")]
		public string Type { get; set; }
		/// <summary>
		/// Desc:父级ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PARENT_ID")]
		public string ParentId { get; set; }
		/// <summary>
		/// Desc:计划数量
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_QTY")]
		public decimal PlanQty { get; set; }
		/// <summary>
		/// Desc:设定速度
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SPEED")]
		public int? Speed { get; set; }
		/// <summary>
		/// Desc:设定速度单位
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SPEED_UOM")]
		public string SpeedUom { get; set; }
		/// <summary>
		/// Desc:BOM版本
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "BOM_VERSION")]
		public string BomVersion { get; set; }
		/// <summary>
		/// Desc:长文本版本
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TEXT_VERSION")]
		public string TextVersion { get; set; }
		/// <summary>
		/// Desc:计划开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_START_TIME")]
		public DateTime? PlanStartTime { get; set; }
		/// <summary>
		/// Desc:计划结束时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_END_TIME")]
		public DateTime? PlanEndTime { get; set; }
		/// <summary>
		/// Desc:订单状态
		/// Default:
		/// 1:未释放
		/// 2:已释放
		/// 3:已完成
		/// 4:废弃
		/// 5:暂停
		/// 6:运行中
		/// 7:QA通过
		/// </summary>
		[SugarColumn(ColumnName = "PO_STATUS")]
		public string PoStatus { get; set; }
		/// <summary>
		/// Desc:下发状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "RELEASE_STATUS")]
		public string ReleaseStatus { get; set; }
		/// <summary>
		/// Desc:完结状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FINAL_STATUS")]
		public string FinalStatus { get; set; }
		/// <summary>
		/// Desc:容量
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CAPICITY")]
		public int? Capicity { get; set; }
		/// <summary>
		/// Desc:期望效率
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "EXPECTED_EFFICIENCY")]
		public decimal? ExpectedEfficiency { get; set; }
		/// <summary>
		/// Desc:备注
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REMARK")]
		public string Remark { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int Deleted { get; set; }

		/// <summary>
		/// Desc:工段编码
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SEGMENT_CODE")]
		public string SegmentCode { get; set; }

		/// <summary>
		/// Desc:产线编码
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LINE_CODE")]
		public string LineCode { get; set; }

		/// <summary>
		/// Desc:备料班次
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PREPARE_SHIFTID")]
		public string PrepareShiftid { get; set; }

		/// <summary>
		/// Desc:SAP工单类型  ZXH9 包装车间返工工单   ZXH1 包装   ZXH2 制造   ZXH4  开盖取料(制造车间返工工单)
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SAPORDERTYPE")]
		public string SapOrderType { get; set; }
		/// <summary>
		/// Desc:MES工单号 'C'+4为年+2位月+2位日+4位顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MES_ORDER_CODE")]
		public string MesOrderCode { get; set; }
		/// <summary>
		/// Desc:工单类型2 区分PACK 工单，FILL工单
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PDTYPE")]
		public string PDType { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Sequence")]
		public long Sequence { get; set; }
		/// <summary>
		/// Desc:SAP配方
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SapFormula")]
		public string SapFormula { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "ProduceStatus")]
		public string ProduceStatus { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Reason")]
		public string Reason { get; set; }
		/// <summary>
		/// Desc:SAP状态
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SapStatus")]
		public string SapStatus { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QASTATUS")]
		public string QaStatus { get; set; }

		/// <summary>
		/// Desc:是否有喉头标识
		/// Default:0 
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "HAS_THROAT")]
		public int HasThroat { get; set; }

		/// <summary>
		/// Desc:返工责任部门
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REWORKDRI")]
		public string ReWorkDRI { get; set; }

		/// <summary>
		/// Desc:返工类型
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REWORKTYPE")]
		public string ReWorkType { get; set; }
		/// <summary>
		/// Desc:返工原因
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REWORKREASON")]
		public string ReWorkReason { get; set; }
		/// <summary>
		/// Desc:返工原因备注
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REWORKREMARK")]
		public string ReWorkRemark { get; set; }
		/// <summary>
		/// Desc:返工方法
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "REWORKMATHORD")]
		public string ReWorkMathord { get; set; }
		/// <summary>
		/// Desc:SAP同步状态
		/// Default:0 无需同步，1 待同步，2 同步中 3 同步成功 4 同步失败 
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SAP_FLAG")]
		public int SapFlag { get; set; }

		/// <summary>
		/// DESC:订单类型
		/// DEFAULT:C    C 普通工单 ， S 试制工单， O 油渣工单， F 返工工单
		/// Nullable:true
		/// </summary>
		[SugarColumn(ColumnName = "ORDER_TYPE")]
		public string OrderType { get; set; }

		/// <summary>
		/// 灌装产线
		/// </summary>
		[SugarColumn(ColumnName = "FILL_LINE_ID")]
		public string FillLineId { get; set; }
		/// <summary>
		/// Desc:开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "START_TIME")]
		public DateTime? StartTime { get; set; }
		/// <summary>
		/// Desc:结束时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "END_TIME")]
		public DateTime? EndTime { get; set; }
		/// <summary>
		/// Desc:QA时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QA_TIME")]
		public DateTime? QATime { get; set; }

        /// <summary>
        /// Desc:计划日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_DATE")]
        public DateTime? PlanDate { get; set; }

        /// <summary>
        /// Desc:配方物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FORMULA_MATERIAL_ID")]
        public string FormulaMaterialId { get; set; }

        /// <summary>
        /// Desc:LongTextResult
        /// Default:长文本比对结果
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LONG_TEXT_RESULT")]
        public string LongTextResult { get; set; }
    }

	public class ProductionOrderModel
	{
		public ProductionOrderEntity Po { get; set; }

		public List<PoEquipmentEntity> Equipments { get; set; }

	}
}