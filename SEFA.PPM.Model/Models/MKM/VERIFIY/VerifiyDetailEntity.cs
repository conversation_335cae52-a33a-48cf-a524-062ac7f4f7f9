using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("MKM_B_VERIFIY_DETAIL")] 
    public class VerifiyDetailEntity : EntityBase
    {
        public VerifiyDetailEntity()
        {
        }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:追溯码ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SUBLOT_ID")]
        public string SublotId { get; set; }
           /// <summary>
           /// Desc:批次ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:盘点结果
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RESULT")]
        public string Result { get; set; }
           /// <summary>
           /// Desc:库存数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CURRENT_QUANTITY")]
        public decimal CurrentQuantity { get; set; }
           /// <summary>
           /// Desc:盘点实数
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_QUANTITY")]
        public decimal ActualQuantity { get; set; }
           /// <summary>
           /// Desc:单位ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:差异值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DIFFERENCE")]
        public decimal? Difference { get; set; }
           /// <summary>
           /// Desc:差异原因
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REASON")]
        public string Reason { get; set; }
           /// <summary>
           /// Desc:主表ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VERIFIYLIST_ID")]
        public string VerifiylistId { get; set; }
           /// <summary>
           /// Desc:颜色（绿色/红色）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VERIFIY_COLOR")]
        public string VerifiyColor { get; set; }
           /// <summary>
           /// Desc:差异值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DIFFERENCENUMBER")]
        public decimal? Differencenumber { get; set; }
           /// <summary>
           /// Desc:物料用，已转移，已退库
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="INVENTTYPE")]
        public string Inventtype { get; set; }
           /// <summary>
           /// Desc:SAPNO
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAPNO")]
        public string Sapno { get; set; }
           /// <summary>
           /// Desc:是否已读
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ISREAD")]
        public string Isread { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:复盘优先排序
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ORDERNUMBER")]
        public string Ordernumber { get; set; }
           /// <summary>
           /// Desc:配方号喉头用
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAPFORMULA")]
        public string Sapformula { get; set; }
    }
}