using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///按规格统计各月产量
    ///</summary>
    
    [SugarTable("V_MKM_PACK_CONTAINER_VIEW")] 
    public class PackContainerViewEntity : EntityBase
    {
        public PackContainerViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SALES_CONTAINER")]
        public int? SalesContainer { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="YEAR")]
        public int Year { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MONTH")]
        public int Month { get; set; }

    }
}