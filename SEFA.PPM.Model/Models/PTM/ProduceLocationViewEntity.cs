using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///工单产出-location下拉选
	///</summary>

	[SugarTable("V_PTM_PRODUCE_LOCATION_VIEW")]
	public class ProduceLocationViewEntity : EntityBase
	{
		public ProduceLocationViewEntity()
		{
		}
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_ID")]
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_CODE")]
		public string EquipmentCode { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_NAME")]
		public string EquipmentName { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOCATION_ID")]
		public string LocationId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOCATION_CODE")]
		public string LocationCode { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LOCATION_NAME")]
		public string LocationName { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "IsDefault")]
		public string IsDefault { get; set; }

	}
}