using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PTM.Model.Models
{
	///<summary>
	///获取启用的EQUIPMENT以及对应的FUNCTION
	///</summary>

	[SugarTable("V_PTM_EQUIPMENT_FUNCTION_VIEW")]
	public class EquipmentFunctionViewEntity : EntityBase
	{
		public EquipmentFunctionViewEntity()
		{
		}

		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_GROUP_ROW_ID")]
		public string EquipmentGroupRowId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SORT_ORDER")]
        public int SortOrder { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_CODE")]
		public string EquipmentCode { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_NAME")]
		public string EquipmentName { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FUNCTION_CODES")]
		public string FunctionCodes { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "NEED_QA_RELEASE")]
		public string NeedQARelease { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_START_TIME")]
		public DateTime? PlanStartTime { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "LINE_CODE")]
		public string LineCode { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Sequence")]
		public long Sequence { get; set; }
	}
}