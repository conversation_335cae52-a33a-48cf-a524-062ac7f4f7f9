using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.PTM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PTM_B_COOK_CONFIRMATION")]
    public class CookConfirmationEntity : EntityBase
    {
        public CookConfirmationEntity()
        {
        }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ORDER_ID")]
        public string OrderId { get; set; }
        /// <summary>
        /// Desc:产品ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:开始时间
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "StART_TIME")]
        public DateTime StartTime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "END_TIME")]
        public DateTime EndTime { get; set; }
        /// <summary>
        /// Desc:停机时长
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DURATION")]
        public decimal Duration { get; set; }
        /// <summary>
        /// Desc:运行时长
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RUNTIME_HOURS")]
        public decimal RuntimeHours { get; set; }
        /// <summary>
        /// Desc:停机原因
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REASON")]
        public string Reason { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public int Status { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "ISMMI")]
		public int? ISMMI { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG")]
		public string Msg { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_NO")]
		public string CONF_NO { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_CNT")]
		public string CONF_CNT { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME")]
		public DateTime? SendTime { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME2")]
		public DateTime? SendTime2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG2")]
		public string Msg2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS2")]
		public int Status2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "CONF_TEXT")]
		public string CONF_TEXT { get; set; }
	}
}