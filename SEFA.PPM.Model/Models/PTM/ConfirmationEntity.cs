using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PTM_B_CONFIRMATION")]
	public class ConfirmationEntity : EntityBase
	{
		public ConfirmationEntity()
		{
		}
		/// <summary>
		/// Desc:工单ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "ORDER_ID")]
		public string OrderId { get; set; }
		/// <summary>
		/// Desc:工序ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SAP_SEGMENT_ID")]
		public string SapSegmentId { get; set; }
		/// <summary>
		/// Desc:运行时长
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "RUNTIME")]
		public decimal Runtime { get; set; }
		/// <summary>
		/// Desc:计划停机时长
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PLANNEDTIME")]
		public decimal Plannedtime { get; set; }
		/// <summary>
		/// Desc:非计划停机时长
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "UNPLANNEDTIME")]
		public decimal Unplannedtime { get; set; }
		/// <summary>
		/// Desc:运行时长修改
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "RUNTIME_OVERRIDE")]
		public decimal? RuntimeOverride { get; set; }
		/// <summary>
		/// Desc:计划停机时长修改
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLANNEDTIME_OVERRIDE")]
		public decimal? PlannedtimeOverride { get; set; }
		/// <summary>
		/// Desc:非计划停机时长修改
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNPLANNEDTIME_OVERRIDE")]
		public decimal? UnplannedtimeOverride { get; set; }
		/// <summary>
		/// Desc:人员工时
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "CREW_HOURS")]
		public decimal CrewHours { get; set; }
		/// <summary>
		/// Desc:良品数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "GOOD_COUNT")]
		public decimal GoodCount { get; set; }
		/// <summary>
		/// Desc:废品数
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "WASTE_COUNT")]
		public decimal? WasteCount { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public int Status { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "TOTAL_COUNT")]
		public decimal TotalCount { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME")]
		public DateTime? SendTime { get; set; }
		/// <summary>
		/// 工单关闭时间
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "ORDER_CLOSE_TIME")]
		public DateTime? OrderCloseTime { get; set; }
		/// <summary>
		/// Desc:理论运行中时长
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STD_RUNTIME")]
		public decimal StdRuntime { get; set; }
		/// <summary>
		/// Desc:转换后产量
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "COV_GOOD_COUNT")]
		public decimal CovGoodCount { get; set; }
		/// <summary>
		/// Desc:转换后单位
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "COV_UNIT")]
		public string CovUnit { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG")]
		public string Msg { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_NO")]
		public string CONF_NO { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_CNT")]
		public string CONF_CNT { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SENDTIME2")]
		public DateTime? SendTime2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS2")]
		public int Status2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG2")]
		public string Msg2 { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CONF_TEXT")]
		public string CONF_TEXT { get; set; }
	}

	public class ConfirmationEntityModel : ConfirmationEntity
	{
		/// <summary>
		/// Desc:工序编码
		/// Default:
		/// Nullable:False
		/// </summary>
		public string SegmentCode { get; set; }
		/// <summary>
		/// Desc:工序名称
		/// Default:
		/// Nullable:False
		/// </summary>
		public string SegmentName { get; set; }
	}
}