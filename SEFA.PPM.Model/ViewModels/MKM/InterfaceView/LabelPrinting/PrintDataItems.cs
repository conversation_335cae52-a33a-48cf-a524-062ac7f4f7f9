using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.InterfaceView
{
    public class PrintDataItems
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string itembarcode { get; set; }

        /// <summary>
        /// 包装规格
        /// </summary>
        public string thispackqty { get; set; }

        /// <summary>
        /// 收货日期
        /// </summary>
        public string recivedate { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public string product_date { get; set; }

        /// <summary>
        /// 有效期
        /// </summary>
        public string validity_date { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string itemcode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string itemname { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        public string batch { get; set; }

        /// <summary>
        /// 条码数量
        /// </summary>
        public string qty { get; set; }

        /// <summary>
        /// 基础单位
        /// </summary>
        public string itemunitcode { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string suppliercode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string suppliername { get; set; }

    }
}
