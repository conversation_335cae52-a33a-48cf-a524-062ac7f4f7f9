using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class UnitConvertRequestModel : RequestPageModelBase
    {
        public UnitConvertRequestModel()
        {
        }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:源单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FormUnitId { get; set; }
           /// <summary>
           /// Desc:源单位名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FormUnitName { get; set; }
           /// <summary>
           /// Desc:转换单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ToUnitId { get; set; }
           /// <summary>
           /// Desc:转换单位名称
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ToUnitName { get; set; }
           /// <summary>
           /// Desc:源单位数值
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? ConvertFormQty { get; set; }
           /// <summary>
           /// Desc:转化单位数值
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? ConvertToQty { get; set; }
           /// <summary>
           /// Desc:生效开始时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? EffectiveBeginDate { get; set; }
           /// <summary>
           /// Desc:生效结束时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? EffectiveEndDate { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }
    }
}