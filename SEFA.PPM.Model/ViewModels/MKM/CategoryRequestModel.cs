using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class CategoryRequestModel : RequestPageModelBase
    {
        public CategoryRequestModel()
        {
        }
           /// <summary>
           /// Desc:编号
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Code { get; set; }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Name { get; set; }
           /// <summary>
           /// Desc:厂别
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Identities { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:启用标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Enabled { get; set; }
           /// <summary>
           /// Desc:删除标志
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}