using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class MaterialPreparationViewRequestModel : RequestPageModelBase
    {
        public MaterialPreparationViewRequestModel()
        {
        }

        public string PIDS { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProLine { get; set; }

        public string ProID { get; set; }

        public string ShiftName { get; set; }

        public string keyWord { get; set; }

        public string ProOrder { get; set; }

        public string MCode { get; set; }

        public string FormulaNo { get; set; }
        public string Quantity { get; set; }
       
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string ProOrder { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string ProResource { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string ProStatus { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string MaterinalCode { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string MaterinalName { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        public string ProductFamily { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:False
        //   /// </summary>
        //public decimal PlanQty { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string ProUnit { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public int? PrepStatuscount { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public int? PrepStatustotalcount { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public DateTime? PlanStartTime { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        public string EquipmentCode { get; set; }

        public string RoomID { get; set; }

        public DateTime? StarTime { get; set; }

        public DateTime? EndTime { get; set; }
        //   /// <summary>
        //   /// Desc:
        //   /// Default:
        //   /// Nullable:True
        //   /// </summary>
        //public string EquipmentName { get; set; }

    }
}