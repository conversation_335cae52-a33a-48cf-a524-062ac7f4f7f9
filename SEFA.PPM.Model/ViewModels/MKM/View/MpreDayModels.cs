using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{

    public class MpreDayModels
    {
        /// <summary>
        /// 产线
        /// </summary>
        public string LineName { get; set; }
       public List<MpreDayModels1> models1s { get; set; }
        /// <summary>
        /// 未开始备料
        /// </summary>
        public int Totalwbl { get; set; }
        /// <summary>
        /// 备料中
        /// </summary>
        public int TotalBlz { get; set; }
        /// <summary>
        /// 已备料
        /// </summary>
        public int TotalYbl { get; set; }
        /// <summary>
        /// 总工单数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        ///已灌锅数
        /// </summary>
        public int Yg { get; set; }
        /// <summary>
        /// 已投锅数
        /// </summary>
        public int Yt { get; set; }
        /// <summary>
        /// 已备锅数
        /// </summary>
        public int Yb { get; set; }
        /// <summary>
        /// 总锅数 
        /// </summary>
        public int ProduceTotal { get; set; }
    }

    public class MpreDayModels1
    {
        /// <summary>
        /// 配方
        /// </summary>
        public string SapFormula { get; set; }
        /// <summary>
        /// 备料中
        /// </summary>
        public int blz { get; set; }
        /// <summary>
        /// 已备料
        /// </summary>
        public int ybl { get; set; }
        /// <summary>
        /// 未开始备料
        /// </summary>
        public int wbl { get; set; }
        /// <summary>
        /// 总工单数
        /// </summary>
        public int Total { get; set; }
    }
}
