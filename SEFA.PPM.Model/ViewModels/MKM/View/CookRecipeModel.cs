using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class CookRecipeModel
    {
        public string LineName { get; set; }
        /// <summary>
        /// 配方名称+配方码
        /// </summary>
        public string DescriptionName { get; set; }
        public List<EquipmentList> equipmentList { get; set; }
        public List<StorageEquipmentList> StorageList { get; set; }
        /// <summary>
        /// 灌装机设备名称
        /// </summary>
        public string EquipmentgzjName { get; set; }
        /// <summary>
        /// 灌装机设备状态
        /// </summary>
        public string EquipmentgzjState { get; set; }
        /// <summary>
        /// 灌装机速度
        /// </summary>
        public string Speed { get; set; }
        /// <summary>
        /// 储缸预计结束时间
        /// </summary>
        public string StorageEndTime { get; set; }
        /// <summary>
        /// 是否预警
        /// </summary>
        public bool IsWarning { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YB")]
        public int? Yb { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YZ")]
        public int? Yz { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YG")]
        public int? Yg { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "total")]
        public int? Total { get; set; }
        
    }


    public class EquipmentList
    {
        public string EquipmentCode { get; set; }
        public string state { get; set; }// 1、运行中；2、无排产；3、CIP；4、故障；5、停机；6、下缸作业
        //public string PropertyValue { get; set; }
    }
    public class StorageEquipmentList
    {
        public string EquipmentCode { get; set; }
        public string state { get; set; }// 1、运行中；2、无排产；3、CIP；4、故障；5、停机；6、下缸作业
        //public string PropertyValue { get; set; }
    }
}
