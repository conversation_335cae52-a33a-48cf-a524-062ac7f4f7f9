using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels.View;

public class SugarPreSplitModel
{
    public string splitID { get; set; }//库存ID
    public string equipmentId { get; set; } 
    public string quantity { get; set; }//拆分数量
    public bool isPrint { get; set; }//是否打印

    public string selectPrint { get; set; }//打印机


    //袋数
    public decimal offullbags { get; set; }

    /// <summary>
    /// 单包数量
    /// </summary>
    public int bagsSize { get; set; }

    /// <summary>
    /// 余料
    /// </summary>
    public decimal bagsSurplus { get; set; }
}
