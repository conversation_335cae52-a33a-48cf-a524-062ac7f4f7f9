using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class MergeModel
    {
        public string[] IDS { get; set; }
        public string PrintId { get; set; }
        public string EqupmentID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:拆分子批次ID
        /// Nullable:True
        /// </summary>
        public string subID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:拆分数量
        /// Nullable:True
        /// </summary>
        public string inputBagWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:合并的子批次Code
        /// Nullable:True
        /// </summary>
        public string ssccCode { get; set; }


    }
}
