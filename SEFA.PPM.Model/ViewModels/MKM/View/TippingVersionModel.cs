using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class TippingVersionModel
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YB")]
        public int? Yb { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YT")]
        public int? Yt { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "total")]
        public int? Total { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "Speed")]
        public int Speed { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SapFormula")]
		public string SapFormula { get; set; }
	}
}
