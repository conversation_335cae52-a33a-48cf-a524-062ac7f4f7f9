using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.MKM.Model.ViewModels
{
    public class MPreparationViewRequestModel : RequestPageModelBase
    {
        public MPreparationViewRequestModel()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SegmentCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SegmentName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string EquipmentName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? QSum { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MQuantityunit { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? InQuantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
        public string CompleteStates { get; set; }
    }
}