using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class ProdtgtContainerRequestModel : RequestPageModelBase
    {
        public ProdtgtContainerRequestModel()
        {
        }
           /// <summary>
           /// Desc:生产线
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineId { get; set; }
           /// <summary>
           /// Desc:规格
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SalesContainer { get; set; }
           /// <summary>
           /// Desc:规格所属群组
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SalesContainerGrp { get; set; }
           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:False
           /// </summary>
        public int? Year { get; set; }
           /// <summary>
           /// Desc:月
           /// Default:
           /// Nullable:False
           /// </summary>
        public int? Month { get; set; }
           /// <summary>
           /// Desc:预算类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:物理模型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ModelRef { get; set; }
        /// <summary>
        /// Desc:预算产量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Tgt { get; set; }

    }
}