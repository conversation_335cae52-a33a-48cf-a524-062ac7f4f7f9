using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.SIM.View
{
    public class CompletionRateModel
    {
        public int Month {  get; set; }  
        public string PlantDecs {  get; set; }  
        public string PlanStartTime {  get; set; }  
        public string PlanEndTime {  get; set; }  
        public string ProductionOrderId {  get; set; }  
        public string MaterialId {  get; set; }  
        public string MaterialDecs {  get; set; }  
        public string MrpCode {  get; set; }  
        public string PlantId {  get; set; }  
        public decimal? PlanQtyBox {  get; set; }
        public decimal? ActualQtyBox { get; set; }
        public string BoxUnit { get; set; }
        public decimal? PlanQtyBottle { get; set; }
        public decimal? ActualQtyBottle { get; set; }
        public string BottleUnit { get; set; }
        public int OneTimeCompletion { get; set; }
        public string Reason { get; set; }
    }
}
