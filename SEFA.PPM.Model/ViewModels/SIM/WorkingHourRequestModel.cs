using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.SIM
{
    public class WorkingHourRequestModel : RequestPageModelBase
    {
        public WorkingHourRequestModel()
        {
        }
        /// <summary>
        /// Desc:日期
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// Desc:员工ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EmployeeId { get; set; }
        /// <summary>
        /// Desc:三级部门
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PlantName { get; set; }
        /// <summary>
        /// Desc:组别
        /// Default:
        /// Nullable:False
        /// </summary>
        public string GroupName { get; set; }
        /// <summary>
        /// Desc:实际工作时数
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal ActualWorkingHour { get; set; }

    }
}