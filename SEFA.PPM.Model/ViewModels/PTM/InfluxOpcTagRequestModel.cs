using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
    public class InfluxOpcTagRequestModel : RequestPageModelBase
    {
        public InfluxOpcTagRequestModel()
        {
        }
		/// <summary>
		/// 快速查询
		/// </summary>
		public string Search { get; set; }
		/// <summary>
		/// Desc:EQUIPMENT_ID
		/// Default:
		/// Nullable:True
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:SERVER_ID
		/// Default:
		/// Nullable:True
		/// </summary>
		public string ServerId { get; set; }
		/// <summary>
		/// Desc:点位地址
		/// Default:
		/// Nullable:True
		/// </summary>
		public string Tag { get; set; }
		/// <summary>
		/// Desc:点位名称
		/// Default:
		/// Nullable:True
		/// </summary>
		public string Name { get; set; }
		/// <summary>
		/// Desc:点位描述
		/// Default:
		/// Nullable:True
		/// </summary>
		public string Describe { get; set; }
		/// <summary>
		/// Desc:存储周期（秒）
		/// Default:
		/// Nullable:True
		/// </summary>
		public int? SaveCyc { get; set; }
		/// <summary>
		/// Desc:是否存储
		/// Default:
		/// Nullable:True
		/// </summary>
		public bool? IsSave { get; set; }
		/// <summary>
		/// Desc:采集类型（0：周期性，1：变化时）
		/// Default:
		/// Nullable:True
		/// </summary>
		public int? Type { get; set; }
		/// <summary>
		/// Desc:计算公式
		/// Default:
		/// Nullable:True
		/// </summary>
		public string Formula { get; set; }
		/// <summary>
		/// Desc:最新值
		/// Default:
		/// Nullable:True
		/// </summary>
		public string LastValue { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		public int Deleted { get; set; }

	}
}