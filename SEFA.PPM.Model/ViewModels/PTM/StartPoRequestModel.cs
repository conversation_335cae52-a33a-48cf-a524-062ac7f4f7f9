using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels.PTM
{
	public class StartPoRequestModel
	{
		public string ProductionOrderId { get; set; }

		public string PoSegmentRequirementId { get; set; }

		public string BatchId { get; set; }

		public string LotCode { get; set; }

		public string EquipmentId { get; set; }

		public DateTime? StartTime { get; set; }

		public DateTime? ProductionDate { get; set; }

		public DateTime? ExpirationDate { get; set; }

		public string CrewSize { get; set; }

		public bool IsUpdateLtxt { get; set; }

		public string SegmentId { get; set; }

	}

	public class ResumePoRequestModel : StartPoRequestModel
	{
		public string ExecutionId { get; set; }
		public string Comments { get; set; }

	}
}