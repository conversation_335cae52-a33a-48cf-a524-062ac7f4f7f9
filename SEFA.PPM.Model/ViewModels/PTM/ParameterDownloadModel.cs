using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels
{
	public class ParameterDownloadModel
	{
		public List<string> ParameterNameList { get; set; }
		public List<Step> StepList { get; set; }
		public List<ControlRecipe> ControlRecipeList { get; set; }
	}

	public class Step
	{
		public string Name { get; set; }
		public List<Parameter> Parameters { get; set; }

		public string EquipmentNames { get; set; }
	}

	public class Parameter
	{
		public string Name { get; set; }

		public string Value { get; set; }

		public string Uom { get; set; }
	}

	public class ControlRecipe
	{
		public string GrouopName { get; set; }

		public string EquipmentName { get; set; }

		public List<Parameter> Parameters { get; set; }
	}
}
