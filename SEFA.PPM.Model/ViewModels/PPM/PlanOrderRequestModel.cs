using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using System.Collections.Generic;

namespace SEFA.PPM.Model.ViewModels
{
    public class PlanOrderRequestModel : RequestPageModelBase
    {
        public PlanOrderRequestModel()
        {
        }

        /// <summary>
        /// Desc:计划单号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string PlanOrderNo { get; set; }
        /// <summary>
        /// Desc:生产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:生产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MatId { get; set; }
        /// <summary>
        /// Desc:配方code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MatCode { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MatName { get; set; }
        /// <summary>
        /// Desc:生产版本
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProdVersion { get; set; }
        /// <summary>
        /// Desc:SAP配方
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapFormula {  get; set; }
        /// <summary>
        /// Desc:计划表重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Planweight { get; set; }
        /// <summary>
        /// Desc:损耗重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Lossweight { get; set; }
        /// <summary>
        /// Desc:喉头添加量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Throatweight { get; set; }
        /// <summary>
        /// Desc:调节重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Adjustweight { get; set; }
        /// <summary>
        /// Desc:入轻重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Inlightweight { get; set; }
        /// <summary>
        /// Desc:排产重量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Scheduleweight { get; set; }
        /// <summary>
        /// Desc:缸数
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Tankquantity { get; set; }
        /// <summary>
        /// Desc:每缸数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Tankweight { get; set; }
        /// <summary>
        /// Desc:生产日期
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? ProduceDate { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:开始时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? startTime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? endTime { get; set; }
        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 已上传 3 同步状态 4 同步失败 
        /// Nullable:False
        /// </summary>
        public int SapFlag { get; set; }
        public List<int> SapFlagList { get; set; }
    }
}