using SEFA.DFM.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.PPM
{
    /// <summary>
    /// 排产树形结构
    /// </summary>
    public class SortFormulaTreeModel
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 可操作角色
        /// </summary>
        public string role { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime prodate { get; set; }
        /// <summary>
        /// 节点类型 line 产线， formlua 配方， fill 罐装， pack 包装
        /// </summary>
        public string nodeType { get; set; }
        /// <summary>
        /// 顺序号
        /// </summary>
        public long sequence { get; set; }        
        
        /// <summary>
        /// 父节点ID
        /// </summary>
        public string parentId { get; set; }
        /// <summary>
        /// 显示字段1
        /// </summary>
        public string txt1 { get; set; }
        /// <summary>
        /// 显示字段2
        /// </summary>
        public string txt2 { get; set; }
        /// <summary>
        /// 显示字段3
        /// </summary>
        public string txt3 { get; set; }
        /// <summary>
        /// 显示字段4
        /// </summary>
        public string txt4 { get; set; }
        /// <summary>
        /// 显示字段5
        /// </summary>
        public string txt5 { get; set; }
        /// <summary>
        /// 子节点里面
        /// </summary>
        public List<SortFormulaTreeModel> children { get; set; }
    }
}
