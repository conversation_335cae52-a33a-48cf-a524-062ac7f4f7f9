using SEFA.Base.Model.BASE;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.PPM
{
    public class SpecialformulacapacityModel: EntityBase
    {
        public SpecialformulacapacityModel()
        {
        }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:False
        /// </summary>
       
        public string LineName { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:False
        /// </summary>
       
        public string MatId { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
     
        public string MatName { get; set; }
        /// <summary>
        /// Desc:瓶型ID
        /// Default:
        /// Nullable:False
        /// </summary>
       
        public string ContainerId { get; set; }
        /// <summary>
        /// Desc:瓶型名称
        /// Default:
        /// Nullable:False
        /// </summary>
       
        public string ContainerName { get; set; }
        /// <summary>
        /// Desc:煮制重量
        /// Default:
        /// Nullable:true
        /// </summary>
      
        public decimal? Weight { get; set; }
        /// <summary>
        /// Desc:目标产线
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Targetline { get; set; }
        /// <summary>
        /// Desc:目标缸重
        /// Default:
        /// Nullable:true
        /// </summary>
        public decimal? Targetweight { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:目标产线名称
        /// Default:
        /// Nullable:False
        /// </summary>

        public string TargetlineName { get; set; }

        /// <summary>
        /// Desc:配方编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string SapFormula { get; set; }

        /// <summary>
        /// Desc:容器代码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string ContainerCode { get; set; }

        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string MtrCode { get; set; }

    }
}
