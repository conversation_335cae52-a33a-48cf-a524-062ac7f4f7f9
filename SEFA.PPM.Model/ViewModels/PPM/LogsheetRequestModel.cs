using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class LogsheetListByEqmentIdRequestModel
    {
        public LogsheetListByEqmentIdRequestModel()
        {
        }
           /// <summary>
           /// Desc:订单ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PoId { get; set; }
        /// <summary>
        /// Desc:执行订单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExId { get; set; }
        /// <summary>
        /// Desc:设备ID（日志表名）
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EqmentId { get; set; }
        /// <summary>
        /// Desc:批次id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchId { get; set; }

    }
}