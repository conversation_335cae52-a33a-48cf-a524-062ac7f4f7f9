using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class PackspeedRequestModel : RequestPageModelBase
    {
        public PackspeedRequestModel()
        {
        }
           /// <summary>
           /// Desc:工作中心
           /// Default:
           /// Nullable:False
           /// </summary>
        public string WorkCenter { get; set; }
           /// <summary>
           /// Desc:配方代码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string FormulaCode { get; set; }
           /// <summary>
           /// Desc:包装规格
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SalescontainerCode { get; set; }
           /// <summary>
           /// Desc:包装描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SalescontainerName { get; set; }
           /// <summary>
           /// Desc:包装单位
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Packingunit { get; set; }
           /// <summary>
           /// Desc:包装单位描述
           /// Default:
           /// Nullable:True
           /// </summary>
        public string PackingunitName { get; set; }
           /// <summary>
           /// Desc:产品类别
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ProductionCategory { get; set; }
           /// <summary>
           /// Desc:MRP控制者
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Mrp { get; set; }
           /// <summary>
           /// Desc:生产速度 个/小时
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? Speed { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

      
        public string CustomerType { get; set; }

    }
}