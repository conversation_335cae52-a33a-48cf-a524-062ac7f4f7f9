using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class ProductionOrderCipRequestModel : RequestPageModelBase
    {
        public ProductionOrderCipRequestModel()
        {
        }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:CIP类型ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Switchid { get; set; }
           /// <summary>
           /// Desc:CIP类型名
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Switchname { get; set; }
           /// <summary>
           /// Desc:计划开始时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? PlanStartTime { get; set; }
           /// <summary>
           /// Desc:计划结束时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? PlanEndTime { get; set; }
           /// <summary>
           /// Desc:实际开始时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? ActualStartTime { get; set; }
           /// <summary>
           /// Desc:实际结束时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? ActualEndTime { get; set; }
           /// <summary>
           /// Desc:是否在工单开始前清洗
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int IsBefore { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Status { get; set; }
           /// <summary>
           /// Desc:备注说明
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}