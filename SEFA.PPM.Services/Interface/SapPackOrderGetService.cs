using AutoMapper;
using MongoDB.Bson;
using NetTaste;
using Newtonsoft.Json;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using SEFA.Base.Common;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SEFA.PPM.Services.Interface
{
    public class SapPackOrderGetService : BaseServices<SappackorderEntity>, ILkkEsbService
    {
        private readonly IBaseRepository<SappackorderEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<DFM.Model.Models.MaterialEntity> _materialDal;
        private readonly IBaseRepository<DFM.Model.Models.MaterialVersionEntity> _materiaVersionlDal;
        private readonly IBaseRepository<MKM.Model.Models.EquipmentEntity> _equipmentDal;
        private readonly IBaseRepository<ProductionOrderEntity> _proOrderdal;
        private readonly IOrderBomServices _bomService;
        private readonly ISappackorderServices _sapPackService;
        private readonly IBProductionOrderListViewServices _bpListViewService;
        private readonly IBaseRepository<OrderrealtionEntity> _orderrelationDal;
        private readonly IBaseRepository<FormulascheduleEntity> _formulaScheduleDal;
        private readonly IMapper _mapper;

        private readonly IAndonServices _andonServices;
        private readonly string _serviceName = "SAP_PKGORDGET";

        public SapPackOrderGetService(
            IBaseRepository<SappackorderEntity> dal,
            IMapper mapper,
            IBaseRepository<DFM.Model.Models.MaterialEntity> materialDal,
            IBaseRepository<DFM.Model.Models.MaterialVersionEntity> materiaVersionlDal,
            IBaseRepository<MKM.Model.Models.EquipmentEntity> equipmentDal,
            IBaseRepository<ProductionOrderEntity> proOrderdal,
            IBProductionOrderListViewServices bProductionOrderListViewServices,
            IOrderBomServices bomService,
            ISappackorderServices sapPackService,
            IUnitOfWork unitOfWork,
            IBaseRepository<OrderrealtionEntity> orderrelationDal,
            IBaseRepository<FormulascheduleEntity> formulaScheduleDal,
            IAndonServices andonServices
            )
        {
            this._dal = dal;
            base.BaseDal = dal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            this._materialDal = materialDal;
            this._materiaVersionlDal = materiaVersionlDal;
            this._equipmentDal = equipmentDal;
            this._proOrderdal = proOrderdal;
            this._sapPackService = sapPackService;
            this._bomService = bomService;
            this._bpListViewService = bProductionOrderListViewServices;
            this._orderrelationDal = orderrelationDal;
            this._formulaScheduleDal = formulaScheduleDal;
            this._andonServices = andonServices;
            var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
            _serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_PKGORDGET" : serviceName;
        }

        /// <summary>
        /// 接口名称，和ESB的MessageID一致
        /// </summary>
        public string ServiceName
        {
            get => _serviceName;
        }

        /// <summary>
        /// 执行接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResultModel> Execute(DataRequestModel request)
        {
            var result = new DataResultModel(request);
            try
            {
                //MaterialEntity许愿替换成实体类
                var sourceDataList = JsonConvert.DeserializeObject<List<PP_SAP_PackOrder>>(request.data.ToString());

                /*工单类型
                 *zxh1灌包工单 
                 *zxh9灌包装的返工工单 
                 *zxh2煮制工单 
                 *zxh4煮制返工工单 
                 *暂时排除 zxh2
                 */
                var orderTypeList = new List<string>() { "ZXH1", "ZXH4", "ZXH9" };

                var newMvList = new List<MaterialVersionEntity>();
               

                var equipmentList = await _equipmentDal.FindList(a => a.Deleted == 0 );
                var mcodeList = sourceDataList.Select(a => a.MATNR).Distinct();

                foreach (var item in sourceDataList.Select(a => a.MATNR_COMP).Distinct())
                {
                    if (mcodeList.Contains(item))
                        continue;
                    mcodeList.Append(item);
                }

                var mList = await _materialDal.FindList(a => mcodeList.Contains(a.Code) && a.Deleted == 0);
                var mIdList = mList.Select(a => a.ID).Distinct();
                var mvList = await _materiaVersionlDal.FindList(a => mIdList.Contains(a.MaterialId));

                var plandateList = sourceDataList.Select(a => a.GSTRP).Distinct();
                var formulaScheduleList = await _formulaScheduleDal.FindList(a => plandateList.Contains(a.ProduceDate));

                var aufnrList = sourceDataList.Select(a => a.AUFNR).Distinct();
                var existOrderList = await _proOrderdal.FindList(p => aufnrList.Contains(p.ProductionOrderNo));

                _unitOfWork.BeginTran();
                try
                {

                    SerilogServer.LogDebug($"本次接口工单:{sourceDataList.Count}" + sourceDataList.ToJson(), "接收SAP工单数据");
                    SerilogServer.LogDebug($"本次接口工单:{sourceDataList.Count},处理工单信息开始", "接收SAP工单 ");

                    List<SappackorderEntity> addList = new List<SappackorderEntity>();
                    List<SappackorderEntity> updateList = new List<SappackorderEntity>();

                    List<ProductionOrderEntity> addProdList = new List<ProductionOrderEntity>();
                    List<ProductionOrderEntity> updateProdList = new List<ProductionOrderEntity>();
                    List<ProductionOrderEntity> deleteProdList = new List<ProductionOrderEntity>();

                    List<OrderrealtionEntity> orderrealtionEntities = new List<OrderrealtionEntity>();

                    List<string> needRePublicBatchPoList = new List<string>();
                   

                    foreach (var data in sourceDataList)
                    {
                        bool changed = false;
                        if (orderTypeList.Contains(data.AUART,StringComparer.OrdinalIgnoreCase) == false)
                        {
                            SerilogServer.LogDebug($"工单号[{data.AUFNR}]类型[{data.AUART}]无效,跳过", "接收SAP工单 ");
                            continue;
                        }

                        var curOrderList = existOrderList.Where(a => a.ProductionOrderNo == data.AUFNR).OrderByDescending(a=>a.CreateDate);

                        var lineInfo =  equipmentList.Where(x => x.EquipmentCode == data.ARBPL).FirstOrDefault();
                        if (lineInfo == null)
                        {
                            SerilogServer.LogDebug($"工单号[{data.AUFNR}]工作中心[{data.ARBPL}]未维护,跳过", "接收SAP工单 ");
                            continue;
                        }

                        var parentArea = equipmentList.Where(x => x.ID == lineInfo.ParentId).FirstOrDefault();

                        var material = mList.Where(m => m.Code == data.MATNR).FirstOrDefault();

                        if (material == null)
                        {
                            SerilogServer.LogDebug($"工单号[{data.AUFNR}]物料编码[{data.MATNR}]未维护,跳过", "接收SAP工单 ");
                            continue;
                        }
                       

                        SappackorderEntity obj = await _dal.FindEntity(p => p.Aufnr == data.AUFNR);
                        if (obj == null)
                        {
                            obj = new SappackorderEntity()
                            {
                                Aufnr = data.AUFNR,
                                Matnr = data.MATNR,
                                Werks = data.WERKS,
                                Amein = data.AMEIN,
                                AmeinComp = data.AMEIN_COMP,
                                Arbpl = data.ARBPL,
                                Auart = data.AUART,
                                AuartFill = data.AUART_FILL,
                                BatchFw = data.BATCH_FW,
                                Bezei = data.BEZEI,
                                Category = data.CATEGORY,
                                Codtx = data.CODTX,
                                Dispo = data.DISPO,
                                Ean11 = data.EAN11,
                                Ebeln = data.EBELN,
                                Ebelp = data.EBELP,
                                Ferth = data.FERTH,
                                Gltrp = data.GLTRP,
                                Gstrp = data.GSTRP,
                                Ihran = data.IHRAN,
                                Iprkz = data.IPRKZ,
                                Kdauf = data.KDAUF,
                                Kdpos = data.KDPOS,
                                Kunnr1 = data.KUNNR1,
                                Kunnr2 = data.KUNNR2,
                                Kunnr3 = data.KUNNR3,
                                Kunnr4 = data.KUNNR4,
                                Landx = data.LANDX,
                                Landz = data.LANDZ,
                                Lgort = data.LGORT,
                                Lhmg1Fw = data.LHMG1_FW,
                                Ltext1 = data.LTEXT1,
                                Ltext2 = data.LTEXT2,
                                Ltext3 = data.LTEXT3,
                                Ltext4 = data.LTEXT4,
                                Ltext5 = data.LTEXT5,
                                Ltext6 = data.LTEXT6,
                                Magrv = data.MAGRV,
                                Maktx = data.MAKTX,
                                Maktx2 = data.MAKTX2,
                                MaktxCFw = data.MAKTX_C_FW,
                                MengeCFw = data.MENGE_C_FW,
                                MaktxComp = data.MAKTX_COMP,
                                Matkl = data.MATKL,
                                Matnr2 = data.MATNR2,
                                MatnrComp = data.MATNR_COMP,
                                Mhdhb = data.MHDHB,
                                MngPu = data.MNG_PU,
                                MngPuo = data.MNG_PUO,
                                Veran = data.VERAN,
                                Ntgew = data.NTGEW,
                                Normt = data.NORMT,
                                ShelfFw = data.SHELF_FW,
                                Rgmkt = data.RGMKT,
                                Status = data.Status,
                                Pdtype = data.PDTYPE,
                                Plodd = data.PLODD,
                                Plper = data.PLPER,
                                Plqty = data.PLQTY,
                                Slmkt = data.SLMKT,
                                VeranFw = data.VERAN_FW,
                                Psmng = data.PSMNG,
                                PsmngComp = data.PSMNG_COMP,
                                Sortf = data.SORTF,
                                Vhart = data.VHART,
                                Vkbur = data.VKBUR,
                                Vtext = data.VTEXT,
                                Wemng = data.WEMNG,
                                Werks2 = data.WERKS2,
                                Zcylcd = data.ZCYLCD,
                                Zndsc = data.ZNDSC,
                                Fgprie = data.FGPRI,
                                OrderJarClear = data.ORDERJARCLEAR,
                                ETDAT = data.ETDAT,
                                BSTNK = data.BSTNK,
                                VKORG = data.VKORG,
                                Opstatus = "A",
                                PPstatus = "A",
                                ChangeRecord = $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]接收SAP工单\n"
                            };
                            obj.CreateCustomGuid("SAP_PKGORDGET");

                            if (data.Status != null && data.Status.Contains("DLFL"))
                            {
                                obj.Opstatus = "D";
                                obj.PPstatus = "D";
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n";
                            }

                            addList.Add(obj);

                            if (obj.Arbpl.StartsWith("FIL") && !String.IsNullOrEmpty(obj.MatnrComp) && !obj.Status.Contains("DLFL"))
                            {
                                /*暂时不考虑特殊配方，后期需修改 20241204*/
                                var formulaSchedule = formulaScheduleList.Where(a => a.FillLineID == parentArea.ID && a.ProduceDate == data.GSTRP && a.SapFormula == data.NORMT).FirstOrDefault();
                                if (formulaSchedule != null)
                                {
                                    OrderrealtionEntity orderrealtionEntity = new OrderrealtionEntity();
                                    orderrealtionEntity.CookorderId = formulaSchedule.ID;
                                    orderrealtionEntity.CookorderCode = "F";
                                    orderrealtionEntity.FillorderId = obj.ID;
                                    orderrealtionEntity.FillorderCode = obj.Aufnr;
                                    orderrealtionEntity.CreateCustomGuid("admin");

                                    orderrealtionEntities.Add(orderrealtionEntity);

                                    SerilogServer.LogDebug($"工单号[{data.AUFNR}][{data.GSTRP}][{data.NORMT}]与配方关联", "接收SAP工单 ");
                                }
                            }
                        }
                        else
                        {

                            string remark = string.Empty;

                            if (obj.Matnr != data.MATNR)
                            {
                                changed = true;
                                remark += $"订单料号:由[{obj.Matnr}]变更为[{data.MATNR}]\n";
                            }
                            if (obj.Psmng != data.PSMNG)
                            {
                                changed = true;
                                remark += $"计划数量:由[{obj.Psmng}]变更为[{data.PSMNG}]\n";
                            }
                            if (obj.Arbpl != data.ARBPL)
                            {
                                changed = true;
                                remark += $"工作中心:由[{obj.Arbpl}]变更为[{data.ARBPL}]\n";
                            }
                            if (obj.Magrv != data.MAGRV)
                            {
                                changed = true;
                                remark += $"销售容器:由[{obj.Magrv}]变更为[{data.MAGRV}]\n";
                            }
                            if (obj.Gstrp != data.GSTRP)
                            {
                                changed = true;
                                remark += $"计划日期:由[{obj.Gstrp}]变更为[{data.GSTRP}]\n";
                            }
                            if (obj.OrderJarClear != data.ORDERJARCLEAR)
                            {
                                changed = true;
                                remark += $"PMC备注:由[{obj.OrderJarClear}]变更为[{data.ORDERJARCLEAR}]\n";
                            }
                            if (obj.Zcylcd != data.ZCYLCD)
                            {
                                changed = true;
                                remark += $"清缸代码:由[{obj.Zcylcd}]变更为[{data.ZCYLCD}]\n";
                            }
                            if (obj.Codtx != data.CODTX)
                            {
                                changed = true;
                                remark += $"清缸描述:由[{obj.Codtx}]变更为[{data.CODTX}]\n";
                            }
                            if (obj.Dispo != data.DISPO)
                            {
                                changed = true;
                                remark += $"MRP:由[{obj.Dispo}]变更为[{data.DISPO}]\n";
                            }
                            if (obj.Normt != data.NORMT)
                            {
                                changed = true;
                                remark += $"生产配方:由[{obj.Normt}]变更为[{data.NORMT}]\n";
                            }
                            if (obj.MatnrComp != data.MATNR_COMP)
                            {
                                changed = true;
                                remark += $"生产配方物料:由[{obj.MatnrComp}]变更为[{data.MATNR_COMP}]\n";
                            }
                            if (obj.AuartFill != data.AUART_FILL)
                            {
                                remark += $"前道灌装工单:由[{obj.AuartFill}]变更为[{data.AUART_FILL}]\n";
                            }

                            if (changed)
                            {
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]\n" + remark;
                                obj.Opstatus = "U";
                                obj.PPstatus = "U";
                                if(obj.Gstrp >= DateTime.Now.Date && DateTime.Now.Date.AddDays(2) >= obj.Gstrp)
                                {
                                    /*发送Andon*/
                                    try
                                    {
                                        await _andonServices.PackOrderChange(obj.Aufnr, parentArea.EquipmentCode, obj.Gstrp.Value, remark);
                                    }
                                    catch (Exception e) { }
                                }
                            }

                            if (data.Status != null && data.Status.Contains("DLFL") && !obj.Status.Contains("DLFL"))
                            {
                                obj.Opstatus = "D";
                                obj.PPstatus = "D";
                                obj.ChangeRecord += $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n";
                                if (obj.Gstrp >= DateTime.Now.Date && DateTime.Now.Date.AddDays(2) >= obj.Gstrp)
                                {
                                    /*发送Andon*/
                                    try
                                    {
                                        await _andonServices.PackOrderChange(obj.Aufnr, parentArea.EquipmentCode, obj.Gstrp.Value, $"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}]SAP删除工单\n");
                                    }
                                    catch (Exception e) { }
                                }
                            }
                            if (obj.ChangeRecord != null && obj.ChangeRecord.Length > 1000)
                            {
                                obj.ChangeRecord = obj.ChangeRecord.Substring(0, 1000);
                            }

                            obj.Aufnr = data.AUFNR;
                            obj.Matnr = data.MATNR;
                            obj.Werks = data.WERKS;
                            obj.Amein = data.AMEIN;
                            obj.AmeinComp = data.AMEIN_COMP;
                            obj.Arbpl = data.ARBPL;
                            obj.Auart = data.AUART;
                            obj.AuartFill = data.AUART_FILL;
                            obj.BatchFw = data.BATCH_FW;
                            obj.Bezei = data.BEZEI;
                            obj.Category = data.CATEGORY;
                            obj.Codtx = data.CODTX;
                            obj.Dispo = data.DISPO;
                            obj.Ean11 = data.EAN11;
                            obj.Ebeln = data.EBELN;
                            obj.Ebelp = data.EBELP;
                            obj.Ferth = data.FERTH;
                            obj.Gltrp = data.GLTRP;
                            obj.Gstrp = data.GSTRP;
                            obj.Ihran = data.IHRAN;
                            obj.Iprkz = data.IPRKZ;
                            obj.Kdauf = data.KDAUF;
                            obj.Kdpos = data.KDPOS;
                            obj.Kunnr1 = data.KUNNR1;
                            obj.Kunnr2 = data.KUNNR2;
                            obj.Kunnr3 = data.KUNNR3;
                            obj.Kunnr4 = data.KUNNR4;
                            obj.Landx = data.LANDX;
                            obj.Landz = data.LANDZ;
                            obj.Lgort = data.LGORT;
                            obj.Lhmg1Fw = data.LHMG1_FW;
                            obj.Ltext1 = data.LTEXT1;
                            obj.Ltext2 = data.LTEXT2;
                            obj.Ltext3 = data.LTEXT3;
                            obj.Ltext4 = data.LTEXT4;
                            obj.Ltext5 = data.LTEXT5;
                            obj.Ltext6 = data.LTEXT6;
                            obj.Magrv = data.MAGRV;
                            obj.Maktx = data.MAKTX;
                            obj.Maktx2 = data.MAKTX2;
                            obj.MaktxCFw = data.MAKTX_C_FW;
                            obj.MengeCFw = data.MENGE_C_FW;
                            obj.MaktxComp = data.MAKTX_COMP;
                            obj.Matkl = data.MATKL;
                            obj.Matnr2 = data.MATNR2;
                            obj.MatnrComp = data.MATNR_COMP;
                            obj.Mhdhb = data.MHDHB;
                            obj.MngPu = data.MNG_PU;
                            obj.MngPuo = data.MNG_PUO;
                            obj.Veran = data.VERAN;
                            obj.Ntgew = data.NTGEW;
                            obj.Normt = data.NORMT;
                            obj.ShelfFw = data.SHELF_FW;
                            obj.Rgmkt = data.RGMKT;
                            obj.Status = data.Status;
                            obj.Pdtype = data.PDTYPE;
                            obj.Plodd = data.PLODD;
                            obj.Plper = data.PLPER;
                            obj.Plqty = data.PLQTY;
                            obj.Slmkt = data.SLMKT;
                            obj.VeranFw = data.VERAN_FW;
                            obj.Psmng = data.PSMNG;
                            obj.PsmngComp = data.PSMNG_COMP;
                            obj.Sortf = data.SORTF;
                            obj.Vhart = data.VHART;
                            obj.Vkbur = data.VKBUR;
                            obj.Vtext = data.VTEXT;
                            obj.Wemng = data.WEMNG;
                            obj.Werks2 = data.WERKS2;
                            obj.Zcylcd = data.ZCYLCD;
                            obj.Zndsc = data.ZNDSC;
                            obj.Fgprie = data.FGPRI;
                            obj.OrderJarClear = data.ORDERJARCLEAR;
                            obj.ETDAT = data.ETDAT;
                            obj.BSTNK = data.BSTNK;
                            obj.VKORG = data.VKORG;
                            obj.Modify(obj.ID, "SAP_PKGORDGET");
                            updateList.Add(obj);
                        }

                        var materialVersion = mvList.Where(m => m.MaterialId == material.ID).FirstOrDefault();
                        if (materialVersion == null)
                        {
                            materialVersion = newMvList.Where(a => a.MaterialId == material.ID).FirstOrDefault();
                            if (materialVersion == null)
                            {
                                materialVersion = new MaterialVersionEntity();
                                materialVersion.MaterialId = material.ID;
                                materialVersion.MaterialVersionNumber = "0001";
                                materialVersion.CreateCustomGuid("admin");
                                newMvList.Add(materialVersion);
                            }
                        }

                        /*配方料号Id*/
                        var FormulaMaterialId = string.Empty;
                        if (string.IsNullOrWhiteSpace(data.AUART_FILL))
                        {
                            FormulaMaterialId = (await _materialDal.FindList(a => a.Code == data.MATNR_COMP, a => a.CreateDate, false)).FirstOrDefault()?.ID;
                            if (string.IsNullOrEmpty(FormulaMaterialId))
                            {
                                SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]无AUART_FILL,组件料号[{data.MATNR_COMP}]FormulaMaterialId为空", "接收SAP工单 ");
                            }
                        }
                        else
                        {
                            var fillOrder = sourceDataList.FirstOrDefault(a => a.AUFNR == data.AUART_FILL);
                            if (fillOrder != null)
                            {
                                FormulaMaterialId = (await _materialDal.FindList(a => a.Code == fillOrder.MATNR_COMP, a => a.CreateDate, false)).FirstOrDefault()?.ID;
                                if (string.IsNullOrEmpty(FormulaMaterialId))
                                {
                                    SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]对应灌装工单[{fillOrder.AUFNR}]存在AUART_FILL[{data.AUART_FILL}]组件料号[{fillOrder.MATNR_COMP}] FormulaMaterialId为空", "接收SAP工单 ");
                                }
                            }
                            else
                            {
                                var fillOrderEntity = (await _dal.FindList(a => a.Aufnr == data.AUART_FILL)).FirstOrDefault();
                                if (fillOrderEntity != null)
                                {
                                    FormulaMaterialId = (await _materialDal.FindList(a => a.Code == fillOrderEntity.MatnrComp, a => a.CreateDate, false)).FirstOrDefault()?.ID;
                                    SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]存在AUART_FILL[{data.AUART_FILL}],灌装工单配方料号[{fillOrderEntity.MatnrComp}]FormulaMaterialId=[{FormulaMaterialId}]", "接收SAP工单 ");
                                }
                                else
                                {
                                    if (string.IsNullOrEmpty(FormulaMaterialId))
                                    {
                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]存在AUART_FILL[{data.AUART_FILL}],不存在PackOrder表灌装工单[{data.AUART_FILL}]", "接收SAP工单 ");
                                    }
                                }
                            }
                        }


                        if (curOrderList.Count() == 0)
                        {
                            ProductionOrderEntity prod = new ProductionOrderEntity()
                            {
                                LineCode = parentArea.EquipmentCode,
                                SegmentCode = data.ARBPL,
                                ProductionOrderNo = data.AUFNR,
                                PlanQty = data.PSMNG,
                                Type = "WorkOrder",
                                MaterialVersionId = materialVersion.ID,
                                MaterialId = material.ID,
                                PlanStartTime = data.GSTRP,
                                SapOrderType = data.AUART,
                                PDType = data.PDTYPE,
                                SapFormula = data.NORMT,
                                //  MesOrderCode = "P" + (co + i),
                                PoStatus = "1",
                                QaStatus = data.ARBPL.Contains("OVR-PRNT") ? "通过" : "待QA",
                                OrderType = "C",
                                FillLineId = parentArea.ID,
                                PlanDate = data.GSTRP,
                                FormulaMaterialId = FormulaMaterialId
                            };
                            if (data.Status != null && data.Status.Contains("DLFL"))
                            {
                                prod.PoStatus = "4";
                            }
                            prod.CreateCustomGuid("SAP_PKGORDGET");

                            addProdList.Add(prod);
                        }
                        else
                        {
                            var existOrder = curOrderList.Where(a => a.Type == "WorkOrder").OrderByDescending(p => p.CreateDate).FirstOrDefault();
                            if (obj.Opstatus == "D")
                            {
                                /*工单已删除则废弃工单*/
                                existOrder.PoStatus = "4";
                                updateProdList.Add(existOrder);
                                if (needRePublicBatchPoList.Contains(obj.Aufnr))
                                {
                                    needRePublicBatchPoList.Remove(obj.Aufnr);
                                }

                                SerilogServer.LogDebug($"工单号[{data.AUFNR}]被删除,更新工单状态为 4 已取消", "接收SAP工单 ");
                            }
                            else
                            {
                                if (changed)
                                {
                                    if ((existOrder.PoStatus != "3" && existOrder.PoStatus != "4" && existOrder.PoStatus != "6"))
                                    {
                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]原数量为[{existOrder.PlanQty}],原计划日期[{existOrder.PlanStartTime}],原工作中心[{existOrder.SegmentCode}]", "接收SAP工单 ");

                                        existOrder.PlanQty = data.PSMNG;
                                        existOrder.PlanStartTime = data.GSTRP;
                                        existOrder.SegmentCode = data.ARBPL;
                                        existOrder.LineCode = parentArea.EquipmentCode;
                                        existOrder.FillLineId = parentArea.ID;
                                        existOrder.PlanDate = data.GSTRP;
                                        if (!string.IsNullOrEmpty(FormulaMaterialId))
                                        {
                                            existOrder.FormulaMaterialId = FormulaMaterialId;
                                        }
                                        updateProdList.Add(existOrder);

                                        if(existOrder.PoStatus == "2")
                                        {
                                            needRePublicBatchPoList.Add(existOrder.ProductionOrderNo);
                                        }

                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]修改数量为[{existOrder.PlanQty}],修改计划日期[{existOrder.PlanStartTime}],修改工作中心[{existOrder.SegmentCode}]", "接收SAP工单 ");
                                    }
                                    else
                                    {
                                        SerilogServer.LogDebug($"ProductionOrder工单号[{data.AUFNR}]已在MES已完成或取消,无法变更", "接收SAP工单 ");
                                    }
                                }
                            }

                            var deleteList = curOrderList.Where(p => p.ID != existOrder.ID);
                            if (deleteList.Count() > 0)
                            {
                                foreach (var item in deleteList)
                                {
                                    if (deleteProdList.Contains(item))
                                        continue;
                                    deleteProdList.Add(item);
                                }
                                SerilogServer.LogDebug($"删除工单[{string.Join(',', deleteList.Select(a => a.MesOrderCode))}]", "接收SAP工单 ");
                            }
                        }

                    }
                    var RePublicBatchPoList = existOrderList.Where(a => needRePublicBatchPoList.Contains(a.ProductionOrderNo)).ToList();
                    if (RePublicBatchPoList.Count > 0)
                    {
                        SerilogServer.LogDebug($"需要RebuildBatch工单数量[{RePublicBatchPoList.Count}][{string.Join(",", RePublicBatchPoList.Select(a => a.ProductionOrderNo))}]", "接收SAP工单 ");

                        DateTime btime = DateTime.Now;
                        await _bpListViewService.RebuildBatch2(RePublicBatchPoList.Select(a=>a.ID).ToList());
                        TimeSpan span = DateTime.Now - btime;
                        SerilogServer.LogDebug($"RebuildBatch共耗时:{span.TotalSeconds}", "接收SAP工单 ");
                    }

                    if (addList.Any())
                    {
                        await _dal.Add(addList);
                    }
                    if (updateList.Any())
                    {
                        await _dal.Update(updateList);
                    }
                    if(addProdList.Any())
                    {
                        await _proOrderdal.Add(addProdList);
                    }
                    if (updateList.Any())
                    {
                        await _proOrderdal.Update(updateProdList);
                    }
                    if (deleteProdList.Any())
                    {
                        await _proOrderdal.DeleteByIds(deleteProdList.Select(a=>a.ID).ToArray());
                    }
                    if(newMvList.Count > 0)
                    {
                        await _materiaVersionlDal.Add(newMvList);
                    }
                    if (orderrealtionEntities.Count > 0)
                    {
                        await _orderrelationDal.Add(orderrealtionEntities);
                    }
                    


                    _unitOfWork.CommitTran();
                    result.msg = "Success";
                    result.ids = sourceDataList.Select(m => m.ID).ToList();

                    SerilogServer.LogDebug($"新增SapPackOrder:{addList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"更新SapPackOrder:{updateList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"新增ProductionOrder:{addProdList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"更新ProductionOrder:{updateList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"删除ProductionOrder:{deleteProdList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"新增物料版本:{newMvList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"新增配方与灌包工单关系:{newMvList.Count}", "接收SAP工单 ");
                    SerilogServer.LogDebug($"本次接口工单:{sourceDataList.Count},处理工单信息结束", "接收SAP工单 ");
                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    return result.Fail(ex.Message + ex.StackTrace);
                }

                var sapOrderTypeList = sourceDataList.Select(m => m.AUART).Distinct();
                foreach (var ordertype in sapOrderTypeList)
                {
                    var orderNoList = sourceDataList.Where(a=>a.AUART == ordertype).Select(m => m.AUFNR).ToList();
                    await _bomService.GetSapOrderBom(new SapRequestModel
                    {
                        factory = "2010",
                        orderNo = orderNoList,
                        type = "PK",
                        start = new DateTime(2024, 1, 1),
                        end = new DateTime(2099, 1, 1)
                    });

                    SerilogServer.LogDebug($"获取[{ordertype}]工单BOM信息,工单数量[{orderNoList.Count}]处理结束", "接收SAP工单 ");
                    await _sapPackService.GetOrderRouting(new SapRequestModel
                    {
                        factory = "2010",
                        orderNo = orderNoList,
                        type = ordertype,
                        start = new DateTime(2024, 1, 1),
                        end = new DateTime(2099, 1, 1)
                    });
                    SerilogServer.LogDebug($"获取[{ordertype}]工单Routing信息,工单数量[{orderNoList.Count}],处理结束", "接收SAP工单 ");
                }

               
                return result;
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug($"异常：【{ex.ToString()}】", "接收SAP工单 ");
                return result.Fail(ex.Message + ex.StackTrace);
            }
        }
    }
}
