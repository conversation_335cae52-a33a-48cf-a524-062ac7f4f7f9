
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using SEFA.Base.Common.HttpContextUser;
using Magicodes.ExporterAndImporter.Excel;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Common.HttpRestSharp;

namespace SEFA.PPM.Services
{
    public class CipTimeServices : BaseServices<CipTimeEntity>, ICipTimeServices
    {
        private readonly IBaseRepository<CipTimeEntity> _dal;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        public CipTimeServices(IBaseRepository<CipTimeEntity> dal, IUser user,IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            _user = user;
            _unitOfWork = unitOfWork;
            base.BaseDal = dal;
        }

        public async Task<List<CipTimeEntity>> GetList(CipTimeRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CipTimeEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<CipTimeEntity>> GetPageList(CipTimeRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CipTimeEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.LineName), 
                a=>
                 (a.LineName != null && a.LineName.Contains(reqModel.LineName)) ||
                 (a.Switchname != null && a.Switchname.Contains(reqModel.LineName))
                )
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize, " LINE_ID asc");
            
            return data;
        }

        public async Task<bool> SaveForm(CipTimeEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID,_user.Name.ToString());
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="input">文件流</param>
        /// <returns></returns>
        public async Task<ResultString> ImportData(FileImportDto input)
        {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<CipTimeExcelDto>(stream);
            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }
            // MessageModel<List<DataItemDetailModel>> cipswichList = await HttpHelper.PostAsync<List<DataItemDetailModel>>("DFM", "api/DataItemDetail/GetList?itemCode=CipSwitch", _user.GetToken(), new { });
            var cipswichList = await _dal.Db.Queryable<DataItemDetailEntity>().Where(p => p.ItemCode == "CipSwitch" && p.Deleted == 0).ToListAsync();
            if (cipswichList.Count() == 0)
            {
                result.AddError("获取CIP方式字典数据失败，中断操作！");
                return result;
            }
            var excelData = import.Data.Where(x => !string.IsNullOrEmpty(x.LineName) && !string.IsNullOrEmpty(x.Switchname) && x.Switchtime >0).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据(1、产线，CIP方式不能为空 2、切换时间大于0)");
                return result;
            }
            
            var lineInfos = await _dal.Db.Queryable<EquipmentEntity>().Where(p => p.Level == "line").ToListAsync();
          
            var allData = await this.FindList(m => m.Deleted == 0);

            var addList = new List<CipTimeEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];
                if (string.IsNullOrEmpty(item.LineName) || string.IsNullOrEmpty(item.Switchname) || item.Switchtime <= 0 )
                    continue;
                
                var line = lineInfos.Where(x => x.EquipmentCode == item.LineName || x.EquipmentName == item.LineName).FirstOrDefault();
                if (line == null)
                {
                    result.AddError($"第[{i + 1}]行未找到产线代码[{item.LineName}]的基础表信息数据");
                    return result;
                }
                var cip = cipswichList.Where(x => x.ItemName == item.Switchname).FirstOrDefault();

                if (cip == null)
                {
                    result.AddError($"第[{i + 1}]行未找到CIP方式[{item.Switchname}]的字典信息数据");
                    return result;
                }

                var entity = allData.Where(p => p.LineId == line.ID && p.Switchid == cip.ID ).FirstOrDefault();
                if (!addList.Any(p => p.LineId == line.ID && p.Switchid == cip.ID))
                {
                    entity = new CipTimeEntity();
                    entity.LineId = line.LineId;
                    entity.LineName = line.EquipmentCode;
                    entity.Switchid = cip.ID;
                    entity.Switchname = cip.ItemName;
                    entity.Switchtime = item.Switchtime;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
            }
            _unitOfWork.BeginTran();
            try
            {
                if (allData.Any() && addList.Any())
                {
                    var ids = allData.Select(a => a.ID).ToArray();
                    await this.DeleteByIds(ids);
                }

                if (addList.Any())
                {
                    await this.Add(addList);
                }

                _unitOfWork.CommitTran();

                result.Data += $"删除数据{allData.Count()}条,新增数据{addList.Count()}条";
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.AddError(ex.Message);
            }

            return result;

        }
    }
}