
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models.Interface;
using System;
using ORDSTATUS;
using System.Linq;
using AsynPlnCK;
using LKK.Lib.Core;
using SapRoutingGet;
using SEFA.PPM.Services.Helper;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.DFM.Model.Models;
using System.DirectoryServices.ActiveDirectory;
using System.Runtime;
using SEFA.Base.Common.Helper;
using SEFA.Base.Common.LogHelper;
using MongoDB.Bson;
using SEFA.Base.Model.BASE;
using System.ServiceModel.Channels;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Xml.Linq;
using System.Data;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace SEFA.PPM.Services
{
    public class SappackorderServices : BaseServices<SappackorderEntity>, ISappackorderServices
    {
        private readonly IBaseRepository<SappackorderEntity> _dal;
        private readonly IBaseRepository<PlanOrderEntity> _planOrderdal;
        private readonly IBaseRepository<ProductionOrderEntity> _proOrderdal;
        private readonly IBaseRepository<SaproutingEntity> _sapRouting;
        private readonly IBaseRepository<SapPoRoutingEntity> _sapPoRouting;
        private readonly IBaseRepository<MKM.Model.Models.MaterialEntity> _materialDal;
        private readonly IBaseRepository<MKM.Model.Models.EquipmentEntity> _equipmentDal;
        private readonly IBaseRepository<MaterialProcessDataEntity> _processData;
        private readonly LKKESBHelper _lkKESBHelper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IOrderBomServices _orderBomServices;
        public SappackorderServices(IBaseRepository<SappackorderEntity> dal,
            IBaseRepository<ProductionOrderEntity> proOrderdal,
            IBaseRepository<PlanOrderEntity> planOrderdal,
            IBaseRepository<SaproutingEntity> sapRouting,
            IBaseRepository<SapPoRoutingEntity> sapPoRouting,
            IBaseRepository<MKM.Model.Models.MaterialEntity> materialDal,
            IBaseRepository<MKM.Model.Models.EquipmentEntity> equipmentDal,
            IBaseRepository<MaterialProcessDataEntity> processData,
            LKKESBHelper lkKESBHelper,
            IOrderBomServices orderBomServices,
            IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._lkKESBHelper = lkKESBHelper;
            this._unitOfWork = unitOfWork;
            this._planOrderdal = planOrderdal;
            this._proOrderdal = proOrderdal;
            this._sapRouting = sapRouting;
            this._sapPoRouting = sapPoRouting;
            this._materialDal = materialDal;
            this._equipmentDal = equipmentDal;
            this._processData = processData;
            this._orderBomServices = orderBomServices;
        }

        public async Task<List<SappackorderEntity>> GetList(SappackorderRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SappackorderEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<SappackorderEntity>> GetPageList(SappackorderRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SappackorderEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(SappackorderEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
        /// <summary>
        /// 获取SAP包装工单 SAP_PKGORDGET
        /// </summary>  
        /// <returns></returns>
        public async Task<MessageModel<string>> GetSapPackOrder()
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            var responsData = await _lkKESBHelper.PostJson<ESBBaseModel<List<PP_SAP_PackOrder>>, string>("SAP_PKGORDGET", "");
            if (responsData.successed == true)
            {
                var dataList = responsData.Response.data;
                _unitOfWork.BeginTran();
                try
                {
                    foreach (var data in dataList)
                    {
                        var lineInfo = await _equipmentDal.FindEntity(x => x.EquipmentCode == data.ARBPL);
                        if (lineInfo == null)
                        {
                            continue;
                        }
                        var parentArea = await _equipmentDal.FindEntity(x => x.ID == lineInfo.ParentId);
                        SappackorderEntity obj = await _dal.FindEntity(p => p.Aufnr == data.AUFNR);
                        if (obj == null)
                        {
                            obj = new SappackorderEntity()
                            {
                                Aufnr = data.AUFNR,
                                Matnr = data.MATNR,
                                Werks = data.WERKS,
                                Amein = data.AMEIN,
                                AmeinComp = data.AMEIN_COMP,
                                Arbpl = data.ARBPL,
                                Auart = data.AUART,
                                AuartFill = data.AUART_FILL,
                                BatchFw = data.BATCH_FW,
                                Bezei = data.BEZEI,
                                Category = data.CATEGORY,
                                Codtx = data.CODTX,
                                Dispo = data.DISPO,
                                Ean11 = data.EAN11,
                                Ebeln = data.EBELN,
                                Ebelp = data.EBELP,
                                Ferth = data.FERTH,
                                Gltrp = data.GLTRP,
                                Gstrp = data.GSTRP,
                                Ihran = data.IHRAN,
                                Iprkz = data.IPRKZ,
                                Kdauf = data.KDAUF,
                                Kdpos = data.KDPOS,
                                Kunnr1 = data.KUNNR1,
                                Kunnr2 = data.KUNNR2,
                                Kunnr3 = data.KUNNR3,
                                Kunnr4 = data.KUNNR4,
                                Landx = data.LANDX,
                                Landz = data.LANDZ,
                                Lgort = data.LGORT,
                                Lhmg1Fw = data.LHMG1_FW,
                                Ltext1 = data.LTEXT1,
                                Ltext2 = data.LTEXT2,
                                Ltext3 = data.LTEXT3,
                                Ltext4 = data.LTEXT4,
                                Ltext5 = data.LTEXT5,
                                Ltext6 = data.LTEXT6,
                                Magrv = data.MAGRV,
                                Maktx = data.MAKTX,
                                Maktx2 = data.MAKTX2,
                                MaktxCFw = data.MAKTX_C_FW,
                                MengeCFw = data.MENGE_C_FW,
                                MaktxComp = data.MAKTX_COMP,
                                Matkl = data.MATKL,
                                Matnr2 = data.MATNR2,
                                MatnrComp = data.MATNR_COMP,
                                Mhdhb = data.MHDHB,
                                MngPu = data.MNG_PU,
                                MngPuo = data.MNG_PUO,
                                Veran = data.VERAN,
                                Ntgew = data.NTGEW,
                                Normt = data.NORMT,
                                ShelfFw = data.SHELF_FW,
                                Rgmkt = data.RGMKT,
                                Status = data.Status,
                                Pdtype = data.PDTYPE,
                                Plodd = data.PLODD,
                                Plper = data.PLPER,
                                Plqty = data.PLQTY,
                                Slmkt = data.SLMKT,
                                VeranFw = data.VERAN_FW,
                                Psmng = data.PSMNG,
                                PsmngComp = data.PSMNG_COMP,
                                Sortf = data.SORTF,
                                Vhart = data.VHART,
                                Vkbur = data.VKBUR,
                                Vtext = data.VTEXT,
                                Wemng = data.WEMNG,
                                Werks2 = data.WERKS2,
                                Zcylcd = data.ZCYLCD,
                                Zndsc = data.ZNDSC,
                                Fgprie = data.FGPRI,
                                OrderJarClear = data.ORDERJARCLEAR,
                                ETDAT = data.ETDAT,
                                BSTNK = data.BSTNK,
                                VKORG = data.VKORG,
                                Opstatus = "A",
                                PPstatus = "A"
                            };
                            obj.CreateCustomGuid("SAP_PKGORDGET");
                            await _dal.Add(obj);
                        }
                        else
                        {
                            if (obj.Psmng != data.PSMNG || obj.Arbpl != data.ARBPL || obj.Magrv != data.MAGRV || obj.Gstrp != data.GSTRP)
                            {
                                obj.Opstatus = "U";
                                obj.PPstatus = "U";
                            }
                            obj.Aufnr = data.AUFNR;
                            obj.Matnr = data.MATNR;
                            obj.Werks = data.WERKS;
                            obj.Amein = data.AMEIN;
                            obj.AmeinComp = data.AMEIN_COMP;
                            obj.Arbpl = data.ARBPL;
                            obj.Auart = data.AUART;
                            obj.AuartFill = data.AUART_FILL;
                            obj.BatchFw = data.BATCH_FW;
                            obj.Bezei = data.BEZEI;
                            obj.Category = data.CATEGORY;
                            obj.Codtx = data.CODTX;
                            obj.Dispo = data.DISPO;
                            obj.Ean11 = data.EAN11;
                            obj.Ebeln = data.EBELN;
                            obj.Ebelp = data.EBELP;
                            obj.Ferth = data.FERTH;
                            obj.Gltrp = data.GLTRP;
                            obj.Gstrp = data.GSTRP;
                            obj.Ihran = data.IHRAN;
                            obj.Iprkz = data.IPRKZ;
                            obj.Kdauf = data.KDAUF;
                            obj.Kdpos = data.KDPOS;
                            obj.Kunnr1 = data.KUNNR1;
                            obj.Kunnr2 = data.KUNNR2;
                            obj.Kunnr3 = data.KUNNR3;
                            obj.Kunnr4 = data.KUNNR4;
                            obj.Landx = data.LANDX;
                            obj.Landz = data.LANDZ;
                            obj.Lgort = data.LGORT;
                            obj.Lhmg1Fw = data.LHMG1_FW;
                            obj.Ltext1 = data.LTEXT1;
                            obj.Ltext2 = data.LTEXT2;
                            obj.Ltext3 = data.LTEXT3;
                            obj.Ltext4 = data.LTEXT4;
                            obj.Ltext5 = data.LTEXT5;
                            obj.Ltext6 = data.LTEXT6;
                            obj.Magrv = data.MAGRV;
                            obj.Maktx = data.MAKTX;
                            obj.Maktx2 = data.MAKTX2;
                            obj.MaktxCFw = data.MAKTX_C_FW;
                            obj.MengeCFw = data.MENGE_C_FW;
                            obj.MaktxComp = data.MAKTX_COMP;
                            obj.Matkl = data.MATKL;
                            obj.Matnr2 = data.MATNR2;
                            obj.MatnrComp = data.MATNR_COMP;
                            obj.Mhdhb = data.MHDHB;
                            obj.MngPu = data.MNG_PU;
                            obj.MngPuo = data.MNG_PUO;
                            obj.Veran = data.VERAN;
                            obj.Ntgew = data.NTGEW;
                            obj.Normt = data.NORMT;
                            obj.ShelfFw = data.SHELF_FW;
                            obj.Rgmkt = data.RGMKT;
                            obj.Status = data.Status;
                            obj.Pdtype = data.PDTYPE;
                            obj.Plodd = data.PLODD;
                            obj.Plper = data.PLPER;
                            obj.Plqty = data.PLQTY;
                            obj.Slmkt = data.SLMKT;
                            obj.VeranFw = data.VERAN_FW;
                            obj.Psmng = data.PSMNG;
                            obj.PsmngComp = data.PSMNG_COMP;
                            obj.Sortf = data.SORTF;
                            obj.Vhart = data.VHART;
                            obj.Vkbur = data.VKBUR;
                            obj.Vtext = data.VTEXT;
                            obj.Wemng = data.WEMNG;
                            obj.Werks2 = data.WERKS2;
                            obj.Zcylcd = data.ZCYLCD;
                            obj.Zndsc = data.ZNDSC;
                            obj.Fgprie = data.FGPRI;
                            obj.OrderJarClear = data.ORDERJARCLEAR;
                            obj.ETDAT = data.ETDAT;
                            obj.BSTNK = data.BSTNK;
                            obj.VKORG = data.VKORG;
                            //  obj.Opstatus = "U";
                            //  obj.PPstatus = "U";
                            obj.Modify(obj.ID, "SAP_PKGORDGET");
                            await _dal.Update(obj);
                        }

                        var mv = await _materialDal.QueryTwoTables<MKM.Model.Models.MaterialEntity, MaterialVersionEntity, dynamic>(
                       (m, v) => new object[]
                       {
                        JoinType.Inner, v.MaterialId == m.ID
                       },
                       (m, v) => new
                       {
                           v.ID,
                           m.Name,
                           m.Code,
                           mid = m.ID
                       },
                       (m, v) => m.Code == data.MATNR
                       );
                        if (mv.Count > 0)
                        {
                            var existOrder = await _proOrderdal.FindEntity(p => p.ProductionOrderNo == data.AUFNR && p.SapOrderType == "ZXH1");
                            if (existOrder == null)
                            {
                                ProductionOrderEntity prod = new ProductionOrderEntity()
                                {
                                    LineCode = parentArea.EquipmentCode,
                                    SegmentCode = data.ARBPL,
                                    ProductionOrderNo = data.AUFNR,
                                    PlanQty = data.PSMNG,
                                    Type = "WorkOrder",
                                    MaterialVersionId = mv.FirstOrDefault()?.ID,
                                    MaterialId = mv.FirstOrDefault()?.mid,
                                    PlanStartTime = data.GSTRP,
                                    SapOrderType = "ZXH1",
                                    PDType = data.PDTYPE,
                                    SapFormula = data.NORMT,
                                    //  MesOrderCode = "P" + (co + i),
                                    PoStatus = "1",
                                    QaStatus = data.ARBPL.Contains("OVR-PRNT") ? "通过" : "待QA",
                                    OrderType = "C"
                                };
                                prod.CreateCustomGuid("SAP_PKGORDGET");
                                await _proOrderdal.Add(prod);
                            }
                            else
                            {
                                if (existOrder.PoStatus == "1")
                                {
                                    existOrder.PlanQty = data.PSMNG;
                                    existOrder.PlanStartTime = data.GSTRP;
                                    await _proOrderdal.Update(existOrder);
                                }
                            }
                        }
                    }
                    _unitOfWork.CommitTran();
                    // var r = await _lkKESBHelper.PostJson<string, FlagUpdateModel>("SAP_FlagUpdate", new FlagUpdateModel(responsData.Response.dataType, responsData.Response.ids));
                    result.msg = "操作成功";
                    result.success = true;
                }
                catch (Exception ex)
                {
                    result.msg = "操作失败";
                    result.msgDev = ex.Message;
                    _unitOfWork.RollbackTran();
                }
            }
            else
            {
                result.msgDev = responsData.msg;
            }
            return result;
        }

        /// <summary>
        /// 获取SAP包装工单 SAP_PKGORDGET
        /// </summary>  
        /// <returns></returns>
        public async Task<MessageModel<string>> GetSapPackOrderAsync()
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            var responsData = await _lkKESBHelper.PostJsonAsync<ESBBaseModel<string>, string>("SAP_PKGORDGET", "");
            if (responsData.successed == true)
            {
                result.success = true;
                result.msg = "获取SAP包装工单成功,请稍后查询!";
            }
            else
            {
                result.msg = "获取SAP包装工单失败:" + responsData.msg;
            }
            return result;
        }
        /// <summary>
        /// 更新订单状态
        /// </summary>
        /// <param name="orderType">订单类型</param>
        /// <param name="orderNo">订单号</param>
        /// <param name="newStatus">订单状态</param>
        /// <param name="factory">工厂</param>
        /// <returns>返回消息</returns>
        public async Task<MessageModel<string>> SendSapOrderStatus(OrderStatusRequestModel reqModel)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };

            try
            {
                List<ZRFC_S_PPORDSTATUS> changeList = new List<ZRFC_S_PPORDSTATUS>();
                ZRFC_S_PPORDSTATUS os = new ZRFC_S_PPORDSTATUS()
                {
                    PWERK = reqModel.factory, //工厂
                    AUART = reqModel.orderType, //工单类型
                    AUFNR = reqModel.orderNo,  //工单号
                    NEWSTATUS = reqModel.newStatus, // 状态  DLFL/REL/TECO 三选一
                    MATNR = "",
                    GSTRP = "",
                    TYPE = "",
                    MESSAGE = ""
                };
                changeList.Add(os);
                var request = new ZRFC_PP_MES_ORDSTATUS()
                {
                    IT_ITEMS = changeList.ToArray()
                };
                var xmlreq = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ORDSTATUS>(request);
                var data1 = await _lkKESBHelper.PostXMLString("SAP_ORDSTATUS", xmlreq, null);
                var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDSTATUSResponse>(data1.Response);
                if(reponse == null || reponse.IT_ITEMS == null || reponse.IT_ITEMS.Count() ==0)
                {
                    result.msg = $"操作失败！SAP接口无数据返回";
                    return result;
                }
                if (reponse.IT_ITEMS.Count() > 0)
                {
                    var r1 = reponse.IT_ITEMS[0];
                    if (r1.TYPE == "S")
                    {
                        result.msg = "操作成功";
                        result.success = true;
                    }
                    else
                    {
                        result.msg = $"操作失败！错误原因：{r1.MESSAGE}";
                    }
                }
            }
            catch (Exception ex) {
                result.msg = ex.ToString();
            }
            return result;
        }
        /// <summary>
        /// 煮制计划工单创建
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> CreateCookPlanOrder(List<OrderCreateModel> infoList)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            if (infoList.Count == 0)
            {
                result.msg = "无工单信息！";
                return result;
            }
            List<ZPP_MES_ST_PLNORD> olist = new List<ZPP_MES_ST_PLNORD>();
            //            PLWRK = "2010",   // 工厂 2010  C必填
            foreach (var info in infoList)
            {
                var order = new ZPP_MES_ST_PLNORD()
                {
                    PRCTYP = info.Optype,    //"",  // C创建  D 删除  U 修改  必填
                    PAART = info.OrderType,            //"LA",      //订单类型  C必填     LA
                    MATNR = info.MatNo,          //"",    //物料类型  C必填
                    GSMNG = info.PlanQty,     //生产数量 C U 必填
                    MEINS = info.UnitCode,    //生产单位  C必填
                    PSTTR = info.PlanStart,    //开始日期 C U必填
                    PEDTR = info.PlanEnd,     //完成日期 C U必填
                    VERID = info.ProdVersion,     //生产版本 选填 如有必填
                    AUFNR_MES_CK = info.MesOrdePlanNo, //MES煮制工单号  C U D 必填
                    AUFNR_FIL = info.FillOrderNo,   //罐包装工单编号 U必填
                    WEMPF_FIL = info.FillLine,   //煮料工单对应的罐装生产线    U必填
                    PLNUM_CHG = info.Optype == "U" ? info.SapOrderNo : "",  //待修改的计划订单号  U必填
                    PLNUM_DEL = info.Optype == "D" ? info.SapOrderNo : "",    //待删除的计划订单号   D必填                 
                };
                olist.Add(order);
            }
            ZRFC_PP_MES_ASYN_PLNCK request = new ZRFC_PP_MES_ASYN_PLNCK
            {
                MESNUM = Guid.NewGuid().ToString(),
                WERKS = infoList[0].Factory,   // 工厂 2010  C必填
                AUART = "ZXH1",
                ORDTYP = "P",
                IT_PLNORD = olist.ToArray(),
                IT_CKORD = new ZPP_MES_ST_CKORD[1]
            };

            var xmlreq = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ASYN_PLNCK>(request);

            LKKESBRequest lKKESBRequest = new LKKESBRequest
            {
                messageId = "SAP_ASYNCORDCREATE",
                tranNo = GuidGenerater.GetInstance().GenerateGuid(),
                postData = xmlreq.Replace("\\\"", "'")
            };

            SerilogServer.LogDebug("messageId:SAP_ASYNCORDCREATE", "推送计划工单");
            SerilogServer.LogDebug("tranNo:" + lKKESBRequest.tranNo, "推送计划工单");

            SerilogServer.LogDebug("开始 Start", "推送计划工单");
            var data1 = await _lkKESBHelper.PostXMLString(lKKESBRequest, null);
            SerilogServer.LogDebug("结束 Ended data1=" + data1.ToJson(), "推送计划工单");


            SerilogServer.LogDebug($"本次推送计划工单：[{olist.Count}]条"  , "推送计划工单");

            //var xmlreq = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ASYN_PLNCK>(request);
            //var data1 = await _lkKESBHelper.PostXMLString("SAP_ASYNCORDCREATE", xmlreq, null);
            var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ASYN_PLNCKResponse>(data1.Response);
            if (reponse.RET != null)
            {
                // var r1 = reponse.RET[0];
                if (reponse.RET.TYPE == "S")
                {
                    result.msg = "操作成功";
                    result.success = true;
                    SerilogServer.LogDebug($"推送煮制计划工单成功：[{reponse.ToJson()}]条", "推送计划工单");
                }
                else
                {
                    result.msg = $"操作失败！错误原因：{reponse.RET.MESSAGE}";
                    SerilogServer.LogDebug($"推送煮制计划工单失败：[{reponse.ToJson()}]", "推送计划工单");
                }
            }
            return result;
        }

        /// <summary>
        /// 煮制工单创建
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> CreateCookOrder(List<OrderCreateModel> infoList)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            if (infoList.Count == 0)
            {
                result.msg = "无工单信息！";
                return result;
            }
            List<ZPP_MES_ST_CKORD> olist = new List<ZPP_MES_ST_CKORD>();
            //            PLWRK = "2010",   // 工厂 2010  C必填
            foreach (var info in infoList)
            {
                var order = new ZPP_MES_ST_CKORD()
                {
                    PCTYP = info.Optype,  // C创建  D 删除  U 修改  必填
                    AUFNR = info.SapOrderNo,  //煮料工单号  U D必填           
                    AUART = info.OrderType,    //订单类型  C必填   ZC02  ZCS2
                    MATNR = info.MatNo,    //物料编号  C必填
                    PSMNG = info.PlanQty,     //生产数量 C U 必填
                    AMEIN = info.UnitCode,    //生产单位  C必填
                    GSTRP = info.PlanStart,    //开始日期 C U必填
                    GLTRP = info.PlanEnd,     //完成日期 C U必填
                    VERID = info.ProdVersion,     //生产版本 选填 如有C必填
                    ZAFNR_MES_CK = info.MesOrdeNo, //MES煮制工单号  C U D 必填
                    PLNUM = info.MesOrdePlanNo,  //煮料计划订单号 C必填
                    ZAFNR_FIL = info.FillOrderNo,   //灌注包装工单编号 C U必填
                    WEMPF = info.FillLine   //煮料工单对应的罐装生产线   C U必填
                };
                olist.Add(order);
            }
            ZRFC_PP_MES_ASYN_PLNCK request = new ZRFC_PP_MES_ASYN_PLNCK
            {
                MESNUM = Guid.NewGuid().ToString(),
                WERKS = infoList[0].Factory,   // 工厂 2010  C必填
                AUART = "ZXH1",
                ORDTYP = "A",
                IT_CKORD = olist.ToArray(),
                IT_PLNORD = new ZPP_MES_ST_PLNORD[1]
            };
            var xmlreq = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ASYN_PLNCK>(request);

            LKKESBRequest lKKESBRequest = new LKKESBRequest
            {
                messageId = "SAP_ASYNCORDCREATE",
                tranNo = GuidGenerater.GetInstance().GenerateGuid(),
                postData = xmlreq.Replace("\\\"", "'")
            };

            SerilogServer.LogDebug("messageId:SAP_ASYNCORDCREATE" , "煮制工单创建 SAP_ASYNCORDCREATE");
            SerilogServer.LogDebug("tranNo:" + lKKESBRequest.tranNo, "煮制工单创建 SAP_ASYNCORDCREATE");

            SerilogServer.LogDebug("_lkKESBHelper.PostXMLString Start", "煮制工单创建 SAP_ASYNCORDCREATE");
            //var data1 = await _lkKESBHelper.PostXMLString("SAP_ASYNCORDCREATE", xmlreq, null);
            var data1 = await _lkKESBHelper.PostXMLString(lKKESBRequest, null);
            SerilogServer.LogDebug("_lkKESBHelper.PostXMLString Ended", "煮制工单创建 SAP_ASYNCORDCREATE");
            if (data1.successed)
            {
                var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ASYN_PLNCKResponse>(data1.Response);
                if (reponse.RET != null)
                {
                    if (reponse.RET.TYPE == "S")
                    {
                        result.msg = "操作成功";
                        result.success = true;
                        SerilogServer.LogDebug("操作成功：" + reponse.ToJson(), "煮制工单创建 SAP_ASYNCORDCREATE");
                    }
                    else
                    {
                        result.msg = $"操作失败！错误原因：{reponse.RET.MESSAGE}";
                        SerilogServer.LogDebug($"失败：[{reponse.RET.MESSAGE}]"+ reponse.ToJson(), "煮制工单创建 SAP_ASYNCORDCREATE");
                    }
                }
            }
            else
            {
                result.msg = data1.msg;
            }
            return result;
        }
        /// <summary>
        /// 获取SAP 创建煮制计划工单/煮制工单结果数据
        /// </summary>
        /// <param name="reqModel">请求参数</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AsynCKOrderResult(PP_SAP_CKORD_AsynRequest reqModel)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            if (reqModel.ORDTYP == "A" || reqModel.ORDTYP == "P")
            {
                foreach (var item in reqModel.CKORD)
                {
                    if (reqModel.ORDTYP == "P")
                    {
                        var order = await _planOrderdal.FindEntity(p => p.MesOrderNo == item.ZAFNR_MES_CK);
                        if (order != null)
                        {
                            order.SapOrderNo = item.AUFNR;
                            order.SapFlag = item.TYPE == "S" ? 3 : 4;
                            await _planOrderdal.Update(order);
                        }
                    }
                    else
                    {
                        var order = await _proOrderdal.FindEntity(p => p.MesOrderCode == item.ZAFNR_MES_CK);
                        if (order != null)
                        {

                            order.ProductionOrderNo = item.AUFNR;
                            order.SapFlag = item.TYPE == "S" ? 3 : 4;
                            await _proOrderdal.Update(order);

                            if (!string.IsNullOrEmpty(item.BOMLTX))
                            {
                                var bomLongTxt = Enigma.Decrypt(item.BOMLTX, reqModel.MESNum);
                                if (!string.IsNullOrEmpty(bomLongTxt))
                                {
                                    var hashLongTxt = SHAHelper.Sha256(bomLongTxt);
                                    var longData = await _processData.FindEntity(p => p.OrderId == order.ID && p.Type == 0);
                                    if (longData != null)
                                    {
                                        longData.ProcessData = item.BOMLTX;
                                        longData.HashData = hashLongTxt;
                                        longData.Token = reqModel.MESNum;
                                        longData.IsReminded = "1";
                                        longData.Status = "2";
                                        longData.Modify(longData.ID, "AsynCKOrderResult");
                                        await _processData.Update(longData);
                                    }
                                    else
                                    {
                                        longData = new MaterialProcessDataEntity();
                                        longData.ProcessData = item.BOMLTX;
                                        longData.HashData = hashLongTxt;
                                        longData.Token = reqModel.MESNum;
                                        longData.IsReminded = "1";
                                        longData.Status = "2";
                                        longData.VersionId = order.MaterialVersionId;
                                        longData.Type = 0;
                                        longData.OrderId = order.ID;
                                        longData.CreateCustomGuid("AsynCKOrderResult");
                                        await _processData.Add(longData);

                                    }
                                }
                            }
                            if (!string.IsNullOrEmpty(item.BOMALTX))
                            {
                                var bomATX = Enigma.Decrypt(item.BOMALTX, reqModel.MESNum);
                                if (!string.IsNullOrEmpty(bomATX))
                                {
                                    var hashAtx = SHAHelper.Sha256(bomATX);
                                    var atxData = await _processData.FindEntity(p => p.OrderId == order.ID && p.Type == 2);
                                    if (atxData != null)
                                    {
                                        atxData.ProcessData = item.BOMALTX;
                                        atxData.HashData = hashAtx;
                                        atxData.Token = reqModel.MESNum;
                                        atxData.IsReminded = "1";
                                        atxData.Status = "2";
                                        atxData.Modify(atxData.ID, "AsynCKOrderResult");
                                        await _processData.Update(atxData);
                                    }
                                    else
                                    {
                                        atxData = new MaterialProcessDataEntity();
                                        atxData.ProcessData = item.BOMALTX;
                                        atxData.HashData = hashAtx;
                                        atxData.Token = reqModel.MESNum;
                                        atxData.IsReminded = "1";
                                        atxData.Status = "2";
                                        atxData.VersionId = order.MaterialVersionId;
                                        atxData.Type = 2;
                                        atxData.OrderId = order.ID;
                                        atxData.CreateCustomGuid("AsynCKOrderResult");
                                        await _processData.Add(atxData);
                                    }
                                }
                            }

                            //var planOrder = await _planOrderdal.FindList(p => p.SapFormula == order.SapFormula && p.ProduceDate == order.PlanStartTime.Value.Date && p.LineName == order.LineCode);
                            //if (planOrder.Count > 0)
                            //{
                            //    planOrder.ForEach(p =>
                            //    {
                            //        p.SapStatus = "D";
                            //    });
                            //    await _planOrderdal.Update(planOrder);
                            //}
                        }
                    }
                }
                if (reqModel.ORDTYP != "P")
                {
                    var orderNoList = reqModel.CKORD.Select(m => m.AUFNR).ToList();
                    await _orderBomServices.GetSapOrderBom(new SapRequestModel
                    {
                        factory = "2010",
                        orderNo = orderNoList,
                        type = "CK",
                        start = new DateTime(2024, 1, 1),
                        end = new DateTime(2099, 1, 1)
                    });
                    await GetOrderRouting(new SapRequestModel
                    {
                        factory = "2010",
                        orderNo = orderNoList,
                        type = "ZXH2",
                        start = new DateTime(2024, 1, 1),
                        end = new DateTime(2099, 1, 1)
                    });
                }
                result.success = true;
                result.msg = "Receive SAP Data Success！";
            }
            else
            {
                result.msg = "ORDTYP Error！The value must be either A or P ";
            }
            return result;
        }
        /// <summary>
        /// 获取工艺路线
        /// </summary>
        /// <param name="shop">工厂代码</param>
        /// <param name="type">类型</param>
        /// <param name="orderNo">sap工单号</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetOrderRouting(SapRequestModel reqModel)
        {
            var result = new MessageModel<string>
            {
                msg = "操作失败！",
                success = false,
            };
            RANGE_WERKS werks = new RANGE_WERKS
            {
                SIGN = "I",
                OPTION = "EQ",
                LOW = reqModel.factory,
                HIGH = ""
            };
            RANGE_C4 typeRange = new RANGE_C4
            {
                SIGN = "I",
                OPTION = "EQ",
                LOW = reqModel.type,
                HIGH = ""
            };
            RANGE_DATE timeRage = new RANGE_DATE
            {
                SIGN = "I",
                OPTION = "BT",
                LOW = reqModel.start.ToString("yyyy-MM-dd"),
                HIGH = reqModel.end.ToString("yyyy-MM-dd")
            };
            List<RANGE_AUFNR> olist = new List<RANGE_AUFNR>();
            foreach (var o in reqModel.orderNo)
            {
                RANGE_AUFNR obj = new RANGE_AUFNR()
                {
                    SIGN = "I",
                    OPTION = "EQ",
                    LOW = o,
                    HIGH = ""
                };
                olist.Add(obj);
            }

            ZRFC_PP_MES_PP41 request = new ZRFC_PP_MES_PP41
            {
                S_DAUAT = new RANGE_C4[] { typeRange },
                S_WERKS = new RANGE_WERKS[] { werks },
                S_GSTRP = new RANGE_DATE[] { timeRage },
                S_AUFNR = olist.ToArray(),
                IT_PP41 = new ZRPP41[1]
            };

            var xmlreq = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_PP41>(request);
            var data1 = await _lkKESBHelper.PostXMLString("SAP_ROUTINGGET", xmlreq, null);
            if (data1 != null && data1.Response != null)
            {
                var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_PP41Response>(data1.Response);
                if (reponse != null && reponse.IT_PP41.Count() > 0)
                {
                    List<SapPoRoutingEntity> addList = new List<SapPoRoutingEntity>();

                    foreach (var item in reponse.IT_PP41)
                    {
                        var oda = new SapPoRoutingEntity();
                        oda.CreateCustomGuid("SAP_ROUTINGGET");
                        AttributeCopyHelper.CopyAttributesIgnoringCase(item, oda);
                        oda.Vgw01 = item.VGW01;
                        oda.Psmng = item.PSMNG;
                        oda.Bmsch = item.BMSCH;
                        addList.Add(oda);
                    }
                    var aufnrList = reponse.IT_PP41.Select(a => a.AUFNR).Distinct().ToList();
                    if (aufnrList.Count > 0)
                    {
                        await _sapPoRouting.Delete(p => aufnrList.Contains(p.Aufnr));
                    }
                    if (addList.Count > 0)
                    {
                        await _sapPoRouting.Add(addList);
                    }

                    result.success = true;
                    result.msg = "操作成功！";
                }
            }
            else
            {
                result.msg = "调用方法失败";
            }
            return result;
        }


        /// <summary>
        /// 获取物料需求
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SapSegmentMaterialRequirementModel>> GetMaterialRequirementList (SapPackOrderRequestModel reqModel) {

            var data = await _dal.FindList(a =>
                a.Gstrp >= reqModel.StartTime
                && a.Gstrp <= reqModel.EndTime
                && !string.IsNullOrEmpty(a.Arbpl)
                && a.Arbpl.StartsWith("FIL")
                && !string.IsNullOrEmpty(a.MatnrComp)
                && a.PsmngComp > 0);

            if(data.Count == 0)
            {
                return new List<SapSegmentMaterialRequirementModel>();
            }
            /*汇总物料需求*/
            var group1 = data.GroupBy(a => new { a.Gstrp, a.MatnrComp,a.Normt }).Select( g=>
                    new
                    {
                        PlanDate = g.Key.Gstrp,
                        SapFormula = g.Key.Normt,
                        MaterialCode = g.Key.MatnrComp,
                        SumWeight = g.Sum(a=>a.PsmngComp)
                    }
                );

            /*获取物料*/
            var mList = group1.Select(a => a.MaterialCode).Distinct();

            var mAllList = _dal.Db.Queryable<MaterialEntity, MaterialVersionEntity, SapSegmentMaterialEntity, SapSegmentEntity, SapSegmentMaterialStepEntity, MaterialEntity>((m, mv, ssm, ss, ssms, cm)
                => new object[]
                {
                    JoinType.Inner, m.ID == mv.MaterialId,
                    JoinType.Inner, mv.ID == ssm.MaterialVersionId,
                    JoinType.Inner, ssm.SapSegmentId == ss.ID,
                    JoinType.Inner, ssm.ID == ssms.SapSegmentMaterialId,
                    JoinType.Inner, ssms.MaterialId == cm.ID
                })
                .Select((m, mv, ssm, ss, ssms, cm) => new 
                {
                    SapFormula = m.Description,
                    MCode = m.Code,
                    MName = m.NAME,
                    MaterialVersionNumber = mv.MaterialVersionNumber,
                    SapSegmentMaterialId = ssms.SapSegmentMaterialId,
                    SegmentCode = ss.SegmentCode,
                    MaterialCode = cm.Code,
                    MaterialName = cm.NAME,
                    ParentQuantity = ssms.ParentQuantity,
                    StandQty = ssms.Quantity,
                    AdjustPercentQuantity = ssms.AdjustPercentQuantity
                }).ToList();

            var group2 = mAllList.GroupBy(a => new { a.MCode, a.SapSegmentMaterialId })
            .Select(g =>
                new
                {
                    MCode = g.Key.MCode,
                    SapSegmentMaterialId = g.Key.SapSegmentMaterialId,
                    Count = g.Count()
                }
            );
            var group3 = group2.GroupBy(a => new { a.MCode, a.SapSegmentMaterialId })
               .Select(g =>
                   new
                   {
                       MCode = g.Key.MCode,
                       SapSegmentMaterialId = g.Key.SapSegmentMaterialId,
                       MaxCount = g.Max(a => a.Count)
                   }
               );
            List<SapSegmentMaterialRequirementModel> list = new List<SapSegmentMaterialRequirementModel>();
            foreach(var item in group1.OrderBy(a=>a.PlanDate))
            {
                var SapSegmentMaterialId = group3.FirstOrDefault(a => a.MCode == item.MaterialCode)?.SapSegmentMaterialId;
                if (string.IsNullOrEmpty(SapSegmentMaterialId))
                    continue;
                var requirementList = mAllList.Where(a => a.SapSegmentMaterialId == SapSegmentMaterialId).ToList();

                foreach(var comp in requirementList)
                {
                    var model = new SapSegmentMaterialRequirementModel();

                    model.PlanDate = (DateTime)item.PlanDate;
                    model.SapFormula = item.SapFormula;
                    model.MCode = comp.MCode;
                    model.MName = comp.MName;
                    model.MaterialCode = comp.MaterialCode;
                    model.MaterialName = comp.MaterialName;
                    model.FormulaWeight = item.SumWeight;
                    model.ParentQuantity = comp.ParentQuantity;
                    model.AdjustPercentQuantity = comp.AdjustPercentQuantity;
                    model.DayRequirementQty = (int)(item.SumWeight * (comp.AdjustPercentQuantity ?? 0) / comp.ParentQuantity);

                    list.Add(model);
                }
                
            }

            var list2 = list.Where(a=>a.DayRequirementQty > 0).GroupBy(a => new { a.PlanDate, a.MaterialCode, a.MaterialName })
              .Select(g =>
                  new SapSegmentMaterialRequirementModel
                  {
                      PlanDate = g.Key.PlanDate,
                      MaterialCode = g.Key.MaterialCode,
                      MaterialName = g.Key.MaterialName,
                      FormulaWeight = g.Average(a => a.FormulaWeight),
                      DayRequirementQty = g.Sum(a => a.DayRequirementQty)
                  }
              ).OrderBy(a => a.MaterialCode).ThenBy(a => a.PlanDate).ToList();

            return list2;
        }

        
        public async Task<DataTable> GetMaterialRequirementTable (SapPackOrderRequestModel reqModel) {
            var dt = new DataTable();
            foreach (var item in GetTableHeader(reqModel))
            {
                dt.Columns.Add(item);
            }

            var list = await GetMaterialRequirementList(reqModel);
            var mList = list.OrderBy(a => a.MaterialCode).GroupBy(a => new { a.MaterialCode, a.MaterialName}).Select(g=> new
            {
                MaterialCode = g.Key.MaterialCode,
                MaterialName = g.Key.MaterialName
            });
            foreach (var item in mList) 
            {
                DataRow dr = dt.NewRow();
                dr["MaterialCode"] = item.MaterialCode;
                dr["MaterialName"] = item.MaterialName;
                dr["Unit"] = "Kg";
                dr["Total"] = list.Where(a => a.MaterialCode == item.MaterialCode).Sum(a => a.DayRequirementQty);
                for (DateTime date = reqModel.StartTime.Date; date <= reqModel.EndTime.Date; date = date.AddDays(1))
                {
                    dr[date.ToString("yyyy-MM-dd")] = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                }
                dt.Rows.Add(dr);
            }
            return dt;
        }

        public async Task<List<WeekMaterialRequirementExcelDto>> GetMaterialRequirementDto (SapPackOrderRequestModel reqModel) {
            var returnList = new List<WeekMaterialRequirementExcelDto>();
         
            var list = await GetMaterialRequirementList(reqModel);
            var mList = list.OrderBy(a => a.MaterialCode).GroupBy(a => new { a.MaterialCode, a.MaterialName }).Select(g => new
            {
                MaterialCode = g.Key.MaterialCode,
                MaterialName = g.Key.MaterialName
            });

            var mAllList = _dal.Db.Queryable<DFM.Model.Models.EquipmentMaterialEntity, EquipmentEntity, MaterialEntity>((em, e, m)
              => new object[]
              {
                    JoinType.Inner, em.EquipmentId == e.ID,
                    JoinType.Inner, em.MaterialId == m.ID
              })
              .Select((em, e, m) => new
              {
                  EquipmentCode = e.EquipmentCode ,
                  EquipmentName = e.EquipmentName,
                  MaterialID = m.ID,
                  MaterialCode = m.Code,
                  MaterialName = m.NAME,
                  Type = em.Type
                 
              })
              .MergeTable()
              .Where(a=>a.EquipmentName == "原料加工厂" && a.Type == "Include").ToList();


            foreach (var item in mList)
            {
                WeekMaterialRequirementExcelDto model = new WeekMaterialRequirementExcelDto();
                model.MaterialCode = item.MaterialCode;
                model.MaterialName = item.MaterialName;
                if (mAllList.Exists(a => a.MaterialCode == model.MaterialCode))
                {
                    model.MaterialType = "原料加工厂";
                }
                else if (model.MaterialName.Contains("酱油") || model.MaterialName.Contains("醬油"))
                {
                    model.MaterialType = "豉油厂豉水";
                }
                else if (model.MaterialName.Contains("渣"))
                {
                    model.MaterialType = "豉油厂豉渣";
                }

                model.Unit = "Kg";
                model.Total = list.Where(a => a.MaterialCode == item.MaterialCode).Sum(a => a.DayRequirementQty);
                for (DateTime date = reqModel.StartTime.Date; date <= reqModel.EndTime.Date; date = date.AddDays(1))
                {
                    if (date.DayOfWeek == DayOfWeek.Monday)
                    {
                        model.WeekDay1 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if(date.DayOfWeek == DayOfWeek.Tuesday)
                    {
                        model.WeekDay2 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Tuesday)
                    {
                        model.WeekDay2 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Wednesday)
                    {
                        model.WeekDay3 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Thursday)
                    {
                        model.WeekDay4 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Friday)
                    {
                        model.WeekDay5 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Saturday)
                    {
                        model.WeekDay6 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                    else if (date.DayOfWeek == DayOfWeek.Sunday)
                    {
                        model.WeekDay7 = list.Where(a => a.MaterialCode == item.MaterialCode && a.PlanDate == date).FirstOrDefault()?.DayRequirementQty;
                    }
                }
                returnList.Add(model);
            }

            if(!string.IsNullOrEmpty(reqModel.MaterialCode))
            {
                returnList = returnList.Where(a=>a.MaterialCode.Contains(reqModel.MaterialCode)).ToList();
            }
            if (!string.IsNullOrEmpty(reqModel.MaterialName))
            {
                returnList = returnList.Where(a => a.MaterialName.Contains(reqModel.MaterialName)).ToList();
            }
            if (!string.IsNullOrEmpty(reqModel.MaterialType))
            {
                returnList = returnList.Where(a => a.MaterialType != null && a.MaterialType.Contains(reqModel.MaterialType)).ToList();
            }

            return returnList;
        }

        public async Task<SapPoRoutingEntity> GetSapPoRoutingInfo (OrderStatusRequestModel reqModel) {
          
            return (await  _dal.Db.Queryable<SapPoRoutingEntity>().Where(a => a.Aufnr == reqModel.orderNo && a.Arbpl == reqModel.arbpl).ToListAsync()).FirstOrDefault();
        }

        public  List<string> GetTableHeader (SapPackOrderRequestModel reqModel) {
            var list = new List<string> { "MaterialCode", "MaterialName" };
            for (DateTime date = reqModel.StartTime.Date; date <= reqModel.EndTime.Date; date = date.AddDays(1))
            {
                list.Add(date.ToString("yyyy-MM-dd"));
            }
            list.Add("Total");
            list.Add("Unit");
            return list;
        }

    }
}