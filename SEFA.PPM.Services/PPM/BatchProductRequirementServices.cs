
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class BatchProductRequirementServices : BaseServices<BatchProductRequirementEntity>, IBatchProductRequirementServices
    {
        private readonly IBaseRepository<BatchProductRequirementEntity> _dal;
        public BatchProductRequirementServices(IBaseRepository<BatchProductRequirementEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BatchProductRequirementEntity>> GetList(BatchProductRequirementRequestModel reqModel)
        {
            List<BatchProductRequirementEntity> result = new List<BatchProductRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchProductRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchProductRequirementEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<BatchProductRequirementEntity>> GetPageList(BatchProductRequirementRequestModel reqModel)
        {
            PageModel<BatchProductRequirementEntity> result = new PageModel<BatchProductRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchProductRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchProductRequirementEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BatchProductRequirementEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}