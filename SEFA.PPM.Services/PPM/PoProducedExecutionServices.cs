using SEFA.PTM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.IRepository.UnitOfWork;
using System;
using SEFA.Base.Common.HttpContextUser;
using Newtonsoft.Json.Linq;
using SEFA.Base.Common.HttpRestSharp;
using System.Linq;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.IServices.PTM;
using SEFA.Base.ESB;
using SEFA.DFM.Model.Models;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Services;
using SEFA.PPM.Model.Models.PTM;
using LKK.Lib.Core;
using System.Security.Cryptography;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using AutoMapper;
using System.Reflection;
using SEFA.PPM.Model.Models.Interface;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Common.Common;
using SEFA.PTM.Model.Models;
using Abp.Domain.Entities;

namespace SEFA.PTM.Services
{
	public class PoProducedExecutionServices : BaseServices<PoProducedExecutionEntity>, IPoProducedExecutionServices
	{
		private readonly IBaseRepository<PoProducedExecutionEntity> _dal;
		private readonly IBaseRepository<ProductionOrderEntity> _dal2;
		private readonly IBaseRepository<MaterialLotEntity> _dal3;
		private readonly IBaseRepository<PoProducedRequirementEntity> _dal4;
		private readonly IBaseRepository<BatchEntity> _dal5;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _dal6;
		private readonly IBaseRepository<PoEquipmentEntity> _dalpoeq;
		private readonly IBaseRepository<EquipmentEntity> _dal15;
		private readonly IBaseRepository<PoExecutionHistroyViewEntity> _dal36;
		private readonly IBaseRepository<SEFA.PPM.Model.Models.PTM.FunctionPropertyVEntity> _dal37;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IBProductionOrderListViewServices _bProductionOrderListViewServices;
		private readonly IInterfaceServices _iInterfaceServices;
		private readonly IPerformanceServices _performanceServices;
		private readonly IProcessDataViewServices _processDataViewServices;
		private readonly IBaseRepository<MaterialProcessDataEntity> _processDatadal;
		private readonly IBaseRepository<ProcessDataMappingEntity> _processMappingdal;
		private readonly LKKESBHelper _esbHelper;
		private readonly IMapper _mapper;
		private readonly ILogsheetServices _logsheetServices;
		private readonly IAndonServices _andonServices;



		public PoProducedExecutionServices(IBaseRepository<PoProducedExecutionEntity> dal,
			IBaseRepository<ProductionOrderEntity> dal2,
			IBaseRepository<MaterialLotEntity> dal3,
			IBaseRepository<PoProducedRequirementEntity> dal4,
			IBaseRepository<BatchEntity> dal5,
			IBaseRepository<PoSegmentRequirementEntity> dal6,
			IUnitOfWork unitOfWork, IUser user,
			IBProductionOrderListViewServices BProductionOrderListViewServices,
			IInterfaceServices iInterfaceServices,
			IPerformanceServices performanceServices,
			LKKESBHelper esbHelper,
			IProcessDataViewServices processDataViewServices, IBaseRepository<MaterialProcessDataEntity> processDatadal, IBaseRepository<ProcessDataMappingEntity> processMappingdal, IMapper mapper, IBaseRepository<PoEquipmentEntity> dalpoeq, IBaseRepository<EquipmentEntity> dal15, IBaseRepository<PoExecutionHistroyViewEntity> dal36, ILogsheetServices logsheetServices, IBaseRepository<PPM.Model.Models.PTM.FunctionPropertyVEntity> dal37, IAndonServices andonServices)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal3 = dal3;
			this._dal4 = dal4;
			this._dal5 = dal5;
			this._dal6 = dal6;
			this._esbHelper = esbHelper;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;
			_bProductionOrderListViewServices = BProductionOrderListViewServices;
			_iInterfaceServices = iInterfaceServices;
			_performanceServices = performanceServices;
			_processDataViewServices = processDataViewServices;
			_processDatadal = processDatadal;
			_processMappingdal = processMappingdal;
			_mapper = mapper;
			_dalpoeq = dalpoeq;
			_dal15 = dal15;
			_dal36 = dal36;
			_logsheetServices = logsheetServices;
			_dal37 = dal37;
			_andonServices = andonServices;
		}

		public async Task<List<PoProducedExecutionEntity>> GetList(PoProducedExecutionRequestModel reqModel)
		{
			List<PoProducedExecutionEntity> result = new List<PoProducedExecutionEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<PoProducedExecutionEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.RunEquipmentId), a => a.RunEquipmentId.Equals(reqModel.RunEquipmentId))
				.AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
				.ToExpression();
			//result = await _dal.Db.Queryable<PoProducedExecutionEntity>()
			//    .Where(whereExpression).ToListAsync();
			result = await _dal.FindList(whereExpression, reqModel.orderByFileds);
			return result;
		}

		public async Task<PoProducedExecutionEntity> GetOrder(PoProducedExecutionRequestModel reqModel)
		{
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<PoProducedExecutionEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.RunEquipmentId), a => a.RunEquipmentId.Equals(reqModel.RunEquipmentId))
				.AndIF(!string.IsNullOrEmpty(reqModel.Status), a => a.Status.Equals(reqModel.Status))
				.ToExpression();
			var result = await _dal.Db.Queryable<PoProducedExecutionEntity>()
				.Where(whereExpression).FirstAsync();
			return result;
		}

		public async Task<PageModel<PoProducedExecutionEntity>> GetPageList(PoProducedExecutionRequestModel reqModel)
		{
			PageModel<PoProducedExecutionEntity> result = new PageModel<PoProducedExecutionEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<PoProducedExecutionEntity>()
							 .ToExpression();
			var data = await _dal.Db.Queryable<PoProducedExecutionEntity>()
				.Where(whereExpression)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}


		/// <summary>
		/// 发起QA_Proleit ✔
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> InitiateQA(List<MMI_InitiateQA> reqModels)
		{
			SerilogServer.LogDebug($"【发起QA】接口被调用", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "1",
				ReturnMessage = "操作成功！",
				ReturnMessageID = "",
			};
			string err = "";
			foreach (var reqModel in reqModels)
			{
				var equipmentResult = await _iInterfaceServices.GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
				var equipment = equipmentResult.response;
				//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
				if (equipment == null)
				{
					err += equipmentResult.msg;
					//err += $"未找到Equipment{reqModel.EquipmentId}";
					continue;
				}
				var runOrder = await _dal36.FindEntity(x => x.ProcessOrder == reqModel.ProductionNo && x.RunEquipmentId == equipment.ID && x.Number == reqModel.BatchIndex && x.Status == "1");
				if (runOrder == null)
				{
					err += $"设备{reqModel.EquipmentId}未找到运行的工单";
					continue;
				}
				var code = (await _iInterfaceServices.GetFunctionPropertyValue(equipment.ID, "POManagement", "CookInspectionLogsheet")).response;
				if (string.IsNullOrWhiteSpace(code))
				{
					err += $"设备{reqModel.EquipmentId}属性CookInspectionLogsheet为空";
					continue;
				}
				var data = await _logsheetServices.InserNewEntryWithParamter(code, runOrder.ID, 1);
			}
			if (!string.IsNullOrEmpty(err))
			{
				result.ReturnCode = "-1";
				result.ReturnMessage = err;
			}
			SerilogServer.LogDebug($"【发起QA】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 工单控制操作：Start,Resume,Hold,Stop,UpdateOrder
		/// 工单执行状态：1 Running 2 Aborted 3 Stopped 4 Scheduled 5 Hold
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<bool> ConsolPo(ConsolPoRequestModel reqModel)
		{
			_unitOfWork.BeginTran();
			JObject obj = JObject.Parse(reqModel.Body);
			bool result = false;
			var id = obj["ID"].ToString();
			var executionOrder = await QueryById(id);
			switch (reqModel.Key)
			{
				case "Start":
				case "Resume":
					{
						var runningOrder = await GetOrder(new PoProducedExecutionRequestModel() { RunEquipmentId = obj["RunEquipmentId"].ToString(), Status = "1" });
						if (runningOrder != null)
						{
							runningOrder.Status = "3";
							runningOrder.Modify(runningOrder.ID, _user.Name.ToString());
							result = await Update(runningOrder);
						}
						if (result || runningOrder == null)
						{
							if (reqModel.Key == "Start")
							{
								//MaterialLotEntity materialLot = new MaterialLotEntity();
								//materialLot.CreateCustomGuid(_user.Name.ToString());
								//materialLot.LotId = obj["LotId"].ToString();
								//materialLot.MaterialId = obj["MaterialId"].ToString();
								//double exdate = double.Parse(obj["PropertyValue"].ToString());
								//materialLot.ExpirationDate = DateTime.Parse(obj["StartTime"].ToString()).AddDays(exdate);
								//materialLot.ProductionDateLocal = DateTime.Parse(obj["StartTime"].ToString());
								//materialLot.Type = "1";
								//materialLot.ExternalStatus = "2";//默认为解锁
								//materialLot.DispositionId = "1";

								PoProducedExecutionEntity model = new PoProducedExecutionEntity();
								model.CreateCustomGuid(_user.Name.ToString());
								model.LineId = obj["LineId"].ToString();
								model.ProductionOrderId = obj["ProductionOrderId"].ToString();
								model.PoSegmentRequirementId = obj["Id"].ToString();
								model.PoProducedRequirementId = obj["PoProducedRequirementId"].ToString();
								model.BatchId = obj["BatchId"].ToString();
								model.RunEquipmentId = obj["RunEquipmentId"].ToString();
								model.MaterialId = obj["MaterialId"].ToString();
								model.MaterialVersionId = obj["MaterialVersionId"].ToString();
								model.MaterialCode = obj["MaterialCode"].ToString();
								model.MaterialDescription = obj["MaterialDescription"].ToString();
								model.TargetQuantity = long.Parse(obj["Planned"].ToString());
								model.UnitId = obj["UnitId"].ToString();
								model.Status = "1";
								model.Deleted = 0;
								model.StartTime = DateTime.Parse(obj["StartTime"].ToString());
								//model.LotId = materialLot.ID;
								result = await Add(model) > 0;
								if (result)
								{
									var po = await _dal2.FindEntity(executionOrder.ProductionOrderId);
									if (po != null && po.PoStatus == "2")
									{
										po.PoStatus = "6";
										if (po.StartTime == null)
										{
											po.StartTime = model.StartTime;
										}
										po.Modify(po.ID, _user.Name.ToString());
										await _dal2.Update(po);
										//对比工艺长文本
										//await _bProductionOrderListViewServices.CheckLongText(executionOrder.ProductionOrderId);
									}
									//result = await _dal3.Add(materialLot) > 0;
								}
							}
							else
							{
								if (executionOrder != null)
								{
									executionOrder.Status = "1";
									executionOrder.Modify(executionOrder.ID, _user.Name.ToString());
									result = await Update(executionOrder);
								}
							}
						}
						break;
					}
				case "Stop":
				case "Hold":
					{
						if (executionOrder != null)
						{
							if (reqModel.Key == "Stop")
							{
								if (executionOrder.Status == "1")
								{
									executionOrder.Status = "3";
								}
							}
							else
							{
								executionOrder.Status = "5";
							}
							executionOrder.Modify(executionOrder.ID, _user.Name.ToString());
							result = await Update(executionOrder);
						}
						if (reqModel.Key == "Stop" && result)
						{
							var po = await _dal2.FindEntity(executionOrder.ProductionOrderId);
							if (po != null)
							{
								po.PoStatus = "5";
								po.Modify(po.ID, _user.Name.ToString());
								result = await _dal2.Update(po);
							}
							//MessageModel<dynamic> apiResult = await HttpHelper.PostAsync<dynamic>("PPM,", "api/ProductionOrder/UpdateOrderStatus", _user.GetToken(), new { productionOrderId = obj["ProductionOrderId"], status = "50" });
							//result = apiResult.success;
						}
						break;
					}
				case "UpdateOrder":
					if (executionOrder != null)
					{
						//MessageModel<dynamic> apiResult = await HttpHelper.PostAsync<dynamic>("MKM,", "api/MaterialLot/SaveForm", _user.GetToken(), new { ID = EntityBase.GetCustomGuid() });
						MaterialLotEntity model = new MaterialLotEntity();
						model.CreateCustomGuid(_user.Name.ToString());
						model.LotId = obj["LotId"].ToString();
						model.MaterialId = obj["MaterialId"].ToString();
						model.ExpirationDate = DateTime.Parse(obj["ExpirationDate"].ToString());
						model.ProductionDateLocal = DateTime.Parse(obj["ProductionDateLocal"].ToString());
						model.Type = obj["Type"].ToString();
						model.ExternalStatus = obj["ExternalStatus"].ToString();
						model.DispositionId = obj["DispositionId"].ToString();
						result = await _dal3.Add(model) > 0;
						if (result)
						{
							executionOrder.BatchId = model.ID;
							executionOrder.Modify(executionOrder.ID, _user.Name.ToString());
							result = await Update(executionOrder);
						}
					}
					break;
				default:
					break;
			}
			if (!result)
			{
				_unitOfWork.RollbackTran();
				return result;
			}
			_unitOfWork.CommitTran();
			return result;
		}

		public async Task<MessageModel<string>> Start(StartPoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (string.IsNullOrEmpty(reqModel.SegmentId))
			{
				result.msg = "SegmentId不能为空";
				return result;
			}
			if (string.IsNullOrEmpty(reqModel.EquipmentId))
			{
				result.msg = "EquipmentId不能为空";
				return result;
			}
			var productionOrder = await _dal2.FindEntity(reqModel.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			//煮缸/储缸（1：煮缸；2：储缸）
			var functionProperties = await _dal37.FindList(x => x.FunctionCode == "POManagement" && x.PropertyCode == "IsCookTank");
			var functionProperty = functionProperties.Find(x => x.EquipmentId == reqModel.EquipmentId);
			var isCookTank = functionProperty?.PropertyValue ?? functionProperty?.DefaultValue;
			if (isCookTank == "1")
			{
				var poProducedExecutions = await FindList(x => x.ProductionOrderId == reqModel.ProductionOrderId && x.Status == "1");
				if (poProducedExecutions != null)
				{
					foreach (var item in poProducedExecutions)
					{
						var functionProperty_item = functionProperties.Find(x => x.EquipmentId == item.RunEquipmentId);
						var isCookTank_item = functionProperty?.PropertyValue ?? functionProperty?.DefaultValue;
						if (isCookTank_item == "1")
						{
							result.msg = "该工单已在其他煮缸启动，不允许重复启动";
							return result;
						}
					}
				}
			}
			//包装工单
			if (productionOrder.SapOrderType == "ZXH1")
			{
				if (productionOrder.QaStatus != "通过")
				{
					result.msg = "QA未通过，请先进行QA后再启动工单";
					return result;
				}
			}
			//else
			{
				if (reqModel.IsUpdateLtxt)
				{
					//更新工艺长文本
					await CheckProcessDate(reqModel.ProductionOrderId);
				}
			}
			try
			{
				var poSegmentRequirementId = reqModel.PoSegmentRequirementId;
				var poSegmentRequirement = await _dal6.FindEntity(reqModel.PoSegmentRequirementId);
				if (poSegmentRequirement == null)
				{
					result.msg = "未找到poSegmentRequirement";
					return result;
				}
				MessageModel<DFM.Model.Models.SapSegmentEntity> apiResult_sapSegment = await HttpHelper.GetApiAsync<DFM.Model.Models.SapSegmentEntity>("DFM", "api/SapSegment/GetEntity/" + poSegmentRequirement.SegmentId, _user.GetToken(), null);
				var sapSegment = apiResult_sapSegment.response;
				if (sapSegment == null)
				{
					result.msg = "未找到sapSegment";
					return result;
				}
				var poProducedRequirement = (await _dal4.FindList(x => x.ProductionOrderId == reqModel.ProductionOrderId && x.PoSegmentRequirementId == poSegmentRequirementId))?.FirstOrDefault();
				if (poProducedRequirement == null)
				{
					result.msg = "未找到poProducedRequirement";
					return result;
				}
				var batch = await _dal5.FindEntity(reqModel.BatchId);
				if (batch == null)
				{
					result.msg = "未找到batch";
					return result;
				}
				MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + batch.MaterialId, _user.GetToken(), null);
				var material = apiResult_material.response;
				if (material == null)
				{
					result.msg = "未找到material";
					return result;
				}
				var runOrderExs = await _dal.FindList(x => (x.RunEquipmentId == reqModel.EquipmentId/* || x.BatchId == reqModel.BatchId*/) && (x.Status == "1" || x.Status == "5"));
				if (runOrderExs?.Count > 0)
				{
					runOrderExs.ForEach(runOrderEx =>
					{
						_performanceServices.SaveConfirmation(runOrderEx.ID);
						runOrderEx.EndTime = DateTime.Now;
						runOrderEx.Status = "3";
						runOrderEx.Modify(runOrderEx.ID, _user.Name);
					});
					//var equipment = await _dal15.FindEntity(reqModel.EquipmentId);
					//if (equipment != null)
					//{
					//	var productionOrder2 = await _dal2.FindEntity(runOrderExs.FirstOrDefault().ProductionOrderId);
					//	try
					//	{
					//		//触发消耗
					//		var autoReportResult = await _iInterfaceServices.AutoReport("Consume", equipment.EquipmentCode, null);
					//		SerilogServer.LogDebug($"【Start】工单：{productionOrder2.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗结果：{FAJsonConvert.ToJson(autoReportResult)}", "AutoReportLog");
					//	}
					//	catch (Exception ex)
					//	{
					//		SerilogServer.LogDebug($"【Start】工单：{productionOrder2.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗时出现异常：{ex.Message}", "AutoReportLog");
					//	}
					//}
				}
				batch.RunEquipmentId = reqModel.EquipmentId;
				batch.BatchCode = reqModel.LotCode;
				batch.Modify(batch.ID, _user.Name);

				//var value = await _bProductionOrderListViewServices.GetMaterialPropertyValue(batch.MaterialId, "物料有效期属性编码");
				var materialLot = (await _dal3.FindList(x => x.LotId == reqModel.LotCode && x.MaterialId == batch.MaterialId))?.OrderByDescending(x => x.CreateDate).FirstOrDefault();
				bool flag = false;
				if (materialLot == null)
				{
					flag = true;
					materialLot = new();
					materialLot.CreateCustomGuid(_user.Name.ToString());
					materialLot.LotId = reqModel.LotCode;
					materialLot.MaterialId = batch.MaterialId;
					materialLot.ProductionDateLocal = reqModel.ProductionDate;
					//materialLot.ExpirationDate = DateTime.Parse(obj["StartTime"].ToString()).AddDays(double.Parse(value));
					materialLot.ExpirationDate = DateTime.Now.AddDays(30);
					materialLot.Type = "1";
					materialLot.ExternalStatus = "2";//默认为解锁
					materialLot.DispositionId = "1";
				}
				PoProducedExecutionEntity model = new();
				model.CreateCustomGuid(_user.Name.ToString());
				model.SapEquipmentId = sapSegment.SapEquipmentId;
				model.LineId = batch.LineId;
				model.ProductionOrderId = reqModel.ProductionOrderId;
				model.PoSegmentRequirementId = poSegmentRequirementId;
				model.PoProducedRequirementId = poProducedRequirement.ID;
				model.BatchId = batch.ID;
				model.RunEquipmentId = reqModel.EquipmentId;
				model.MaterialId = batch.MaterialId;
				model.MaterialVersionId = batch.MaterialVersionId;
				model.MaterialCode = material.Code;
				model.MaterialDescription = material.Description;
				model.TargetQuantity = batch.TargetQuantity;
				model.UnitId = batch.UnitId;
				model.Status = "1";//RUNNING
				model.Deleted = 0;
				model.StartTime = reqModel.StartTime;
				model.LotId = materialLot.ID;
				model.CrewSize = reqModel.CrewSize;
				productionOrder.PoStatus = "6";
				if (productionOrder.StartTime == null)
				{
					productionOrder.StartTime = model.StartTime;
				}
				productionOrder.Modify(productionOrder.ID, _user.Name);
				List<PoEquipmentEntity> poEs = new List<PoEquipmentEntity>();
				var poeqs = await _dalpoeq.FindList(x => x.OrderId == reqModel.ProductionOrderId);
				var poeq = poeqs.FindAll(x => x.SegmentId == reqModel.SegmentId && x.EquipmentId == reqModel.EquipmentId)?.FirstOrDefault();
				if (poeq == null)
				{
					poeq = new PoEquipmentEntity()
					{
						OrderId = reqModel.ProductionOrderId,
						SegmentId = reqModel.SegmentId,
						EquipmentId = reqModel.EquipmentId
					};
					poeq.CreateCustomGuid(_user.Name);
					poEs.Add(poeq);

					var EquipmentIdList = poeqs.Select(x => x.EquipmentId).ToList();
					EquipmentIdList.Add(poeq.EquipmentId);
					EquipmentIdList = EquipmentIdList.Distinct().ToList();
					MessageModel<string> apiResult = await HttpHelper.PostAsync<string>("DFM", "api/RecipeCommon/BindPoRecipe", _user.GetToken(), new { ProductionOrderId = productionOrder.ID, MaterialVersionId = productionOrder.MaterialVersionId, ProductionTime = productionOrder.PlanStartTime, EquipmentIdList = EquipmentIdList });
					SerilogServer.LogDebug($"[{productionOrder.ProductionOrderNo}]绑定配方数据完成", "StartOrderLog");
				}
				_unitOfWork.BeginTran();
				try
				{
					await _dal5.Update(batch);
					await _dal2.Update(productionOrder);
					if (flag)
					{
						await _dal3.Add(materialLot);
					}
					if (runOrderExs?.Count > 0)
					{
						await Update(runOrderExs);
					}
					await Add(model);
					if (poEs.Count > 0)
					{
						await _dalpoeq.Add(poEs);
					}
					_unitOfWork.CommitTran();
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
				//包装工单
				if (productionOrder.SapOrderType != "ZXH2")
				{
					string reasonId = await GetReason(model.RunEquipmentId);
					var r1 = await _performanceServices.SaveStartOrStopEvent(new DowntimeEntity() { CrewSize = reqModel.CrewSize, OrderId = model.ProductionOrderId, PoExecutionId = model.ID, EquipmentId = model.RunEquipmentId, ReasonId = reasonId });
				}
				else
				//制造工单
				if (productionOrder.SapOrderType == "ZXH2")
				{
					//发送给MMI
					//var re = await _iInterfaceServices.POStart(model.ID, reqModel.BatchId, reqModel.EquipmentId, 1, poEs?.Count > 0 ? true : false);
					//if (!re.success)
					//{
					//	result.msgDev = re.response?.ReturnMessage;
					//	//return result;
					//}
				}
				try
				{
					//发送给Colos系统
					//await _iInterfaceServices.SendWorkOrderInfoToColos(reqModel.EquipmentId, reqModel.ProductionOrderId);
					//更新数据字典自动消耗点位最新值
					//await _iInterfaceServices.UpdateAutoReportDataItemData(reqModel.EquipmentId);
					//发送给ANDON系统
					//await _andonServices.CheckNextOrderMaterialPrep(model.ID);
				}
				catch (Exception ex)
				{

				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<string> GetReason(string equipmentId)
		{
			return (await _iInterfaceServices.GetFunctionPropertyValue(equipmentId, "PerformanceEvents", "POStartStoppage"))?.response == "True" ? "生产前准备" : "运行中";
		}

		/// <summary>
		/// 获取煮制工单长文本
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<MaterialProcessDataEntity>>> GetCookOrderLtexts(UpdateQaStatusRequestModel reqModel)
		{
			var result = new MessageModel<List<MaterialProcessDataEntity>>
			{
				msg = "获取失败！",
				success = true,
			};
			ProductionOrderEntity entity = await _dal2.FindEntity(reqModel.Id);
			if (entity == null)
			{
				result.msg = "PoId不存在";
				return result;
			}
			var list = new List<MaterialProcessDataEntity>();
			//煮制长文本
			var ltext1 = await _processDatadal.FindEntity(x => x.Status == "2" && x.OrderId == reqModel.Id && x.Type == 0);
			if (ltext1 == null)
			{
				result.msg = "未找到工单煮制长文本";
				return result;
			}
			try
			{
				ltext1.ProcessData = Enigma.Decrypt(ltext1.ProcessData, ltext1.Token)?.Replace("@@", "\n");
			}
			catch (Exception ex)
			{
                result.msg = "解码工单煮制长文本异常：" +ex.Message;
                return result;
            }
			list.Add(ltext1);
			var contextVersionId = (await _processDataViewServices.GetContextVersionId(reqModel.Id)).response;
			MaterialProcessDataEntity ltext2 = new MaterialProcessDataEntity();
			if (!string.IsNullOrEmpty(contextVersionId))
			{
				//煮制长文本 根据pv
				var processData2 = await _processDataViewServices.GetLastProcessDataByContextVersion(contextVersionId);
				if (processData2.success)
				{
					ltext2 = processData2.response;
				}
				else
				{
					result.msg = processData2.msg;
					//return result;
				}
				if (ltext2 == null)
				{
					result.msg = "根据pv未找到煮制长文本";
					//return result;
				}
				else
				{
					ltext2.ProcessData = ltext2.ProcessData?.Replace("@@", "\n");
				}
				//ltext2.ProcessData = Enigma.Decrypt(ltext2.ProcessData, ltext2.Token);
			}
			else
			{
				result.msg = "工单未绑定配方数据";
			}
			list.Add(ltext2);
			result.response = list;
			result.success = true;
			
			if (ltext1?.HashData != ltext2?.HashData)
			{
				result.msg = "长文本比对不一致！";
			}
			else
			{
                result.msg = "长文本比对通过！";
            }
			return result;
		}

		public async Task<MessageModel<string>> CheckProcessDate(string PoId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var contextVersionId = (await _processDataViewServices.GetContextVersionId(PoId)).response;
			if (string.IsNullOrEmpty(contextVersionId))
			{
				result.msg = "未获取到contextVersionId";
				return result;
			}
			var messageModel = (await GetCookOrderLtexts(new UpdateQaStatusRequestModel() { Id = PoId }));
			if (messageModel.success && messageModel.msg == "长文本不一致！")
			{
				var list = messageModel.response;
				var ltext1 = list[0];
				var ltext2 = list[1];
				ltext1.ProcessData = ltext1.ProcessData?.Replace("\n", "@@");
				ltext2.ProcessData = ltext2.ProcessData?.Replace("\n", "@@");
				ltext1.ProcessData = Enigma.Encrypt(ltext1.ProcessData, ltext1.Token);
				int versionNumber = 0;
				if (!string.IsNullOrEmpty(ltext2.ID))
				{
					ltext2.ProcessData = Enigma.Encrypt(ltext2.ProcessData, ltext2.Token);
					ltext2.Status = "1";
					ltext2.Modify(ltext2.ID, _user.Name);
					versionNumber = ltext2.TextVersion;
				}
				versionNumber++;
				MaterialProcessDataEntity pde = new MaterialProcessDataEntity()
				{
					VersionId = ltext1.VersionId,
					ProcessData = ltext1.ProcessData,
					HashData = ltext1.HashData,
					TextVersion = versionNumber,
					IsReminded = "1",
					Status = "2",
					Token = ltext1.Token,
					Type = ltext1.Type,
				};
				pde.CreateCustomGuid(_user.Name);
				ProcessDataMappingEntity pdme = new()
				{
					VersionId = contextVersionId,
					ProcessData = pde.ID
				};
				pdme.CreateCustomGuid(_user.Name);
				_unitOfWork.BeginTran();
				try
				{
					if (!string.IsNullOrEmpty(ltext2.ID))
					{
						await _processDatadal.Update(ltext2);
					}
					await _processDatadal.Add(pde);
					await _processMappingdal.Add(pdme);
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
				_unitOfWork.CommitTran();
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;


		}
		public async Task<MessageModel<string>> Stop(StopPoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poProducedExecution = await QueryById(reqModel.ExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poProducedExecution";
				return result;
			}
			try
			{
				var productionOrder = await _dal2.FindEntity(poProducedExecution.ProductionOrderId);
				if (productionOrder == null)
				{
					result.msg = "未找到productionOrder";
					return result;
				}
				if (reqModel.EndTime < poProducedExecution.StartTime)
				{
					result.msg = "结束时间不能早于开始时间！";
					return result;
				}
				poProducedExecution.Status = "3";//STOPPED
				poProducedExecution.EndTime = reqModel.EndTime;
				poProducedExecution.Modify(poProducedExecution.ID, _user.Name);
				//var equipment = await _dal15.FindEntity(poProducedExecution.RunEquipmentId);
				//if (equipment != null)
				//{
				//	try
				//	{
				//		//触发消耗
				//		var autoReportResult = await _iInterfaceServices.AutoReport("Consume", equipment.EquipmentCode, null);
				//		SerilogServer.LogDebug($"【Stop】工单：{productionOrder.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗结果：{FAJsonConvert.ToJson(autoReportResult)}", "AutoReportLog");
				//	}
				//	catch (Exception ex)
				//	{
				//		SerilogServer.LogDebug($"【Stop】工单：{productionOrder.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗时出现异常：{ex.Message}", "AutoReportLog");
				//	}
				//}
				if (reqModel.IsComplete)
				{
					if (productionOrder.SapOrderType != "ZXH2")
					{
						if (!string.IsNullOrEmpty(reqModel.ProduceStatus))
						{
							productionOrder.ProduceStatus = reqModel.ProduceStatus;
						}
						if (!string.IsNullOrEmpty(reqModel.Reason))
						{
							productionOrder.Reason = reqModel.Reason;
						}
					}
					productionOrder.PoStatus = "3";//COMPLETE
					if (productionOrder.EndTime == null)
					{
						productionOrder.EndTime = poProducedExecution.EndTime;
					}
					productionOrder.Modify(productionOrder.ID, _user.Name);
				}
				_unitOfWork.BeginTran();
				try
				{
					await Update(poProducedExecution);
					await _dal2.Update(productionOrder);
					_unitOfWork.CommitTran();
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
				if (productionOrder.SapOrderType != "ZXH2")
				{
					var r1 = await _performanceServices.SaveStartOrStopEvent(new DowntimeEntity() {StartTimeUtc = poProducedExecution.EndTime, OrderId = poProducedExecution.ProductionOrderId, PoExecutionId = poProducedExecution.ID, EquipmentId = poProducedExecution.RunEquipmentId, ReasonId = "无生产" });
				}
				else //制造工单
				if (productionOrder.SapOrderType == "ZXH2")
				{
					//发送给MMI
					var re = await _iInterfaceServices.POStart(poProducedExecution.ID, poProducedExecution.BatchId, poProducedExecution.RunEquipmentId, 0);
					if (!re.success)
					{
						result.msgDev = re.response?.ReturnMessage;
						//return result;
					}
				}
				var r = await _performanceServices.SaveConfirmation(poProducedExecution.ID);
				//获取能耗
				await _iInterfaceServices.GetEnergyDataByOrder(poProducedExecution);
				if (productionOrder.SapOrderType == "ZXH2")
				{
					var rc = await _logsheetServices.CheckLogsheet(poProducedExecution);
					if (rc != null && rc.success == true)
					{
						result.msg = rc.msg;
						result.success = true;
						return result;
					}
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> Hold(StopPoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			try
			{
				var poProducedExecution = await QueryById(reqModel.ExecutionId);
				if (poProducedExecution == null)
				{
					result.msg = "未找到poProducedExecution";
					return result;
				}
				var productionOrder = await _dal2.FindEntity(poProducedExecution.ProductionOrderId);
				if (productionOrder == null)
				{
					result.msg = "未找到productionOrder";
					return result;
				}
				//if (reqModel.EndTime < poProducedExecution.StartTime)
				//{
				//	result.msg = "结束时间不能早于开始时间！";
				//	return result;
				//}
				poProducedExecution.Status = "5";//HOLD
				poProducedExecution.EndTime = reqModel.EndTime;
				poProducedExecution.Modify(poProducedExecution.ID, _user.Name);
				if (reqModel.IsComplete)
				{
					productionOrder.PoStatus = "3";//COMPLETE
					if (productionOrder.EndTime == null)
					{
						productionOrder.EndTime = poProducedExecution.EndTime;
					}
					productionOrder.Modify(productionOrder.ID, _user.Name);
				}
				_unitOfWork.BeginTran();
				try
				{
					await Update(poProducedExecution);
					await _dal2.Update(productionOrder);
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
				_unitOfWork.CommitTran();
				//发送给MMI
				//await _iInterfaceServices.POStart(poProducedExecution.BatchId, 2);
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> Resume(ResumePoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			PoProducedExecutionEntity poProducedExecution;
			try
			{
				poProducedExecution = await QueryById(reqModel.ExecutionId);
				if (poProducedExecution == null)
				{
					result.msg = "未找到poProducedExecution";
					return result;
				}
				poProducedExecution.Status = "3";//STOPPED
				poProducedExecution.EndTime = DateTime.Now;
				poProducedExecution.Modify(poProducedExecution.ID, _user.Name);
				var productionOrder = await _dal2.FindEntity(reqModel.ProductionOrderId);
				if (productionOrder == null)
				{
					result.msg = "未找到productionOrder";
					return result;
				}
				var poSegmentRequirementId = reqModel.PoSegmentRequirementId;
				var poSegmentRequirement = await _dal6.FindEntity(reqModel.PoSegmentRequirementId);
				if (poSegmentRequirement == null)
				{
					result.msg = "未找到poSegmentRequirement";
					return result;
				}
				MessageModel<DFM.Model.Models.SapSegmentEntity> apiResult_sapSegment = await HttpHelper.GetApiAsync<DFM.Model.Models.SapSegmentEntity>("DFM", "api/SapSegment/GetEntity/" + poSegmentRequirement.SegmentId, _user.GetToken(), null);
				var sapSegment = apiResult_sapSegment.response;
				if (sapSegment == null)
				{
					result.msg = "未找到sapSegment";
					return result;
				}
				var poProducedRequirement = await _dal4.FindEntity(x => x.ProductionOrderId == reqModel.ProductionOrderId && x.PoSegmentRequirementId == poSegmentRequirementId);
				if (poProducedRequirement == null)
				{
					result.msg = "未找到poProducedRequirement";
					return result;
				}
				var batch = await _dal5.FindEntity(reqModel.BatchId);
				if (batch == null)
				{
					result.msg = "未找到batch";
					return result;
				}
				MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + batch.MaterialId, _user.GetToken(), null);
				var material = apiResult_material.response;
				if (material == null)
				{
					result.msg = "未找到material";
					return result;
				}
				batch.BatchCode = reqModel.LotCode;
				batch.Modify(batch.ID, _user.Name);
				var materialLot = (await _dal3.FindList(x => x.LotId == reqModel.LotCode && x.MaterialId == batch.MaterialId))?.OrderByDescending(x => x.CreateDate).FirstOrDefault();
				bool flag = false;
				//var value = await _bProductionOrderListViewServices.GetMaterialPropertyValue(batch.MaterialId, "物料有效期属性编码");
				if (materialLot == null)
				{
					flag = true;
					materialLot = new();
					materialLot.CreateCustomGuid(_user.Name.ToString());
					materialLot.LotId = reqModel.LotCode;
					materialLot.MaterialId = batch.MaterialId;
					materialLot.ProductionDateLocal = reqModel.StartTime;
					//materialLot.ExpirationDate = DateTime.Parse(obj["StartTime"].ToString()).AddDays(double.Parse(value));
					materialLot.ExpirationDate = reqModel.ExpirationDate.Value;
					materialLot.Type = "1";
					materialLot.ExternalStatus = "2";//默认为解锁
					materialLot.DispositionId = "1";
				}

				PoProducedExecutionEntity model = new();
				model.CreateCustomGuid(_user.Name.ToString());
				model.SapEquipmentId = sapSegment.SapEquipmentId;
				model.LineId = batch.LineId;
				model.ProductionOrderId = reqModel.ProductionOrderId;
				model.PoSegmentRequirementId = poSegmentRequirementId;
				model.PoProducedRequirementId = poProducedRequirement.ID;
				model.BatchId = batch.ID;
				model.RunEquipmentId = reqModel.EquipmentId;
				model.MaterialId = batch.MaterialId;
				model.MaterialVersionId = batch.MaterialVersionId;
				model.MaterialCode = material.Code;
				model.MaterialDescription = material.Description;
				model.TargetQuantity = batch.TargetQuantity;
				model.UnitId = batch.UnitId;
				model.Status = "1";//RUNNING
				model.Deleted = 0;
				model.StartTime = reqModel.StartTime;
				model.LotId = materialLot.ID;
				_unitOfWork.BeginTran();
				try
				{
					await Update(poProducedExecution);
					await _dal5.Update(batch);
					if (flag)
					{
						await _dal3.Add(materialLot);
					}
					await Add(model);
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			await _performanceServices.SaveConfirmation(reqModel.ExecutionId);
			try
			{
				//发送给Colos系统
				await _iInterfaceServices.SendWorkOrderInfoToColos(reqModel.EquipmentId, reqModel.ProductionOrderId);
			}
			catch (Exception ex)
			{

			}
			//获取能耗
			await _iInterfaceServices.GetEnergyDataByOrder(poProducedExecution);
			//发送给MMI
			await _iInterfaceServices.POStart(poProducedExecution.ID, poProducedExecution.BatchId, poProducedExecution.RunEquipmentId, 0);
			await _andonServices.CheckNextOrderMaterialPrep(poProducedExecution.ID);
			await _iInterfaceServices.UpdateAutoReportDataItemData(poProducedExecution.RunEquipmentId);
			//发送给MMI
			await _iInterfaceServices.POStart(reqModel.ExecutionId, reqModel.BatchId, reqModel.EquipmentId, 1);
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> UpdatePo(ResumePoRequestModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poProducedExecution = await QueryById(reqModel.ExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poProducedExecution";
				return result;
			}
			PoProducedExecutionEntity model = new();
			var productionOrder = await _dal2.FindEntity(poProducedExecution.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			try
			{
				var batch = await _dal5.FindEntity(poProducedExecution.BatchId);
				if (batch == null)
				{
					result.msg = "未找到batch";
					return result;
				}
				MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + batch.MaterialId, _user.GetToken(), null);
				var material = apiResult_material.response;
				if (material == null)
				{
					result.msg = "未找到material";
					return result;
				}
				batch.BatchCode = reqModel.LotCode;
				batch.Modify(batch.ID, _user.Name);

				var materialLot = (await _dal3.FindList(x => x.LotId == reqModel.LotCode && x.MaterialId == batch.MaterialId))?.OrderByDescending(x => x.CreateDate).FirstOrDefault();
				bool flag = false;
				//var value = await _bProductionOrderListViewServices.GetMaterialPropertyValue(batch.MaterialId, "物料有效期属性编码");
				if (materialLot == null)
				{
					flag = true;
					materialLot = new();
					materialLot.CreateCustomGuid(_user.Name.ToString());
					materialLot.LotId = reqModel.LotCode;
					materialLot.MaterialId = batch.MaterialId;
					materialLot.ProductionDateLocal = reqModel.ProductionDate;
					//materialLot.ExpirationDate = DateTime.Parse(obj["StartTime"].ToString()).AddDays(double.Parse(value));
					materialLot.ExpirationDate = reqModel.ExpirationDate.Value;
					materialLot.Type = "1";
					materialLot.ExternalStatus = "2";//默认为解锁
					materialLot.DispositionId = "1";
				}

				model.CreateCustomGuid(_user.Name.ToString());
				model.SapEquipmentId = poProducedExecution.SapEquipmentId;
				model.LineId = batch.LineId;
				model.ProductionOrderId = poProducedExecution.ProductionOrderId;
				model.PoSegmentRequirementId = poProducedExecution.PoSegmentRequirementId;
				model.PoProducedRequirementId = poProducedExecution.PoProducedRequirementId;
				model.BatchId = batch.ID;
				model.RunEquipmentId = poProducedExecution.RunEquipmentId;
				model.MaterialId = batch.MaterialId;
				model.MaterialVersionId = batch.MaterialVersionId;
				model.MaterialCode = material.Code;
				model.MaterialDescription = material.Description;
				model.TargetQuantity = batch.TargetQuantity;
				model.UnitId = batch.UnitId;
				model.Status = "1";//RUNNING
				model.Deleted = 0;
				model.StartTime = reqModel.StartTime != null ? reqModel.StartTime : DateTime.Now;
				model.LotId = materialLot.ID;
				model.CrewSize = poProducedExecution.CrewSize;
				model.Comments = reqModel.Comments;

				//poProducedExecution.StartTime = reqModel.ProductionDate;
				//poProducedExecution.Comments = reqModel.Comments;
				//poProducedExecution.LotId = materialLot.ID;
				poProducedExecution.EndTime = model.StartTime.Value.AddSeconds(-1);
				poProducedExecution.Status = "3";
				poProducedExecution.Modify(poProducedExecution.ID, _user.Name);
				_unitOfWork.BeginTran();
				try
				{
					//model.CreateDate = model.CreateDate.AddSeconds(5);
					await Update(poProducedExecution);
					await Add(model);
					await _dal5.Update(batch);
					if (flag)
					{
						await _dal3.Add(materialLot);
					}
					_unitOfWork.CommitTran();
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			if (productionOrder.SapOrderType != "ZXH2")
			{
				//var r1 = await _performanceServices.SaveStartOrStopEvent(new DowntimeEntity() { OrderId = poProducedExecution.ProductionOrderId, PoExecutionId = poProducedExecution.ID, EquipmentId = poProducedExecution.RunEquipmentId, ReasonId = "无生产" });
				string reasonId = await GetReason(model.RunEquipmentId);
				var r2 = await _performanceServices.SaveStartOrStopEvent(new DowntimeEntity() { CrewSize = model.CrewSize, OrderId = model.ProductionOrderId, PoExecutionId = model.ID, EquipmentId = model.RunEquipmentId, ReasonId = reasonId });
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> UpdateOrderRemark(ProductionOrderEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var productionOrder = await _dal2.FindEntity(reqModel.ID);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			productionOrder.Remark = reqModel.Remark;
			productionOrder.Modify(productionOrder.ID, _user.Name);
			_unitOfWork.BeginTran();
			try
			{
				await _dal2.Update(productionOrder);
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> StartNextBatch(ProcessOrderViewEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poProducedExecution = await QueryById(reqModel.ExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poProducedExecution";
				return result;
			}
			if (poProducedExecution.Status != "1")
			{
				result.msg = "当前执行记录状态已不是运行中，请刷新界面选取最新激活工单进行操作";
				return result;
			}
			PoProducedExecutionEntity model = new();
			var productionOrder = await _dal2.FindEntity(poProducedExecution.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			try
			{
				var nextBatchNumber = (int.Parse(reqModel.Number) + 1).ToString();
				var batch = await _dal5.FindEntity(x => x.PoSegmentRequirementId == poProducedExecution.PoSegmentRequirementId && x.Number == reqModel.Number);
				if (batch == null)
				{
					result.msg = "未找到batch";
					return result;
				}
				var nextBatch = await _dal5.FindEntity(x => x.PoSegmentRequirementId == poProducedExecution.PoSegmentRequirementId && x.Number == nextBatchNumber);
				if (nextBatch == null)
				{
					result.msg = "未找到nextBatch";
					return result;
				}
				MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + nextBatch.MaterialId, _user.GetToken(), null);
				var material = apiResult_material.response;
				if (material == null)
				{
					result.msg = "未找到material";
					return result;
				}
				nextBatch.RunEquipmentId = reqModel.EquipmentId;
				nextBatch.BatchCode = batch.BatchCode;
				nextBatch.Modify(nextBatch.ID, _user.Name);

				//var value = await _bProductionOrderListViewServices.GetMaterialPropertyValue(batch.MaterialId, "物料有效期属性编码");
				//MaterialLotEntity materialLot = new();
				//materialLot.CreateCustomGuid(_user.Name.ToString());
				//materialLot.LotId = reqModel.LotCode;
				//materialLot.MaterialId = batch.MaterialId;
				//materialLot.ProductionDateLocal = reqModel.ProductionDate;
				////materialLot.ExpirationDate = DateTime.Parse(obj["StartTime"].ToString()).AddDays(double.Parse(value));
				//materialLot.ExpirationDate = reqModel.ExpirationDate.Value;
				//materialLot.Type = "1";
				//materialLot.ExternalStatus = "2";//默认为解锁
				//materialLot.DispositionId = "1";

				model.CreateCustomGuid(_user.Name.ToString());
				model.SapEquipmentId = poProducedExecution.SapEquipmentId;
				model.LineId = nextBatch.LineId;
				model.ProductionOrderId = poProducedExecution.ProductionOrderId;
				model.PoSegmentRequirementId = poProducedExecution.PoSegmentRequirementId;
				model.PoProducedRequirementId = poProducedExecution.PoProducedRequirementId;
				model.BatchId = nextBatch.ID;
				model.RunEquipmentId = poProducedExecution.RunEquipmentId;
				model.MaterialId = nextBatch.MaterialId;
				model.MaterialVersionId = nextBatch.MaterialVersionId;
				model.MaterialCode = material.Code;
				model.MaterialDescription = material.Description;
				model.TargetQuantity = nextBatch.TargetQuantity;
				model.UnitId = nextBatch.UnitId;
				model.Status = "1";//RUNNING
				model.Deleted = 0;
				model.StartTime = DateTime.Now;
				model.LotId = poProducedExecution.LotId;
				model.CrewSize = poProducedExecution.CrewSize;
				model.Comments = "";

				//poProducedExecution.StartTime = reqModel.ProductionDate;
				//poProducedExecution.Comments = reqModel.Comments;
				//poProducedExecution.LotId = materialLot.ID;
				poProducedExecution.EndTime = model.StartTime.Value.AddSeconds(-1);
				poProducedExecution.Status = "3";
				poProducedExecution.Modify(poProducedExecution.ID, _user.Name);
				//var equipment = await _dal15.FindEntity(poProducedExecution.RunEquipmentId);
				//if (equipment != null)
				//{
				//	try
				//	{
				//		//触发消耗
				//		var autoReportResult = await _iInterfaceServices.AutoReport("Consume", equipment.EquipmentCode, null);
				//		SerilogServer.LogDebug($"【StartNextBatch】工单：{productionOrder.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗结果：{FAJsonConvert.ToJson(autoReportResult)}", "AutoReportLog");
				//	}
				//	catch (Exception ex)
				//	{
				//		SerilogServer.LogDebug($"【StartNextBatch】工单：{productionOrder.ProductionOrderNo}在设备：{equipment.EquipmentCode}停止自动消耗时出现异常：{ex.Message}", "AutoReportLog");
				//	}
				//}
				_unitOfWork.BeginTran();
				try
				{
					//model.CreateDate = model.CreateDate.AddSeconds(5);
					await Update(poProducedExecution);
					await Add(model);
					await _dal5.Update(nextBatch);
					//await _dal3.Add(materialLot);
					_unitOfWork.CommitTran();
				}
				catch (Exception e)
				{
					_unitOfWork.RollbackTran();
					result.msg = e.Message;
					return result;
				}
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			if (productionOrder.SapOrderType != "ZXH2")
			{
				string reasonId = await GetReason(model.RunEquipmentId);
				var r2 = await _performanceServices.SaveStartOrStopEvent(new DowntimeEntity() { CrewSize = model.CrewSize, OrderId = model.ProductionOrderId, PoExecutionId = model.ID, EquipmentId = model.RunEquipmentId, ReasonId = reasonId });
			}
			else
			{
				var r = await _logsheetServices.CheckLogsheet(poProducedExecution);
				if (r != null && r.success == true)
				{
					result.msg = r.msg;
					result.success = true;
					return result;
				}
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> GetBatchCodeByProLine(GetBatchCodeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = true
			};

			try
			{
				string plant = "3";//Y
				string lineCode = reqModel.LotCode;//A
				if (lineCode.Length == 2)
				{
					plant = lineCode.Substring(0, 1);
					lineCode = lineCode.Substring(1, 1);
				}
				else
				{
					result.msg = "获取失败，长度超过2";
					return result;
				}
				try
				{
					//result.response = "123";
					//result.msg = "获取成功！";
					//result.success = true;
					//return result;

					reqModel.productionDate = reqModel.productionDate;
					var bgsResponse = await _esbHelper.PostXMLString("BGS00001", InterfaceHelper.GetBgsRequestXML(plant, lineCode, reqModel.productionDate.ToString("yyyyMMdd")));
					if (bgsResponse.successed != true)
					{

						result.msg = bgsResponse.msg;
						return result;
					}
					var batchResponse = InterfaceHelper.ParseFromBgsResponseXml<BGS.GetBatchResponse>(bgsResponse.Response);
					if (!batchResponse.GetBatchResult.IsSuccess)
					{
						result.msg = batchResponse.GetBatchResult.Message;
						return result;
					}
					result.response = batchResponse.GetBatchResult.Batch;
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
					result.response = string.Empty;
					return result;
				}
				//result.response = "";
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				result.response = string.Empty;
				return result;
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> GetBatchCodeByProLineold(GetBatchCodeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = true
			};
			if (string.IsNullOrEmpty(reqModel.productionId))
			{
				result.msg = "productionId不能为空";
				return result;
			}
			try
			{
				//获取上层列表
				MessageModel<List<EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", "api/Equipment/GetProductLine", _user.GetToken(), new { equipmentCode = reqModel.equipmentCode, level = "Line" });
				var equipments = apiResult_Equipments.response;
				if (equipments == null || equipments.Count == 0)
				{
					result.msg = "未找到Line";
					return result;
				}
				var line = equipments.FirstOrDefault();
				if (line == null)
				{
					result.msg = "未找到Line";
					return result;
				}
				var allCode = string.Empty;

				MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys = await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM", "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _user.GetToken(), new { EquipmentId = line.ID });
				var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
				if (equFunctionPropertys?.Count > 0)
				{
					foreach (var item in equFunctionPropertys)
					{
						var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "CounterfeitLineCode");
						if (pr != null)
						{
							allCode = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
						}
						if (!string.IsNullOrEmpty(allCode))
						{
							break;
						}
					}
				}
				if (allCode == null)
				{
					result.msg = "未找到LineCode";
					return result;
				}
				if (allCode.Length < 2)
				{
					result.msg = "LineCode位数不足两位";
					return result;
				}
				string plant = allCode[0].ToString();//Y
				string lineCode = allCode[1].ToString();//A
				try
				{
					var productionOrder = await _dal2.FindEntity(reqModel.productionId);
					if (productionOrder == null)
					{
						result.msg = "未找到productionOrder";
						return result;
					}
					///煮制工单
					if (productionOrder.SapOrderType == "ZXH2")
					{
						reqModel.productionDate = productionOrder.PlanStartTime.Value;
					}
					var bgsResponse = await _esbHelper.PostXMLString("BGS00001", InterfaceHelper.GetBgsRequestXML(plant, lineCode, reqModel.productionDate.ToString("yyyyMMdd")));
					if (bgsResponse.successed != true)
					{
						result.msg = bgsResponse.msg;
						return result;
					}
					var batchResponse = InterfaceHelper.ParseFromBgsResponseXml<BGS.GetBatchResponse>(bgsResponse.Response);
					if (!batchResponse.GetBatchResult.IsSuccess)
					{
						result.msg = batchResponse.GetBatchResult.Message;
						return result;
					}
					result.response = batchResponse.GetBatchResult.Batch;
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
					result.response = string.Empty;
					return result;
				}
				//result.response = "";
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				result.response = string.Empty;
				return result;
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}
		public async Task<MessageModel<string>> GetBatchCode(GetBatchCodeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = true
			};
			if (string.IsNullOrEmpty(reqModel.productionId))
			{
				result.msg = "productionId不能为空";
				return result;
			}
			try
			{
				//获取上层列表
				MessageModel<List<EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", "api/Equipment/GetProductLine", _user.GetToken(), new { equipmentCode = reqModel.equipmentCode, level = "Line" });
				var equipments = apiResult_Equipments.response;
				if (equipments == null || equipments.Count == 0)
				{
					result.msg = "未找到Line";
					return result;
				}
				var line = equipments.FirstOrDefault();
				if (line == null)
				{
					result.msg = "未找到Line";
					return result;
				}
				var allCode = reqModel.LineCode;
				if (string.IsNullOrEmpty(allCode))
				{
					MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys = await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM", "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _user.GetToken(), new { EquipmentId = line.ID });
					var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
					if (equFunctionPropertys?.Count > 0)
					{
						foreach (var item in equFunctionPropertys)
						{
							var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "CounterfeitLineCode");
							if (pr != null)
							{
								allCode = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
							}
							if (!string.IsNullOrEmpty(allCode))
							{
								break;
							}
						}
					}
				}
				if (allCode == null)
				{
					result.msg = "未找到LineCode";
					return result;
				}
				if (allCode.Length < 2)
				{
					result.msg = "LineCode位数不足两位";
					return result;
				}
				string plant = allCode[0].ToString();//Y
				string lineCode = allCode[1].ToString();//A
				try
				{
					var productionOrder = await _dal2.FindEntity(reqModel.productionId);
					if (productionOrder == null)
					{
						result.msg = "未找到productionOrder";
						return result;
					}
					///煮制工单
					if (productionOrder.SapOrderType == "ZXH2")
					{
						reqModel.productionDate = productionOrder.PlanStartTime.Value;
					}
					var bgsResponse = await _esbHelper.PostXMLString("BGS00001", InterfaceHelper.GetBgsRequestXML(plant, lineCode, reqModel.productionDate.ToString("yyyyMMdd")));
					if (bgsResponse.successed != true)
					{
						result.msg = bgsResponse.msg;
						return result;
					}
					var batchResponse = InterfaceHelper.ParseFromBgsResponseXml<BGS.GetBatchResponse>(bgsResponse.Response);
					if (!batchResponse.GetBatchResult.IsSuccess)
					{
						result.msg = batchResponse.GetBatchResult.Message;
						return result;
					}
					result.response = batchResponse.GetBatchResult.Batch;
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
					result.response = string.Empty;
					return result;
				}
				//result.response = "";
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				result.response = string.Empty;
				return result;
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<string>> GetRunOrder(string equipmentId)
		{
			var result = new MessageModel<string>
			{
				msg = "获取成功！",
				success = true
			};
			var runOrderEx = (await _dal.FindList(x => x.RunEquipmentId == equipmentId)).OrderByDescending(x => x.CreateDate).FirstOrDefault();
			if (runOrderEx != null && runOrderEx.Status == "1")
			{
				var runOrder = await _dal2.FindEntity(x => x.ID == runOrderEx.ProductionOrderId);
				result.response = runOrder?.ProductionOrderNo;
			}
			return result;
		}

		public async Task<bool> SaveForm(PoProducedExecutionEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				entity.CreateCustomGuid(_user.Name);
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}

}