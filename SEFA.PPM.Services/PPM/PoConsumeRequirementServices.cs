using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.ViewModels.PPM;
using System;
using System.Linq;

namespace SEFA.PPM.Services
{
    public class PoConsumeRequirementServices : BaseServices<PoConsumeRequirementEntity>, IPoConsumeRequirementServices
    {
        #region 常量定义

        /// <summary>
        /// 备料状态常量
        /// </summary>
        private static class PreparationStatus
        {
            /// <summary>
            /// 无需备料
            /// </summary>
            public const string NoPreparation = "1";

            /// <summary>
            /// 待备料
            /// </summary>
            public const string WaitingPreparation = "2";

            /// <summary>
            /// 备料中
            /// </summary>
            public const string InPreparation = "12";

            /// <summary>
            /// 备料完成
            /// </summary>
            public const string PreparationCompleted = "3";

            /// <summary>
            /// 投料中
            /// </summary>
            public const string InFeeding = "8";

            /// <summary>
            /// 投料完成
            /// </summary>
            public const string FeedingCompleted = "9";
        }

        /// <summary>
        /// SAP需求标识常量
        /// </summary>
        private static class SapRequirementFlags
        {
            /// <summary>
            /// 需要准备
            /// </summary>
            public const string NeedPrepare = "1";

            /// <summary>
            /// 不需要准备
            /// </summary>
            public const string NoNeedPrepare = "0";

            /// <summary>
            /// 需要提前准备
            /// </summary>
            public const string NeedAdvancePrepare = "1";

            /// <summary>
            /// 不需要提前准备
            /// </summary>
            public const string NoNeedAdvancePrepare = "0";
        }

        #endregion
        private readonly IBaseRepository<PoConsumeRequirementEntity> _dal;
        private readonly IBaseRepository<ProductionOrderEntity> _productionOrderDal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        private readonly IBaseRepository<EquipmentEntity> _equipmentDal;
        private readonly IBaseRepository<PoSegmentRequirementEntity> _poSegmentRequirementDal;
        private readonly IUser _user;
        private readonly IBaseRepository<SapSegmentEntity> _sapSegmentDal;
        private readonly IBaseRepository<SapSegmentMaterialEntity> _sapSegmentMaterialDal;
        private readonly IBaseRepository<SapSegmentMaterialStepEntity> _sapSegmentMaterialStepDal;
        private readonly IBaseRepository<MaterialVersionEntity> _materialVersionDal;

        public PoConsumeRequirementServices(IBaseRepository<PoConsumeRequirementEntity> dal,
            IBaseRepository<ProductionOrderEntity> productionOrderDal,
            IBaseRepository<MaterialEntity> materialDal,
            IBaseRepository<EquipmentEntity> equipmentDal,
            IBaseRepository<PoSegmentRequirementEntity> poSegmentRequirementDal,
            IUser user,
            IBaseRepository<SapSegmentEntity> sapSegmentDal,
            IBaseRepository<SapSegmentMaterialEntity> sapSegmentMaterialDal,
            IBaseRepository<SapSegmentMaterialStepEntity> sapSegmentMaterialStepDal,
            IBaseRepository<MaterialVersionEntity> materialVersionDal)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._productionOrderDal = productionOrderDal;
            this._materialDal = materialDal;
            this._equipmentDal = equipmentDal;
            this._poSegmentRequirementDal = poSegmentRequirementDal;
            this._user = user;
            this._sapSegmentDal = sapSegmentDal;
            this._sapSegmentMaterialDal = sapSegmentMaterialDal;
            this._sapSegmentMaterialStepDal = sapSegmentMaterialStepDal;
            this._materialVersionDal = materialVersionDal;
        }

        public async Task<List<PoConsumeRequirementEntity>> GetList(PoConsumeRequirementRequestModel reqModel)
        {
            List<PoConsumeRequirementEntity> result = new List<PoConsumeRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<PoConsumeRequirementEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<PoConsumeRequirementEntity>> GetPageList(PoConsumeRequirementRequestModel reqModel)
        {
            PageModel<PoConsumeRequirementEntity> result = new PageModel<PoConsumeRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<PoConsumeRequirementEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(PoConsumeRequirementEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 根据工单编码、设备编码、物料编码、物料需求数量生成工单物料需求单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> GeneratePoConsumeRequirement(PoConsumeRequirementViewModel request)
        {
            try
            {
                // 参数校验
                var validationResult = ValidateRequest(request);
                if (!validationResult.IsValid)
                {
                    return CreateFailureResult(validationResult.ErrorMessage);
                }

                // 并行查询所有必要的数据
                var queryResult = await QueryRequiredDataAsync(request);
                if (!queryResult.IsValid)
                {
                    return CreateFailureResult(queryResult.ErrorMessage);
                }

                // 计算准备状态
                var preparationStatus = CalculatePreparationStatus(queryResult.SapSegmentMaterialStep);

                // 创建工单物料需求单
                var poConsumeRequirement = CreatePoConsumeRequirementEntity(
                    request,
                    queryResult,
                    preparationStatus);

                // 保存实体
                poConsumeRequirement.CreateCustomGuid(_user.Name);
                var saveSuccess = await this.SaveForm(poConsumeRequirement);

                return CreateResult(saveSuccess,
                    saveSuccess ? "插入物料需求成功" : "插入物料需求失败");
            }
            catch (Exception ex)
            {
                // 这里可以添加日志记录
                return CreateFailureResult($"生成工单物料需求时发生异常：{ex.Message}");
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 验证请求参数
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>验证结果</returns>
        private ValidationResult ValidateRequest(PoConsumeRequirementViewModel request)
        {
            if (request == null)
            {
                return ValidationResult.Failure("请求参数不能为空");
            }

            if (string.IsNullOrWhiteSpace(request.OrderCode))
            {
                return ValidationResult.Failure("工单编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(request.EquipmentCode))
            {
                return ValidationResult.Failure("设备编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(request.MaterialCode))
            {
                return ValidationResult.Failure("物料编码不能为空");
            }

            if (request.Quantity <= 0)
            {
                return ValidationResult.Failure("物料需求数量必须大于0");
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// 并行查询所有必要的数据
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>查询结果</returns>
        private async Task<QueryResult> QueryRequiredDataAsync(PoConsumeRequirementViewModel request)
        {
            // 并行查询基础数据和关联数据
            var basicDataTask = QueryBasicDataAsync(request);
            var relatedDataTask = QueryRelatedDataAsync(request);

            await Task.WhenAll(basicDataTask, relatedDataTask);

            var basicData = await basicDataTask;
            if (!basicData.IsValid)
            {
                return QueryResult.Failure(basicData.ErrorMessage);
            }

            var relatedData = await relatedDataTask;
            if (!relatedData.IsValid)
            {
                return QueryResult.Failure(relatedData.ErrorMessage);
            }

            return QueryResult.Success(
                basicData.Order,
                basicData.Material,
                basicData.Equipment,
                relatedData.PoSegmentRequirement,
                relatedData.SapSegmentMaterialStep);
        }

        /// <summary>
        /// 并行查询基础数据（工单、物料、设备）
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>基础数据查询结果</returns>
        private async Task<BasicDataResult> QueryBasicDataAsync(PoConsumeRequirementViewModel request)
        {
            // 创建并行任务
            var orderTask = _productionOrderDal.FindEntity(a => a.ProductionOrderNo == request.OrderCode);
            var materialTask = _materialDal.FindEntity(a => a.Code == request.MaterialCode);
            var equipmentTask = _equipmentDal.FindEntity(a => a.EquipmentCode == request.EquipmentCode);

            // 等待所有任务完成
            await Task.WhenAll(orderTask, materialTask, equipmentTask);

            // 获取结果
            var order = await orderTask;
            var material = await materialTask;
            var equipment = await equipmentTask;

            if (order == null)
            {
                return BasicDataResult.Failure($"未找到工单号为 {request.OrderCode} 的工单");
            }

            if (material == null)
            {
                return BasicDataResult.Failure($"未找到物料编码为 {request.MaterialCode} 的物料");
            }

            if (equipment == null)
            {
                return BasicDataResult.Failure($"未找到设备编码为 {request.EquipmentCode} 的设备");
            }

            return BasicDataResult.Success(order, material, equipment);
        }

        /// <summary>
        /// 查询关联数据（工单工序需求、SAP物料步骤）
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>关联数据查询结果</returns>
        private async Task<RelatedDataResult> QueryRelatedDataAsync(PoConsumeRequirementViewModel request)
        {
            // 首先获取基础数据以便查询关联数据
            var (order, material, equipment) = await (
                _productionOrderDal.FindEntity(a => a.ProductionOrderNo == request.OrderCode),
                _materialDal.FindEntity(a => a.Code == request.MaterialCode),
                _equipmentDal.FindEntity(a => a.EquipmentCode == request.EquipmentCode)
            );

            if (order == null || material == null || equipment == null)
            {
                return RelatedDataResult.Failure("基础数据查询失败，无法查询关联数据");
            }

            // 并行查询关联数据
            var (poSegmentRequirement, sapSegmentMaterialStep) = await (
                _poSegmentRequirementDal.FindEntity(a =>
                    a.ProductionOrderId == order.ID && a.SegmentId == equipment.ID),
                _sapSegmentMaterialDal.Db
                    .Queryable<SapSegmentMaterialEntity, SapSegmentMaterialStepEntity>((s, m) =>
                        new object[]
                        {
                            JoinType.Left, s.ID == m.SapSegmentMaterialId
                        })
                    .Where((s, m) => s.MaterialVersionId == order.MaterialVersionId && m.MaterialId == material.ID)
                    .Select((s, m) => m)
                    .FirstAsync()
            );

            if (poSegmentRequirement == null)
            {
                return RelatedDataResult.Failure("未找到工单工序需求");
            }

            if (sapSegmentMaterialStep == null)
            {
                return RelatedDataResult.Failure("未找到工单物料需求");
            }

            return RelatedDataResult.Success(poSegmentRequirement, sapSegmentMaterialStep);
        }

        /// <summary>
        /// 计算准备状态
        /// </summary>
        /// <param name="sapSegmentMaterialStep">SAP物料步骤</param>
        /// <returns>准备状态</returns>
        private string CalculatePreparationStatus(SapSegmentMaterialStepEntity sapSegmentMaterialStep)
        {
            if (sapSegmentMaterialStep.NeedPrepare == SapRequirementFlags.NeedPrepare &&
                sapSegmentMaterialStep.NeedAdvancePrepare == SapRequirementFlags.NoNeedAdvancePrepare)
            {
                return PreparationStatus.WaitingPreparation;
            }

            return PreparationStatus.NoPreparation;
        }

        /// <summary>
        /// 创建工单物料需求实体
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <param name="queryResult">查询结果</param>
        /// <param name="preparationStatus">准备状态</param>
        /// <returns>工单物料需求实体</returns>
        private PoConsumeRequirementEntity CreatePoConsumeRequirementEntity(
            PoConsumeRequirementViewModel request,
            QueryResult queryResult,
            string preparationStatus)
        {
            return new PoConsumeRequirementEntity
            {
                ProductionOrderId = queryResult.Order.ID,
                PoSegmentRequirementId = queryResult.PoSegmentRequirement.ID,
                MaterialId = queryResult.Material.ID,
                MaterialCode = queryResult.Material.Code,
                MaterialDescription = queryResult.Material.NAME,
                Quantity = request.Quantity,
                WeighingQty = request.Quantity,
                UnitId = queryResult.Material.Unit,
                PreparationStatus = preparationStatus,
                BomMaterialId = queryResult.SapSegmentMaterialStep.ID,
                CurrentFlag = "1",
                Deleted = 0
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败结果</returns>
        private MessageModel<string> CreateFailureResult(string errorMessage)
        {
            return new MessageModel<string>
            {
                success = false,
                msg = errorMessage
            };
        }

        /// <summary>
        /// 创建结果
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        /// <returns>结果</returns>
        private MessageModel<string> CreateResult(bool success, string message)
        {
            return new MessageModel<string>
            {
                success = success,
                msg = message
            };
        }

        #endregion

        #region 内部辅助类

        /// <summary>
        /// 验证结果
        /// </summary>
        private class ValidationResult
        {
            public bool IsValid { get; private set; }
            public string ErrorMessage { get; private set; }

            private ValidationResult(bool isValid, string errorMessage = null)
            {
                IsValid = isValid;
                ErrorMessage = errorMessage;
            }

            public static ValidationResult Success() => new ValidationResult(true);
            public static ValidationResult Failure(string errorMessage) => new ValidationResult(false, errorMessage);
        }

        /// <summary>
        /// 基础数据查询结果
        /// </summary>
        private class BasicDataResult
        {
            public bool IsValid { get; private set; }
            public string ErrorMessage { get; private set; }
            public ProductionOrderEntity Order { get; private set; }
            public MaterialEntity Material { get; private set; }
            public EquipmentEntity Equipment { get; private set; }

            private BasicDataResult(bool isValid, string errorMessage = null)
            {
                IsValid = isValid;
                ErrorMessage = errorMessage;
            }

            private BasicDataResult(ProductionOrderEntity order, MaterialEntity material, EquipmentEntity equipment)
            {
                IsValid = true;
                Order = order;
                Material = material;
                Equipment = equipment;
            }

            public static BasicDataResult Success(ProductionOrderEntity order, MaterialEntity material, EquipmentEntity equipment)
                => new BasicDataResult(order, material, equipment);

            public static BasicDataResult Failure(string errorMessage)
                => new BasicDataResult(false, errorMessage);
        }

        /// <summary>
        /// 关联数据查询结果
        /// </summary>
        private class RelatedDataResult
        {
            public bool IsValid { get; private set; }
            public string ErrorMessage { get; private set; }
            public PoSegmentRequirementEntity PoSegmentRequirement { get; private set; }
            public SapSegmentMaterialStepEntity SapSegmentMaterialStep { get; private set; }

            private RelatedDataResult(bool isValid, string errorMessage = null)
            {
                IsValid = isValid;
                ErrorMessage = errorMessage;
            }

            private RelatedDataResult(PoSegmentRequirementEntity poSegmentRequirement, SapSegmentMaterialStepEntity sapSegmentMaterialStep)
            {
                IsValid = true;
                PoSegmentRequirement = poSegmentRequirement;
                SapSegmentMaterialStep = sapSegmentMaterialStep;
            }

            public static RelatedDataResult Success(PoSegmentRequirementEntity poSegmentRequirement, SapSegmentMaterialStepEntity sapSegmentMaterialStep)
                => new RelatedDataResult(poSegmentRequirement, sapSegmentMaterialStep);

            public static RelatedDataResult Failure(string errorMessage)
                => new RelatedDataResult(false, errorMessage);
        }

        /// <summary>
        /// 完整查询结果
        /// </summary>
        private class QueryResult
        {
            public bool IsValid { get; private set; }
            public string ErrorMessage { get; private set; }
            public ProductionOrderEntity Order { get; private set; }
            public MaterialEntity Material { get; private set; }
            public EquipmentEntity Equipment { get; private set; }
            public PoSegmentRequirementEntity PoSegmentRequirement { get; private set; }
            public SapSegmentMaterialStepEntity SapSegmentMaterialStep { get; private set; }

            private QueryResult(bool isValid, string errorMessage = null)
            {
                IsValid = isValid;
                ErrorMessage = errorMessage;
            }

            private QueryResult(ProductionOrderEntity order, MaterialEntity material, EquipmentEntity equipment,
                PoSegmentRequirementEntity poSegmentRequirement, SapSegmentMaterialStepEntity sapSegmentMaterialStep)
            {
                IsValid = true;
                Order = order;
                Material = material;
                Equipment = equipment;
                PoSegmentRequirement = poSegmentRequirement;
                SapSegmentMaterialStep = sapSegmentMaterialStep;
            }

            public static QueryResult Success(ProductionOrderEntity order, MaterialEntity material, EquipmentEntity equipment,
                PoSegmentRequirementEntity poSegmentRequirement, SapSegmentMaterialStepEntity sapSegmentMaterialStep)
                => new QueryResult(order, material, equipment, poSegmentRequirement, sapSegmentMaterialStep);

            public static QueryResult Failure(string errorMessage)
                => new QueryResult(false, errorMessage);
        }

        #endregion
    }
}