using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Services.Helper
{
    public class AttributeCopyHelper
    {
        /// <summary>
        /// 将 source 类型的实例中的属性值复制到 target 类型的实例中，
        /// 只有当属性名称字母相同（忽略大小写）时才复制。
        /// </summary>
        /// <param name="source">源对象。</param>
        /// <param name="target">目标对象。</param>
        public static void CopyAttributesIgnoringCase(object source, object target)
        {
            if (source == null || target == null)
                throw new ArgumentNullException("参数不能为 null");

            var sourceProperties = source.GetType().GetProperties();
            var targetProperties = target.GetType().GetProperties();

            foreach (var sourceProperty in sourceProperties)
            {
                // 获取目标类型的同名属性（忽略大小写）
                var targetProperty = targetProperties.FirstOrDefault(
                    p => p.Name.Equals(sourceProperty.Name, StringComparison.OrdinalIgnoreCase));

                if (targetProperty != null && targetProperty.CanWrite && sourceProperty.CanRead)
                {
                    // 检查属性类型是否匹配
                    if (sourceProperty.PropertyType == targetProperty.PropertyType)
                    {
                        // 尝试设置属性值
                        try
                        {
                            targetProperty.SetValue(target, sourceProperty.GetValue(source));
                        }
                        catch (Exception ex)
                        {
                            // Console.WriteLine($"Error setting property {sourceProperty.Name}: {ex.Message}");
                        }
                    }
                    else
                    {
                        if (targetProperty.PropertyType == typeof(String))
                        {
                            try
                            {
                                var tvalue = sourceProperty.GetValue(source);
                                if (tvalue != null)
                                {
                                    targetProperty.SetValue(target, tvalue.ToString());
                                }
                            }
                            catch (Exception ex)
                            {
                                // Console.WriteLine($"Error setting property {sourceProperty.Name}: {ex.Message}");
                            }

                        }
                        // Console.WriteLine($"Type mismatch for property {sourceProperty.Name}");
                    }
                }
            }
        }
    }
}
