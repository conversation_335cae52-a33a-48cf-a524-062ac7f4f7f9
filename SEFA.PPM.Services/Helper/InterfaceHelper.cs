using System;
using System.Xml.Serialization;
using System.Xml;
using System.IO;
using System.Text.RegularExpressions;
using System.Data.SqlTypes;
using System.Xml.Schema;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Magicodes.IE.Core;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
using SEFA.Base.Common;

namespace SEFA.PPM.Services
{
	public class InterfaceHelper
	{
		#region Sap

		public static string GetSapRequestXML<T>(object obj)
		{
			try
			{
				XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
				XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
				ns.Add("", ""); // 添加一个空的namespace
				using StringWriter textWriter = new StringWriter();
				using var xmlWriter = new XmlTextWriter(textWriter) { Formatting = Formatting.Indented };
				//xmlSerializer.Serialize(textWriter, obj);
				xmlSerializer.Serialize(textWriter, obj, ns);
				string xmlContent = textWriter.ToString();
				// 去除xml声明
				xmlContent = xmlContent.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "");
				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(xmlContent);
				XmlElement root = xmlDoc.DocumentElement; // 获取根元素
				xmlContent = xmlContent.Replace(root.Name, "urn:" + root.Name);
				xmlContent = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:sap-com:document:sap:rfc:functions\"><soapenv:Header/><soapenv:Body>" + xmlContent;
				xmlContent += "</soapenv:Body></soapenv:Envelope>";
				return xmlContent;
			}
			catch (Exception)
			{
				return string.Empty;
			}
		}

        public static string GetSapRoutingRequestXML<T>(object obj)
        {
            try
            {
                XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
                XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
                ns.Add("", ""); // 添加一个空的namespace
                using StringWriter textWriter = new StringWriter();
                using var xmlWriter = new XmlTextWriter(textWriter) { Formatting = Formatting.Indented };
                //xmlSerializer.Serialize(textWriter, obj);
                xmlSerializer.Serialize(textWriter, obj, ns);
                string xmlContent = textWriter.ToString();
                // 去除xml声明
                xmlContent = xmlContent.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "");
                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(xmlContent);
                XmlElement root = xmlDoc.DocumentElement; // 获取根元素
                xmlContent = xmlContent.Replace(root.Name, "urn:" + root.Name);
                xmlContent = "<soapenv:Envelope xmlns:soapenv=\\\"http://schemas.xmlsoap.org/soap/envelope/\\\" xmlns:urn=\\\"urn:sap-com:document:sap:rfc:functions\\\"><soapenv:Header/><soapenv:Body>" + xmlContent;
                xmlContent += "</soapenv:Body></soapenv:Envelope>";
                return xmlContent;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }

        public static string GetSapResponseXML<T>(object obj)
		{
			try
			{
				XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
				XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
				ns.Add("", ""); // 添加一个空的namespace
				using StringWriter textWriter = new StringWriter();
				using var xmlWriter = new XmlTextWriter(textWriter) { Formatting = Formatting.Indented };
				//xmlSerializer.Serialize(textWriter, obj);
				xmlSerializer.Serialize(textWriter, obj, ns);
				string xmlContent = textWriter.ToString();
				// 去除xml声明
				xmlContent = xmlContent.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "");
				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(xmlContent);
				XmlElement root = xmlDoc.DocumentElement; // 获取根元素
				root.SetAttribute("xmlns:n0", "urn:sap-com:document:sap:rfc:functions");
				xmlContent = xmlDoc.OuterXml;
				xmlContent = xmlContent.Replace(root.Name, "n0:" + root.Name);
				xmlContent = "<soap-env:Envelope xmlns:soap-env=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap-env:Header/><soap-env:Body>" + xmlContent;
				xmlContent += "</soap-env:Body></soap-env:Envelope>";
				return xmlContent;
			}
			catch (Exception)
			{
				return string.Empty;
			}
		}

		public static T ParseFromSapResponseXml<T>(string xml)
		{
			xml = xml.Replace("<soap-env:Envelope xmlns:soap-env=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap-env:Header/><soap-env:Body>", "");
			xml = xml.Replace("</soap-env:Body></soap-env:Envelope>", "");
			xml = xml.Replace("n0:", "");
			XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
			StringReader stringReader = new StringReader(xml);
			T result = (T)xmlSerializer.Deserialize(stringReader);
			stringReader.Close();
			stringReader.Dispose();
			return result;
		}

		#endregion

		#region Bgs

		public static string GetBgsRequestXML(string plant = "H", string line = "A", string date = "20240620")
		{
			string userId = "admin";
			string userPW = "B1!#@!#234wwwx";
			try
			{
				userId = Appsettings.app("BGSAuth", "UserId");
				userPW = Appsettings.app("BGSAuth", "UserPW");
			}
			catch (Exception ex)
			{

			}
			try
			{
				string BGSFomart = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:lkk=\"http://lkk.com/\"><soapenv:Header><lkk:UserValidation><lkk:UserId>{0}</lkk:UserId><lkk:UserPW>{1}</lkk:UserPW></lkk:UserValidation></soapenv:Header><soapenv:Body><lkk:GetBatch><lkk:inputModel><lkk:Plant>{2}</lkk:Plant><lkk:Line>{3}</lkk:Line><lkk:Date>{4}</lkk:Date></lkk:inputModel></lkk:GetBatch></soapenv:Body></soapenv:Envelope>";
				return string.Format(BGSFomart, userId, userPW, plant, line, date);
			}
			catch (Exception ex)
			{
				return string.Empty;
			}
		}

		public static T ParseFromBgsResponseXml<T>(string xml)
		{
			// 使用正则表达式匹配XML内容
			string pattern = @"<soap:Body>.*?</soap:Body>";
			Match match = Regex.Match(xml, pattern);
			if (match.Success)
			{
				xml = match.Value;
			}
			else
			{
				Console.WriteLine("XML data not found.");
			}
			xml = xml.Replace("<soap:Body>", "");
			xml = xml.Replace("</soap:Body>", "");
			xml = xml.Replace(" xmlns=\"http://lkk.com/\"", "");
			//xml = xml.Replace("<GetBatchResult>", "<GetBatchResult xmlns=\"http://lkk.com/\">");
			xml = xml.Replace("<IsSuccess>", "<IsSuccess xmlns=\"http://lkk.com/\">");
			xml = xml.Replace("<Batch>", "<Batch xmlns=\"http://lkk.com/\">");
			xml = xml.Replace("<Message>", "<Message xmlns=\"http://lkk.com/\">");
			XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
			StringReader stringReader = new StringReader(xml);
			T result = (T)xmlSerializer.Deserialize(stringReader);
			stringReader.Close();
			stringReader.Dispose();
			return result;
		}

		#endregion

		#region Colos

		public static string GetColosRequestXML<T>(object obj)
		{
			try
			{
				XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
				using TextWriter textWriter = new StringWriter();
				xmlSerializer.Serialize(textWriter, obj);
				string xmlContent = textWriter.ToString();
				xmlContent = xmlContent.Replace("xsd", "xsd1");
				xmlContent = xmlContent.Replace("xsi", "xsd");
				xmlContent = xmlContent.Replace("xsd1", "xsi");
				xmlContent = xmlContent.Replace("XMLSchema-instance", "XMLSchema-instance1");
				xmlContent = xmlContent.Replace("XMLSchema", "XMLSchema-instance");
				xmlContent = xmlContent.Replace("XMLSchema-instance-instance1", "XMLSchema");
				return xmlContent;
			}
			catch (Exception)
			{
				return string.Empty;
			}
		}

		public static T ParseFromColosResponseXml<T>(string xml)
		{
			xml = xml.Replace("<soap-env:Envelope xmlns:soap-env=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap-env:Header/><soap-env:Body>", "");
			xml = xml.Replace("</soap-env:Body></soap-env:Envelope>", "");
			xml = xml.Replace("n0:", "");
			XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
			StringReader stringReader = new StringReader(xml);
			T result = (T)xmlSerializer.Deserialize(stringReader);
			stringReader.Close();
			stringReader.Dispose();
			return result;
		}


		#endregion

	}
}