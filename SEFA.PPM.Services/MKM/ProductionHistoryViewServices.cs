
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using StackExchange.Profiling.Internal;
using Microsoft.AspNetCore.Mvc;
using SEFA.MKM.Model.Models.MKM;
using SEFA.Base.Repository.Base;
using Microsoft.AspNetCore.Mvc.RazorPages;
using SEFA.PPM.Model.Models;
using SEFA.Base.Common.HttpContextUser;
using AutoMapper;
using Abp.Domain.Uow;
using SEFA.Base.IRepository.UnitOfWork;
using IUnitOfWork = SEFA.Base.IRepository.UnitOfWork.IUnitOfWork;
using MongoDB.Bson.Serialization.IdGenerators;
using System.Linq.Expressions;
using SEFA.Base.ESB;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using InfluxDB.Client.Api.Domain;
using System;
using Newtonsoft.Json;
using SEFA.Base.Repository.UnitOfWork;
using System.Linq;
using System.Security.Cryptography;
using System.Numerics;
using System.Text.RegularExpressions;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.MKM.Model.ViewModels.View;
using static SEFA.PTM.Services.ConsumeViewServices;
using System.Collections;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;
using static SEFA.PPM.Services.TippingMlistViewServices;
using Microsoft.Extensions.Logging;
using System.Reactive;
using static MongoDB.Driver.WriteConcern;
using System.Diagnostics;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using SEFA.Base.Common.LogHelper;
using System.Reflection;
using SEFA.PPM.IServices;
using static SEFA.DFM.Model.ViewModels.DFMCommonModel;
using SEFA.Base.Common.WebApiClients.HttpApis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;

namespace SEFA.MKM.Services
{
    public class ProductionHistoryViewServices : BaseServices<ProductionHistoryViewEntity>, IProductionHistoryViewServices
    {

        private readonly IBaseRepository<SappackorderEntity> _dalSappackorderEntity;

        private readonly IBaseRepository<ProductionHistoryViewEntity> _dal;
        private readonly IBaseRepository<PoProducedActualEntity> _dal2;
        private readonly IBaseRepository<MaterialInventoryEntity> _dal3;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
        private readonly IMapper _mapper;
        private readonly LKKESBHelper _esbHelper;


        private readonly IBaseRepository<PoProfunctionViewEntity> _PoProfunctionViewEntityDal;
        private readonly IBaseRepository<MaterialLotEntity> _materialLotEntityDal;
        private readonly IBaseRepository<MMaterialPropertyViewEntity> _MaterialPropertyDal;
        private readonly IBaseRepository<ProductionOrderEntity> _ProductionOrderEntityDal;
        private readonly IBaseRepository<PoProducedRequirementEntity> _PoProducedRequirementEntityDal;
        private readonly IBaseRepository<MaterialSubLotEntity> _materialSubLotServicesDal;
        private readonly IBaseRepository<EquipmentEntity> _EquipmentEntityDal;
        private readonly IBaseRepository<MaterialEntity> _materialEntitydal;

        private readonly IBaseRepository<MaterialInventoryEntity> _MaterialInventoryEntitydal;
        private readonly IBaseRepository<MaterialTransferEntity> _MaterialTransferEntitydal;
        private readonly IBaseRepository<PoProducedExecutionEntity> _PoProducedExecutionEntitydal;
        private readonly IProduceViewServices _produceViewServices;

        public ProductionHistoryViewServices(IBaseRepository<ProductionHistoryViewEntity> dal, IBaseRepository<PoProducedActualEntity> dal2, IBaseRepository<MaterialInventoryEntity> dal3, IUnitOfWork unitOfWork, IUser user, IMapper mapper, LKKESBHelper esbHelper, IBaseRepository<MaterialLotEntity> materialLotEntityDal, IBaseRepository<MMaterialPropertyViewEntity> materialPropertyDal, IBaseRepository<MaterialSubLotEntity> materialSubLotServicesDal, IBaseRepository<EquipmentEntity> equipmentEntityDal, IBaseRepository<ProductionOrderEntity> productionOrderEntityDal, IBaseRepository<PoProducedRequirementEntity> poProducedRequirementEntityDal, IBaseRepository<PoProfunctionViewEntity> poProfunctionViewEntityDal, IBaseRepository<MaterialEntity> materialEntitydal, IBaseRepository<MaterialInventoryEntity> materialInventoryEntitydal, IBaseRepository<MaterialTransferEntity> materialTransferEntitydal, IBaseRepository<PoProducedExecutionEntity> poProducedExecutionEntitydal, IProduceViewServices produceViewServices, IBaseRepository<SappackorderEntity> dalSappackorderEntity = null)
        {
            _dal = dal;
            _dal2 = dal2;
            _dal3 = dal3;
            BaseDal = dal;
            _unitOfWork = unitOfWork;
            _user = user;
            _mapper = mapper;
            _esbHelper = esbHelper;
            _materialLotEntityDal = materialLotEntityDal;
            _MaterialPropertyDal = materialPropertyDal;
            _materialSubLotServicesDal = materialSubLotServicesDal;
            _EquipmentEntityDal = equipmentEntityDal;
            _ProductionOrderEntityDal = productionOrderEntityDal;
            _PoProducedRequirementEntityDal = poProducedRequirementEntityDal;
            _PoProfunctionViewEntityDal = poProfunctionViewEntityDal;
            _materialEntitydal = materialEntitydal;
            _MaterialInventoryEntitydal = materialInventoryEntitydal;
            _MaterialTransferEntitydal = materialTransferEntitydal;
            _PoProducedExecutionEntitydal = poProducedExecutionEntitydal;
            _produceViewServices = produceViewServices;
            _dalSappackorderEntity = dalSappackorderEntity;
        }

        public async Task<List<ProductionHistoryViewEntity>> GetList(ProductionHistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }

            var whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
              //加入查询条件(时间)
              //EquipmentId
              .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
              //batch
              .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
              //SSCC
              .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
               .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
              //Process Order
              .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
              //Containername
              .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
              //machine
              .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machinecode == reqModel.Machine)
              //Destination
              .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
              //Comment
              .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
              //ReasonCode
              .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
              //制造工单/灌包装工单
              .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                .AndIF(!string.IsNullOrEmpty(reqModel.CreateUserId), a => a.CreateUserId.Contains(reqModel.CreateUserId))

              //ShiftName
              .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
              .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
              .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
              .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
            .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
                //加入查询条件(时间)
                //EquipmentId
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
                //batch
                .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
                 .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
                //Process Order
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                //Containername
                .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
                //machine
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machinecode == reqModel.Machine)
                //Destination
                .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
                //Comment
                .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
                //ReasonCode
                .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
                    //制造工单/灌包装工单
                    .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype != "ZXH2")
                 //ShiftName
                 .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
                 .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
                 .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
               .ToExpression();
            }
            var data = await _dal.Db.Queryable<ProductionHistoryViewEntity>().
            Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();
            return data;
        }

        public async Task<List<GroupData>> GetProduceSumList(ProductionHistoryViewRequestModel reqModel)
        {
            var list = await GetList(reqModel);
            var goups = list.GroupBy(x => new { x.Pid, x.BatchId, x.ProcessOrder, x.MaterialId, x.MaterialCode, x.MaterialName, x.LotCode, x.Unit });
            // 查询 BatchProducedRequirementEntity 表的数据
            var batchProducedRequirements = await _dal.Db.Queryable<BatchProducedRequirementEntity>().ToListAsync();
            // 查询 PoProducedRequirementEntity 表的数据
            var poProducedRequirements = await _dal.Db.Queryable<PoProducedRequirementEntity>().ToListAsync();
            var data = (goups.Select(g =>
            {
                var batchProducedSum = batchProducedRequirements
                            .Join(poProducedRequirements, bc => bc.PoProducedRequirementId, pc => pc.ID, (bc, pc) => new { bc, pc })
                            .Where(j => j.bc.BatchId == g.Key.BatchId && j.pc.ProductionOrderId == g.Key.Pid && j.pc.MaterialId == g.Key.MaterialId)
                            .Sum(j => j.bc.Quantity);
                return new GroupData
                {
                    Pid = g.Key.Pid,
                    BatchId = g.Key.BatchId,
                    ProcessOrder = g.Key.ProcessOrder,
                    MId = g.Key.MaterialId,
                    MCode = g.Key.MaterialCode,
                    MName = g.Key.MaterialName,
                    LBatch = g.Key.LotCode,
                    Unit = g.Key.Unit,
                    Q0 = batchProducedSum,
                    Q1 = g.Sum(x => x.Quantity) ?? 0,
                    Q2 = g.Where(x => x.SendStates == "已发送")?.Sum(x => x.Quantity) ?? 0,
                    Q3 = g.Where(x => x.SendStates == "等待SAP返回结果")?.Sum(x => x.Quantity) ?? 0,
                    Q4 = g.Where(x => x.SendStates == "未发送")?.Sum(x => x.Quantity) ?? 0,
                    Q5 = g.Where(x => x.SendStates == "发送失败")?.Sum(x => x.Quantity) ?? 0,
                    Q6 = (g.Sum(x => x.Quantity) ?? 0) - batchProducedSum,
                    Details = g.GroupBy(x => new
                    {
                        x.Pid,
                        x.BatchId,
                        x.ProcessOrder,
                        x.MaterialId,
                        x.MaterialCode,
                        x.MaterialName,
                        x.LotCode,
                        x.SUB_LOT_ID,
                        x.Unit
                    }).Select(gi => new GroupDataDetail()
                    {
                        Pid = gi.Key.Pid,
                        BatchId = gi.Key.BatchId,
                        ProcessOrder = gi.Key.ProcessOrder,
                        MId = gi.Key.MaterialId,
                        MCode = gi.Key.MaterialCode,
                        MName = gi.Key.MaterialName,
                        LBatch = gi.Key.LotCode,
                        TraceCode = gi.Key.SUB_LOT_ID,
                        Quantity = gi.Sum(x => x.Quantity) ?? 0,
                        Unit = gi.Key.Unit
                    }).OrderBy(x => x.ProcessOrder).ThenBy(x => x.MCode).ThenBy(x => x.LBatch).ToList()
                };
            })).OrderBy(x => x.ProcessOrder).ThenBy(x => x.MCode).ThenBy(x => x.LBatch).ToList();
            return data;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
  //    public async Task<List<Select>> GetProductionMachine(BatchPalletModel reqModel)
                                  //{
                                  //	if (reqModel.typeSerch.Contains("ZZ"))
                                  //	{
                                  //		reqModel.typeSerch = "ZXH2";
                                  //	}
                                  //	else if (reqModel.typeSerch.Contains("GBZ"))
                                  //	{
                                  //		reqModel.typeSerch = "ZXH1";
                                  //	}

        //	if (reqModel.typeSerch == "ZXH1")
        //	{
        //		var data = await _dal.FindList(a => a.Sapordertype != "ZXH2");
        //		var selects = data.GroupBy(p => new { p.Machine, p.Machinecode }).Select(x => new Select { key = x.Key.Machinecode, value = x.Key.Machine }).ToList();
        //		return selects;
        //	}
        //	else
        //	{
        //		var data = await _dal.FindList(a => a.Sapordertype == "ZXH2");
        //		var selects = data.GroupBy(p => new { p.Machine, p.Machinecode }).Select(x => new Select { key = x.Key.Machinecode, value = x.Key.Machine }).ToList();
        //		return selects;
        //	}
        //}

        public async Task<List<Select>> GetProductionMachine(BatchPalletModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }


            var dataRespone = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", $"api/Equipment/GetListByLevel?key=unit", _user.GetToken());
            if (!dataRespone.success)
            {
                return new List<Select>();
            }

            var result = dataRespone.response;
            if (dataRespone.response == null || dataRespone.response.Count <= 0)
            {
                return new List<Select>();
            }

            var selects = result.GroupBy(p => new { p.EquipmentName, p.EquipmentCode }).Select(x => new Select { key = x.Key.EquipmentCode, value = x.Key.EquipmentName }).ToList();
            return selects;

            //    if (reqModel.typeSerch == "ZXH1")
            //{
            //    var data = await _dal.FindList(a => a.Sapordertype != "ZXH2");
            //    var selects = data.GroupBy(p => new { p.Machine, p.Machinecode }).Select(x => new Select { key = x.Key.Machinecode, value = x.Key.Machine }).ToList();
            //    return selects;
            //}
            //else
            //{
            //    var data = await _dal.FindList(a => a.Sapordertype == "ZXH2");
            //    var selects = data.GroupBy(p => new { p.Machine, p.Machinecode }).Select(x => new Select { key = x.Key.Machinecode, value = x.Key.Machine }).ToList();
            //    return selects;
            //}
        }

        public async Task<PageModel<ProductionHistoryViewEntity>> GetPageList(ProductionHistoryViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(ProductionHistoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        /// <summary>
        /// 产出制造历史/产出罐包装导出
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<ProductionConsumExports>> GetPHisExport(ProductionHistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }

            PageModel<ProductionHistoryViewEntity> result = new PageModel<ProductionHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
              //加入查询条件(时间)
              //EquipmentId
              .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
              //batch
              .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
              //SSCC
              .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
               .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
              //Process Order
              .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
              //Containername
              .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
              //machine
              .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machinecode == reqModel.Machine)
              //Destination
              .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
              //Comment
              .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
              //ReasonCode
              .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
              //制造工单/灌包装工单
              .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                .AndIF(!string.IsNullOrEmpty(reqModel.CreateUserId), a => a.CreateUserId.Contains(reqModel.CreateUserId))

              //ShiftName
              .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
              .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
              .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
              .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
              .AndIF(reqModel.DataType == "1", a => a.Quantity >= 0)
              .AndIF(reqModel.DataType == "-1", a => a.Quantity < 0)
            .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
                //加入查询条件(时间)
                //EquipmentId
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
                //batch
                .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
                 .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
                //Process Order
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                //Containername
                .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
                //machine
                .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machinecode == reqModel.Machine)
                //Destination
                .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
                //Comment
                .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
                //ReasonCode
                .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
                    //制造工单/灌包装工单
                    .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype != "ZXH2")
                 //ShiftName
                 .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
                 .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
                 .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
                 .AndIF(reqModel.DataType == "1", a => a.Quantity >= 0)
                 .AndIF(reqModel.DataType == "-1", a => a.Quantity < 0)
               .ToExpression();
            }
            var data = await _dal.Db.Queryable<ProductionHistoryViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToListAsync();

            //转换
            var results = (from a in data
                           select new ProductionConsumExports
                           {
                               ProcessOrder = a.ProcessOrder,
                               Formula = a.Formula,
                               MaterialCode = a.MaterialCode,
                               MaterialName = a.MaterialName,
                               LotCode = a.LotCode,
                               SUB_LOT_ID = a.SUB_LOT_ID,
                               Quantity = a.Quantity,
                               Unit = a.Unit,
                               Machine = a.Machine,
                               Destination = a.Destination,
                               SourceCode = a.SourceCode,
                               ShiftName = a.ShiftName,
                               Mblnr = a.Mblnr,
                               ReasonCode = a.ReasonCode,
                               ActuaComment = a.ActuaComment,
                               CreateDate = a.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                               ModifyDate = a.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),

                           }).ToList();
            return results;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<ProductionHistoryViewEntityModel>> GetPageLists(ProductionHistoryViewRequestModel reqModel)
        {
            if (reqModel.typeSerch.Contains("ZZ"))
            {
                reqModel.typeSerch = "ZXH2";
            }
            else if (reqModel.typeSerch.Contains("GBZ"))
            {
                reqModel.typeSerch = "ZXH1";
            }

            PageModel<ProductionHistoryViewEntityModel> result = new PageModel<ProductionHistoryViewEntityModel>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
              //加入查询条件(时间)
              //EquipmentId
              .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
              //batch
              .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
              //SSCC
              .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
               .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
              //Process Order
              .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
              //Containername
              .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
              //machine
              .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine.Contains(reqModel.Machine))
              //Destination
              .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
              //Comment
              .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
              //ReasonCode
              .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
              //制造工单/灌包装工单
              .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype.Contains(reqModel.typeSerch))
                .AndIF(!string.IsNullOrEmpty(reqModel.CreateUserId), a => a.CreateUserId.Contains(reqModel.CreateUserId))

              //ShiftName
              .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
              .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
              .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
              .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
              .AndIF(reqModel.DataType == "1", a => a.Quantity >= 0)
              .AndIF(reqModel.DataType == "-1", a => a.Quantity < 0)
            .ToExpression();

            if (reqModel.typeSerch == "ZXH1")
            {
                whereExpression = Expressionable.Create<ProductionHistoryViewEntity>()
                //加入查询条件(时间)
                //EquipmentId
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId.Contains(reqModel.EquipmentId))
                //batch
                .AndIF(!string.IsNullOrEmpty(reqModel.LotCode), a => a.LotCode.Contains(reqModel.LotCode))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.SubLotId), a => a.SUB_LOT_ID.Contains(reqModel.SubLotId))
                 .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode) || a.MaterialName.Contains(reqModel.MaterialCode))
                //Process Order
                .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProcessOrder.Contains(reqModel.ProcessOrder))
                //Containername
                .AndIF(!string.IsNullOrEmpty(reqModel.Containername), a => a.ProcessOrder.Contains(reqModel.Containername))
                 //machine
                 .AndIF(!string.IsNullOrEmpty(reqModel.Machine), a => a.Machine.Contains(reqModel.Machine))
                //Destination
                .AndIF(!string.IsNullOrEmpty(reqModel.Destination), a => a.Destination == reqModel.Destination)
                //Comment
                .AndIF(!string.IsNullOrEmpty(reqModel.ActuaComment), a => a.ActuaComment == reqModel.ActuaComment)
                //ReasonCode
                .AndIF(!string.IsNullOrEmpty(reqModel.ReasonCode), a => a.ReasonCode.Contains(reqModel.ReasonCode))
                    //制造工单/灌包装工单
                    .AndIF(!string.IsNullOrEmpty(reqModel.typeSerch), a => a.Sapordertype != "ZXH2")
                 //ShiftName
                 .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
                 .AndIF(!string.IsNullOrEmpty(reqModel.Formula), a => a.Formula != null && a.Formula.Contains(reqModel.Formula))
                 .AndIF(reqModel.StartTime.HasValue, a => a.CreateDate >= reqModel.StartTime)
                 .AndIF(reqModel.EndTime.HasValue, a => a.CreateDate <= reqModel.EndTime)
                 .AndIF(reqModel.DataType == "1", a => a.Quantity >= 0)
                 .AndIF(reqModel.DataType == "-1", a => a.Quantity < 0)
               .ToExpression();
            }




            var data2 = await _dal.Db.Queryable<ProductionHistoryViewEntity>().
          Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();

            ProductionHistoryViewEntityModel historyViewEntityModel = new ProductionHistoryViewEntityModel();
            historyViewEntityModel.historyViewEntities = rDat;
            if (data2 != null)
            {
                historyViewEntityModel.Total = data2.Sum(p => p.Quantity).Value;
            }
            else
            {
                historyViewEntityModel.Total = 0;
            }
            List<ProductionHistoryViewEntityModel> list = new List<ProductionHistoryViewEntityModel>();
            list.Add(historyViewEntityModel);
            result.dataCount = data2.Count;
            result.data = list;
            return result;

            //var data = await _dal.Db.Queryable<ProductionHistoryViewEntity>()
            //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data;
            //return result;
        }


        public async Task<MessageModel<string>> GetReverseQty(ReverseModel reqModel)
        {

            string userID = _user.Name.ToString();
            var result = new MessageModel<string>();
            result.success = true;
            try
            {
                //获取当前行实际产出数据
                var pae = await _dal2.FindEntity(reqModel.ID);
                if (pae == null)
                {
                    result.msg = "0";
                }

                decimal returnQty = pae.Quantity.Value;
                //拿到当前数据的SSCC
                var history = await _dal.FindEntity(p => p.ID == reqModel.ID);
                string sscc = history.SublotId;
                if (!string.IsNullOrEmpty(sscc))
                {
                    var qtyModel = await _dal.FindList(p => p.SublotId == sscc && p.Quantity.Value <= 0);
                    if (qtyModel != null)
                    {
                        //returnQty = returnQty - qtyModel.Sum(p => Math.Abs(p.Quantity.Value));
                        if (returnQty < 0)
                        {
                            result.msg = "0";

                        }
                        else
                        {
                            result.msg = returnQty.ToString();
                        }
                    }
                    else
                    {
                        result.msg = returnQty.ToString();

                    }
                }
                else
                {
                    result.msg = returnQty.ToString();

                }
                return result;
            }
            catch (Exception)
            {
                result.success = false;
                result.msg = "0";
                return result;
            }

        }


        /// <summary>
        /// 根据节点插叙是否管理库存（返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存）
        /// </summary>
        /// <param name="equipmentID"></param>
        /// <returns></returns>
        public async Task<string> GetEquipmentStorege(string equipmentID)
        {

            string typeEquipmentID = equipmentID;
            var api_equipmentRequirement = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { EquipmentId = typeEquipmentID });
            DFM.Model.Models.EquipmentStorageEntity storage = api_equipmentRequirement.response.Find(p => p.EquipmentId == typeEquipmentID);
            if (storage == null)
            {
                return string.Empty;
            }

            var api_equirement = await HttpHelper.GetApiAsync<DFM.Model.Models.EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + storage.EquipmentRequirementId, _user.GetToken(), null);
            var equirement = api_equirement.response;
            if (equirement == null)
            {
                return string.Empty;
            }
            else
            {
                var sapStorageType = equirement.ManageInventory;

                if (string.IsNullOrEmpty(sapStorageType))
                {
                    return equirement.Code + ";" + "空";
                }
                return equirement.Code + ";" + sapStorageType;
            }
        }


        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> Reverse(ReverseModel reqModel)
        {
            string userID = _user.Name.ToString();
            var result = new MessageModel<string>();
            result.success = false;
            //获取storagetype类型
            var pve = await _dal.FindEntity(reqModel.ID);
            //获取当前行实际产出数据
            var pae = await _dal2.FindEntity(reqModel.ID);

            #region 判断是否满足条件

            if (pve == null || pae == null)
            {
                result.msg = "不存在对应的反冲数据";
                //result.success = false;
                return result;
            }

            //这里判断（是否管理库存）
            //string equpT = await GetEquipmentStorege(pve.DestinationId);

            //string[] strArray = equpT.Split(';').ToArray();
            //if (strArray == null || strArray.Length <= 1)
            //{
            //    result.msg = "未配置是否管理库存节点";
            //    return result;
            //}
            //string storegeState = strArray[1];
            string storegeState = "1";
            //管理库存
            if (storegeState == "1")
            {
                //拿到剩余值
                var reuslt = await GetReverseQty(reqModel);

                if (reqModel.ReverseQuantity > Convert.ToDecimal(reuslt.msg))
                {
                    result.msg = "反冲数量大于库存数量";
                    //result.success = false;
                    return result;
                }
            }
            else//不管理库存 
            {

            }

            #endregion

            var list = new List<PoProducedActualEntity>();
            var model = _mapper.Map<PoProducedActualEntity>(pae);
            var olgQuantity = pae.Quantity;
            model.Quantity = -pae.Quantity;
            model.ReasonCode = reqModel.ReasonCode;
            model.Comment = reqModel.Comment;
            model.SendExternal = 0;//SAP状态默认为0未发送
            model.Type = null;
            model.Mjahr = null;
            model.Mblnr = null;
            model.Zeile = null;
            model.Msg = null;
            model.Pid = pae.ID;
            model.CreateCustomGuid(_user.Name.ToString());
            list.Add(model);
            PoProducedActualEntity model2 = null;
            if (olgQuantity > reqModel.ReverseQuantity)
            {
                model2 = new PoProducedActualEntity
                {
                    SendExternal = 0,//SAP状态默认为0未发送
                    Type = null,
                    Mjahr = null,
                    Mblnr = null,
                    Zeile = null,
                    Msg = null,
                    Pid = null,
                    TranNo = null,
                    Index = null
                };
                model2.ProductExecutionId = pae.ProductExecutionId;
                model2.ProductionOrderId = pae.ProductionOrderId;
                model2.PoProducedRequirementId = pae.PoProducedRequirementId;
                model2.CreateDate = pae.CreateDate;
                model2.LotId = pae.LotId;
                model2.LotStatus = pae.LotStatus;
                model2.SubLotId = pae.SubLotId; ;
                model2.SubLotStatus = pae.SubLotStatus; ;
                model2.SourceType = pae.SourceType; ;
                model2.EquipmentId = pae.EquipmentId;
                model2.DesinationEquipmentId = pae.DesinationEquipmentId;
                model2.UnitId = pae.UnitId;
                model2.TeamId = pae.TeamId;
                model2.ShiftId = pae.ShiftId;
                model2.Quantity = olgQuantity - reqModel.ReverseQuantity;
                model2.CreateCustomGuid(_user.Name.ToString());
                list.Add(model2);
            }

            _unitOfWork.BeginTran();
            result.success = await _dal2.Add(list) > 0;
            try
            {
                if (result.success)
                {
                    if (pae.SubLotId != "" && storegeState == "1")//需要管理库存才进行库存操作
                    {
                        var materialInventoryEntity = await _dal3.FindEntity(x => x.SublotId == pae.SubLotId);
                        //如果为空什么都不做
                        if (materialInventoryEntity == null)
                        {
                            //_unitOfWork.RollbackTran();
                            //result.msg = "未找到追溯码";
                            //return result;
                        }
                        else
                        {
                            //等于MES时判断是否需要删除
                            if (pve.StorageType == "MES")
                            {
                                decimal quantity = Math.Round(Convert.ToDecimal(materialInventoryEntity.Quantity), 3) - Math.Round(Convert.ToDecimal(reqModel.ReverseQuantity), 3);
                                //materialInventoryEntity.Quantity - reqModel.ReverseQuantity;
                                if (quantity <= 0)
                                {
                                    result.success = await _dal3.DeleteById(materialInventoryEntity.ID);
                                }
                                else
                                {
                                    materialInventoryEntity.Modify(materialInventoryEntity.ID, _user.Name.ToString());
                                    result.success = await _dal3.Update(materialInventoryEntity);
                                }
                            }
                            else
                            {
                                //SAP的要判断是否需要删除
                                if (pve.ManageInventory == "1")
                                {
                                    materialInventoryEntity.Quantity -= Math.Round(Convert.ToDecimal(reqModel.ReverseQuantity), 3);// reqModel.ReverseQuantity;
                                    if (materialInventoryEntity.Quantity <= 0)
                                    {
                                        result.success = await _dal3.DeleteById(materialInventoryEntity.ID);
                                    }
                                    else
                                    {
                                        materialInventoryEntity.Modify(materialInventoryEntity.ID, _user.Name.ToString());
                                        result.success = await _dal3.Update(materialInventoryEntity);
                                    }
                                }
                            }
                        }
                    }


                }
                if (result.success)
                {

                    //调用接口发送sap反冲数据接口地址api
                    //var ssccStrings1 = await HttpHelper.PostAsync<string>("BASE", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                    List<PrintSendDataItems> printList = new List<PrintSendDataItems>();


                    PrintSendDataItems dataitems = new PrintSendDataItems();
                    dataitems.itemcode = pve.MaterialCode;
                    dataitems.itemunitcode = pve.Unit;
                    dataitems.qty = reqModel.ReverseQuantity;
                    dataitems.workshopStockcode = pve.Destination;
                    dataitems.type = 2;

                    printList.Add(dataitems);

                    var printReuslt = await PrintLabel(printList);
                    if (printReuslt.successed == false)
                    {
                        result.msg = "库存反冲成功，接口访问失败";
                        result.success = false;
                        _unitOfWork.RollbackTran();
                        return result;
                    }
                    //if (printReuslt.Response.flag == false)
                    //{
                    //    result.msg = "库存反冲成功" + printReuslt.Response.msg;
                    //    return result;
                    //}
                }
                else
                {
                    _unitOfWork.RollbackTran();
                }

                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "反冲成功";
                //try
                //{
                //	if (model != null)
                //	{
                //		await _produceViewServices.ProduceReport(new List<string>() { model.ID });
                //	}
                //	if (model2 != null)
                //	{
                //		await _produceViewServices.ProduceReverseReport(new List<string>() { model2.ID });
                //	}
                //}
                //catch (Exception)
                //{

                //}
                return result;
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.success = false;
                result.msg = ex.Message;
            }
            return result;
        }


        /// <summary>
        /// 报工重发
        /// </summary>
        /// <param name="id"></param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> RepeatPoProducedActual(string[] id)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                List<PoProducedActualEntity> upList = new List<PoProducedActualEntity>();

                for (Int32 i = 0; i < id.Length; i++)
                {
                    //查询数据
                    var data = await _dal2.QueryById(id[i]);
                    if (data != null)
                    {
                        data.SendExternal = 3;
                        data.Modify(id[i], _user.ToString());
                        upList.Add(data);
                    }
                }
                _unitOfWork.BeginTran();

                bool dataBase = await _dal2.Update(upList);
                if (dataBase == true)
                {
                    result.success = true;
                    result.msg = "重发成功";
                }

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();

            }


            return result;
        }


        #region 标签打印接口

        #region 请求

        /// <summary>
        /// 打印标签
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PrintResult>> PrintLabel(List<PrintSendDataItems> model)
        {
            string json = JsonConvert.SerializeObject(model);
            string sendMsg = "key=WCS&data=" + json;
            var response = await _esbHelper.PostString<PrintResult>("WMS_PrintTag", sendMsg);
            return response;
        }

        #endregion

        #endregion

        #region WMS标签创建

        /// <summary>
        /// 去掉从左往右0数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string KeepNonZeroNumbers(string input)
        {
            // 匹配一个非0的数字后面跟任意多的数字
            string pattern = @"(^|[^0-9])[1-9][0-9]*([^0-9]|$)";

            // 使用正则表达式替换掉前导0
            return Regex.Replace(input, pattern, m => m.Groups[1].Value + m.Groups[2].Value);
        }


        /// <summary>
        /// 有效期换算
        /// </summary>
        /// <param name="addNumber">增加数</param>
        /// <param name="type">单位</param>
        /// <param name="dateTime">传入时间</param>
        /// <returns></returns>
        public DateTime GetExpirationDate(int addNumber, string type, DateTime dateTime)
        {

            switch (type)
            {
                case "Y":
                    {
                        return dateTime.AddYears(addNumber);
                    }
                case "Q":
                    {
                        return dateTime.AddYears(addNumber * 3);
                    }
                case "M":
                    {
                        return dateTime.AddMonths(addNumber);
                    }
                case "W":
                    {
                        return dateTime.AddDays(addNumber * 7);
                    }
                case "D":
                    {
                        return dateTime.AddDays(addNumber);
                    }
                case "h":
                    {
                        return dateTime.AddHours(addNumber);
                    }
                case "m":
                    {
                        return dateTime.AddMinutes(addNumber);
                    }
                case "s":
                    {
                        return dateTime.AddSeconds(addNumber);
                    }
                default:
                    {
                        return dateTime.AddDays(addNumber);
                    }
            }
        }

        public async Task<MessageModel<string>> ScanWMSPrint(string sscc)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断

                if (string.IsNullOrEmpty(sscc))
                {
                    result.msg = "请确认扫码编码规则是否存在";
                    return result;
                }

                if (sscc.Length != 31)
                {
                    result.msg = "追溯码缺失数字";
                    return result;
                }



                #endregion

                #region 参数和实体

                string lotID = string.Empty;
                string mid = string.Empty;
                DateTime expirationDate = DateTime.Now;
                string poID = string.Empty;
                string po_reID = string.Empty;
                string subLotID = string.Empty;
                string unitID = string.Empty;
                string lotState = "2";
                string sbLotState = "3";
                List<MaterialLotEntity> inserLotList = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> inserSubLotList = new List<MaterialSubLotEntity>();
                List<PoProducedActualEntity> insertPoProduced = new List<PoProducedActualEntity>();
                List<MaterialInventoryEntity> inserMaterialInventoryList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> tranList = new List<MaterialTransferEntity>();
                #endregion

                #region 解析判断数据库对应关系

                sscc = sscc.Trim();
                //工单
                string proNo = sscc.Substring(0, 12);
                //生产批次号
                string lotNo = Regex.Replace(sscc.Substring(12, 10), "^0+", "");
                //数量
                string partNumber = Regex.Replace(sscc.Substring(22, 5), "^0+", "");  //KeepNonZeroNumbers(sscc.Substring(23, 5));
                                                                                      //板数
                string bNumber = Regex.Replace(sscc.Substring(sscc.Length - 4, 4), "^0+", "");//KeepNonZeroNumbers(sscc.Substring(sscc.Length-4, 4));

                #region 判断工单

                // QA_Approve  7  
                // Running 6  
                //green
                //Holding 5  
                //yellow
                //Discard 4   
                //red
                //COMPLETE    3  
                //#FFA500	
                //RELEASED    2   
                //gray
                // Scheduled   1   
                //blue
                // New 0

                //找工单（状态为）
                List<ProductionOrderEntity> proList = await _ProductionOrderEntityDal.FindList(p => p.ProductionOrderNo == proNo && (p.PoStatus == "3" || p.PoStatus == "6"));
                proList = proList.OrderBy(P => P.CreateDate).ToList();
                if (proList == null || proList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                poID = proList[0].ID;
                string idshfit = proList[0].PrepareShiftid;
                var poReList = await _PoProducedRequirementEntityDal.FindList(p => p.ProductionOrderId == proList[0].ID);
                poReList = poReList.OrderBy(P => P.CreateDate).ToList();
                if (poReList == null || poReList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                po_reID = poReList[0].ID;
                mid = poReList[0].MaterialId;
                unitID = poReList[0].UnitId;
                #endregion

                #region 判断板号

                var poActualList = await _dal2.FindList(p => p.PalletIdentifier == bNumber);
                if (poActualList == null && poActualList.Count > 0)
                {
                    result.msg = "当前板号数据已存在";
                    return result;
                }

                #endregion

                #region 判断批次信息是否存在，并拿批次ID                

                //新增批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotNo && x.MaterialId == mid);
                if (lotList != null && lotList.Count > 0)
                {
                    lotID = lotList[0].ID;
                    lotState = lotList[0].ExternalStatus;
                }
                else
                {
                    #region 有效期

                    MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mid);
                    if (materialData != null)
                    {
                        string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                        int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                        expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                    }

                    #endregion

                    ////这里查询物料属性
                    //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mid);
                    //if (property != null)
                    //{
                    //	string propertyValue = property.PropertyValue;
                    //	if (propertyValue != string.Empty)
                    //	{
                    //		string type = property.MaterialType;
                    //		int value = Convert.ToInt32(propertyValue);
                    //		expirationDate = expirationDate.AddDays(value);
                    //	}
                    //}
                    //保存批次
                    MaterialLotEntity model = new MaterialLotEntity();
                    model.Create(_user.Name.ToString());
                    lotID = model.ID;
                    model.LotId = lotNo;
                    model.MaterialId = mid;
                    model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                    model.ExternalStatus = "2"; //(1:B 上锁 2)
                    model.Type = "0";
                    // request.LotId = lotID;
                    inserLotList.Add(model);

                }

                #endregion

                #region 判断子批次

                //新增子批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == sscc);
                if (subLotList != null && subLotList.Count > 0)
                {
                    result.msg = "子批次存在重复数据";
                    return result;
                }
                else
                {
                    MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                    submodel.Create(_user.Name.ToString());
                    subLotID = submodel.ID;
                    submodel.SubLotId = sscc;
                    string subLotCode = String.Empty;

                    #region 构造实体

                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _user.GetToken();
                    //var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                    //if (ssccString.success == true)
                    //{
                    //    //ssccString = ssccString.response;
                    //    subLotCode = ssccString.response.ToString();
                    //}

                    //submodel.SubLotId = subLotCode;

                    #endregion

                    submodel.ExternalStatus = "3";
                    submodel.Type = "0";
                    inserSubLotList.Add(submodel);
                }

                #endregion

                #endregion


                #region 判断执行工单

                var exData = await _PoProducedExecutionEntitydal.FindList(p => p.ProductionOrderId == poID);

                if (exData == null)
                {
                    result.msg = "请确认是否有执行工单";
                    return result;

                }
                var orderData = exData.OrderByDescending(p => p.CreateDate).ToList();
                #endregion

                #region 创建产出历史

                PoProducedActualEntity poProducedActualEntity = new PoProducedActualEntity();
                poProducedActualEntity.Create(_user.Name.ToString());
                poProducedActualEntity.ProductionOrderId = poID;
                poProducedActualEntity.PoProducedRequirementId = po_reID;
                poProducedActualEntity.ProductExecutionId = orderData[0].ID;


                string eCode = await GetEqupmentID(orderData[0].RunEquipmentId);
                if (string.IsNullOrEmpty(eCode))
                {
                    var model = await _EquipmentEntityDal.FindEntity(p => p.ID == orderData[0].RunEquipmentId);
                    result.msg = "请确认工厂节点是否配置Associated Node" + "执行设备：" + model.EquipmentName;
                    return result;

                }
                var eEntity = await _EquipmentEntityDal.FindEntity(p => p.EquipmentCode == eCode);
                //var eEntity = await _EquipmentEntityDal.FindEntity(p => p.EquipmentCode.Contains("PSA_FG09"));
                if (eEntity == null)
                {
                    result.msg = $"物料模型未找到设备({eCode})，请检查配置Associated Node";
                    return result;
                }
                poProducedActualEntity.EquipmentId = eEntity == null ? "" : eEntity.ID;
                poProducedActualEntity.DesinationEquipmentId = eEntity == null ? "" : eEntity.ID;
                poProducedActualEntity.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                poProducedActualEntity.UnitId = unitID;
                poProducedActualEntity.LotId = lotID;
                poProducedActualEntity.LotStatus = Convert.ToInt32(lotState == string.Empty ? "2" : lotState);
                poProducedActualEntity.SubLotId = subLotID;
                poProducedActualEntity.ShiftId = idshfit;
                poProducedActualEntity.SubLotStatus = 3;
                poProducedActualEntity.PalletIdentifier = bNumber;
                poProducedActualEntity.SourceType = 0;
                poProducedActualEntity.SendExternal = 0;
                poProducedActualEntity.Deleted = 0;

                insertPoProduced.Add(poProducedActualEntity);

                #endregion

                #region 创建库存

                #region 创建库存

                MaterialInventoryEntity request = new MaterialInventoryEntity();

                //创建数据
                request.Create(_user.Name.ToString());
                //默认是kg
                request.QuantityUomId = unitID;
                request.EquipmentId = eEntity.ID;
                request.QuantityUomId = unitID;//"5";
                request.LotId = lotID;
                request.SublotId = subLotID;
                request.ContainerId = "";
                request.StorageLocation = "";
                request.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                inserMaterialInventoryList.Add(request);

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldLotId = "";

                trans.OldSublotId = "";

                trans.OldExpirationDate = null;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3); // Convert.ToDecimal(Quantity);
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Create Inventory";
                trans.Comment = "产出-创建库存";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = "";
                trans.NewEquipmentId = eEntity.ID;
                trans.OldContainerId = "";
                trans.NewContainerId = "";
                trans.OldLotExternalStatus = "";
                trans.OldSublotExternalStatus = "";
                trans.NewMaterialId = mid;
                trans.OldMaterialId = mid;
                trans.NewLotExternalStatus = lotState;
                trans.NewSublotExternalStatus = sbLotState;

                tranList.Add(trans);

                #endregion

                #endregion

                #endregion


                _unitOfWork.BeginTran();

                bool insertLotResult = true;
                bool insertSubLotResult = true;
                bool insertPoProducedResult = true;

                bool insertInvent = true;
                bool inserttRan = true;

                _unitOfWork.RollbackTran();
                if (inserLotList.Count > 0)
                {
                    insertLotResult = await _materialLotEntityDal.Add(inserLotList) > 0;
                }
                if (inserSubLotList.Count > 0)
                {
                    insertSubLotResult = await _materialSubLotServicesDal.Add(inserSubLotList) > 0;
                }
                if (insertPoProduced.Count > 0)
                {
                    insertPoProducedResult = await _dal2.Add(insertPoProduced) > 0;
                }
                if (inserMaterialInventoryList.Count > 0)
                {
                    insertInvent = await _MaterialInventoryEntitydal.Add(inserMaterialInventoryList) > 0;
                }
                if (tranList.Count > 0)
                {
                    inserttRan = await _MaterialTransferEntitydal.Add(tranList) > 0;
                }

                if (insertLotResult != true || insertSubLotResult != true || insertPoProducedResult != true || insertInvent != true || inserttRan != true)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "产出失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "产出消耗成功";

                return result;
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
                return result;

            }


        }

        public async Task<MessageModel<string>> ScanWMSPrintGBZ(string sscc)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断
                sscc = sscc.Trim();
                if (string.IsNullOrEmpty(sscc))
                {
                    result.msg = "请确认扫码编码规则是否存在";
                    return result;
                }

                if (sscc.Length != 31)
                {
                    result.msg = "追溯码缺失数字";
                    return result;
                }



                #endregion

                #region 参数和实体

                string lotID = string.Empty;
                string mid = string.Empty;
                DateTime expirationDate = DateTime.Now;
                string poID = string.Empty;
                string po_reID = string.Empty;
                string subLotID = string.Empty;
                string unitID = string.Empty;
                string lotState = "2";
                string sbLotState = "3";
                List<MaterialLotEntity> inserLotList = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> inserSubLotList = new List<MaterialSubLotEntity>();
                List<PoProducedActualEntity> insertPoProduced = new List<PoProducedActualEntity>();

                List<MaterialInventoryEntity> inserMaterialInventoryList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> tranList = new List<MaterialTransferEntity>();
                #endregion

                #region 解析判断数据库对应关系

                sscc = sscc.Trim();
                //工单
                string proNo = sscc.Substring(0, 12);
                //生产批次号
                string lotNo = Regex.Replace(sscc.Substring(12, 10), "^0+", "");
                //数量
                string partNumber = Regex.Replace(sscc.Substring(22, 5), "^0+", "");  //KeepNonZeroNumbers(sscc.Substring(23, 5));
                                                                                      //板数
                string bNumber = Regex.Replace(sscc.Substring(sscc.Length - 4, 4), "^0+", "");//KeepNonZeroNumbers(sscc.Substring(sscc.Length-4, 4));

                #region 判断工单

                // QA_Approve  7  
                // Running 6  
                //green
                //Holding 5  
                //yellow
                //Discard 4   
                //red
                //COMPLETE    3  
                //#FFA500	
                //RELEASED    2   
                //gray
                // Scheduled   1   
                //blue
                // New 0

                //找工单（状态为）
                List<ProductionOrderEntity> proList = await _ProductionOrderEntityDal.FindList(p => p.ProductionOrderNo == proNo && (p.PoStatus == "3" || p.PoStatus == "6"));
                proList = proList.OrderBy(P => P.CreateDate).ToList();
                if (proList == null || proList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                string pNo = string.Empty;
                pNo = proList[0].ProductionOrderNo;
                poID = proList[0].ID;
                string idshfit = proList[0].PrepareShiftid;
                var poReList = await _PoProducedRequirementEntityDal.FindList(p => p.ProductionOrderId == proList[0].ID);
                poReList = poReList.OrderBy(P => P.CreateDate).ToList();
                if (poReList == null || poReList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                po_reID = poReList[0].ID;
                mid = poReList[0].MaterialId;
                unitID = poReList[0].UnitId;
                #endregion

                #region 判断板号

                var poActualList = await _dal2.FindList(p => p.PalletIdentifier == bNumber);
                if (poActualList == null && poActualList.Count > 0)
                {
                    result.msg = "当前板号数据已存在";
                    return result;
                }

                #endregion

                #region 判断批次信息是否存在，并拿批次ID                

                //新增批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotNo && x.MaterialId == mid);
                if (lotList != null && lotList.Count > 0)
                {
                    lotID = lotList[0].ID;
                    lotState = lotList[0].ExternalStatus;
                }
                else
                {
                    #region 有效期

                    MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mid);
                    if (materialData != null)
                    {
                        string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                        int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                        expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                    }

                    #endregion

                    ////这里查询物料属性
                    //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mid);
                    //if (property != null)
                    //{
                    //    string propertyValue = property.PropertyValue;
                    //    if (propertyValue != string.Empty)
                    //    {
                    //        string type = property.MaterialType;
                    //        int value = Convert.ToInt32(propertyValue);
                    //        expirationDate = expirationDate.AddDays(value);
                    //    }
                    //}
                    //保存批次
                    MaterialLotEntity model = new MaterialLotEntity();
                    model.Create(_user.Name.ToString());
                    lotID = model.ID;
                    model.LotId = lotNo;
                    model.MaterialId = mid;
                    model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                    model.ExternalStatus = "2"; //(1:B 上锁 2)
                    model.Type = "0";
                    // request.LotId = lotID;
                    inserLotList.Add(model);

                }

                #endregion

                #region 判断子批次

                //新增子批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == sscc);
                if (subLotList != null && subLotList.Count > 0)
                {
                    result.msg = "子批次存在重复数据";
                    return result;
                }
                else
                {
                    MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                    submodel.Create(_user.Name.ToString());
                    subLotID = submodel.ID;
                    submodel.SubLotId = sscc;
                    string subLotCode = String.Empty;

                    #region 构造实体

                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _user.GetToken();
                    //var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                    //if (ssccString.success == true)
                    //{
                    //    //ssccString = ssccString.response;
                    //    subLotCode = ssccString.response.ToString();
                    //}

                    //submodel.SubLotId = subLotCode;

                    #endregion

                    submodel.ExternalStatus = "3";
                    submodel.Type = "0";
                    inserSubLotList.Add(submodel);
                }

                #endregion

                #endregion


                #region 判断执行工单

                var exData = await _PoProducedExecutionEntitydal.FindList(p => p.ProductionOrderId == poID);

                if (exData == null)
                {
                    result.msg = "请确认是否有执行工单";
                    return result;

                }
                var orderData = exData.OrderByDescending(p => p.CreateDate).ToList();
                #endregion

                #region 创建产出历史

                PoProducedActualEntity poProducedActualEntity = new PoProducedActualEntity();
                poProducedActualEntity.Create(_user.Name.ToString());
                poProducedActualEntity.ProductionOrderId = poID;
                poProducedActualEntity.PoProducedRequirementId = orderData[0].PoProducedRequirementId;
                //poProducedActualEntity.PoProducedRequirementId = po_reID;
                poProducedActualEntity.ProductExecutionId = orderData[0].ID;

                //string eCode = await GetEqupmentID(orderData[0].RunEquipmentId);
                //if (string.IsNullOrEmpty(eCode))
                //{
                //    var model = await _EquipmentEntityDal.FindEntity(p => p.ID == orderData[0].RunEquipmentId);
                //    result.msg = "请确认工厂节点是否配置Associated Node" + "执行设备：" + model.EquipmentName;

                //    //               result.msg = "请确认工厂节点是否配置Associated Node" + "执行ID" + orderData[0].RunEquipmentId;
                //    return result;
                //}
                //var eEntity = await _EquipmentEntityDal.FindEntity(p => p.EquipmentCode == eCode);//PSA_FG09
                //if (eEntity == null)
                //{
                //    result.msg = $"物料模型未找到设备({eCode})，请检查配置Associated Node";
                //    return result;
                //}

                #region 新逻辑

                var sapModel = await _dalSappackorderEntity.FindEntity(p => p.Aufnr == pNo);
                if (sapModel == null)
                {
                    result.msg = $"工单未找到({pNo})Sap工单Sappackorder数据，请检查";
                    return result;
                }

                string equpmentCode = sapModel.Lgort.Trim();
                if (string.IsNullOrEmpty(equpmentCode))
                {
                    result.msg = "SAP返回节点为空" + sapModel.Lgort;
                    return result;
                }
                equpmentCode = "PSA_" + equpmentCode;

                //拿equpment  
                var eqModel = await _EquipmentEntityDal.FindEntity(p => p.EquipmentCode == equpmentCode);
                if (eqModel == null)
                {
                    result.msg = "未找到设备，设备code" + equpmentCode;
                    return result;
                }

                #endregion


                poProducedActualEntity.EquipmentId = sapModel == null ? "" : eqModel.ID;
                poProducedActualEntity.DesinationEquipmentId = sapModel == null ? "" : eqModel.ID;
                poProducedActualEntity.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                poProducedActualEntity.UnitId = unitID;
                poProducedActualEntity.LotId = lotID;
                poProducedActualEntity.LotStatus = Convert.ToInt32(lotState == string.Empty ? "2" : lotState);
                poProducedActualEntity.SubLotId = subLotID;
                poProducedActualEntity.ShiftId = idshfit;
                poProducedActualEntity.SubLotStatus = 3;
                poProducedActualEntity.PalletIdentifier = bNumber;
                poProducedActualEntity.SourceType = 0;
                poProducedActualEntity.SendExternal = 0;
                poProducedActualEntity.Deleted = 0;

                insertPoProduced.Add(poProducedActualEntity);

                #endregion

                #region 创建库存

                #region 创建库存

                MaterialInventoryEntity request = new MaterialInventoryEntity();

                //创建数据
                request.Create(_user.Name.ToString());
                //默认是kg
                request.QuantityUomId = unitID;
                request.EquipmentId = eqModel.ID;
                request.QuantityUomId = unitID;//"5";
                request.LotId = lotID;
                request.SublotId = subLotID;
                request.ContainerId = "";
                request.StorageLocation = "";
                request.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                inserMaterialInventoryList.Add(request);

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldLotId = "";

                trans.OldSublotId = "";

                trans.OldExpirationDate = null;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3); // Convert.ToDecimal(Quantity);
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Create Inventory";
                trans.Comment = "产出-创建库存";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = "";
                trans.NewEquipmentId = eqModel.ID;
                trans.OldContainerId = "";
                trans.NewContainerId = "";
                trans.OldLotExternalStatus = "";
                trans.OldSublotExternalStatus = "";
                trans.NewMaterialId = mid;
                trans.OldMaterialId = mid;
                trans.NewLotExternalStatus = lotState;
                trans.NewSublotExternalStatus = sbLotState;

                tranList.Add(trans);

                #endregion

                #endregion

                #endregion

                _unitOfWork.BeginTran();

                bool insertLotResult = true;
                bool insertSubLotResult = true;
                bool insertPoProducedResult = true;

                bool insertInvent = true;
                bool inserttRan = true;

                _unitOfWork.RollbackTran();
                if (inserLotList.Count > 0)
                {
                    insertLotResult = await _materialLotEntityDal.Add(inserLotList) > 0;
                }
                if (inserSubLotList.Count > 0)
                {
                    insertSubLotResult = await _materialSubLotServicesDal.Add(inserSubLotList) > 0;
                }
                if (insertPoProduced.Count > 0)
                {
                    insertPoProducedResult = await _dal2.Add(insertPoProduced) > 0;
                }

                if (inserMaterialInventoryList.Count > 0)
                {
                    insertInvent = await _MaterialInventoryEntitydal.Add(inserMaterialInventoryList) > 0;
                }
                if (tranList.Count > 0)
                {
                    inserttRan = await _MaterialTransferEntitydal.Add(tranList) > 0;
                }

                if (insertLotResult != true || insertSubLotResult != true || insertPoProducedResult != true || insertInvent != true || inserttRan != true)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "产出失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "产出消耗成功";

                return result;
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
                return result;

            }


        }

        public async Task<MessageModel<string>> ScanWMSPrintGBZOLD(string sscc)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断
                sscc = sscc.Trim();
                if (string.IsNullOrEmpty(sscc))
                {
                    result.msg = "请确认扫码编码规则是否存在";
                    return result;
                }

                if (sscc.Length != 31)
                {
                    result.msg = "追溯码缺失数字";
                    return result;
                }



                #endregion

                #region 参数和实体

                string lotID = string.Empty;
                string mid = string.Empty;
                DateTime expirationDate = DateTime.Now;
                string poID = string.Empty;
                string po_reID = string.Empty;
                string subLotID = string.Empty;
                string unitID = string.Empty;
                string lotState = "2";
                string sbLotState = "3";
                List<MaterialLotEntity> inserLotList = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> inserSubLotList = new List<MaterialSubLotEntity>();
                List<PoProducedActualEntity> insertPoProduced = new List<PoProducedActualEntity>();

                List<MaterialInventoryEntity> inserMaterialInventoryList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> tranList = new List<MaterialTransferEntity>();
                #endregion

                #region 解析判断数据库对应关系

                sscc = sscc.Trim();
                //工单
                string proNo = sscc.Substring(0, 12);
                //生产批次号
                string lotNo = Regex.Replace(sscc.Substring(12, 10), "^0+", "");
                //数量
                string partNumber = Regex.Replace(sscc.Substring(22, 5), "^0+", "");  //KeepNonZeroNumbers(sscc.Substring(23, 5));
                                                                                      //板数
                string bNumber = Regex.Replace(sscc.Substring(sscc.Length - 4, 4), "^0+", "");//KeepNonZeroNumbers(sscc.Substring(sscc.Length-4, 4));

                #region 判断工单

                // QA_Approve  7  
                // Running 6  
                //green
                //Holding 5  
                //yellow
                //Discard 4   
                //red
                //COMPLETE    3  
                //#FFA500	
                //RELEASED    2   
                //gray
                // Scheduled   1   
                //blue
                // New 0

                //找工单（状态为）
                List<ProductionOrderEntity> proList = await _ProductionOrderEntityDal.FindList(p => p.ProductionOrderNo == proNo && (p.PoStatus == "3" || p.PoStatus == "6"));
                proList = proList.OrderBy(P => P.CreateDate).ToList();
                if (proList == null || proList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                poID = proList[0].ID;
                string idshfit = proList[0].PrepareShiftid;
                var poReList = await _PoProducedRequirementEntityDal.FindList(p => p.ProductionOrderId == proList[0].ID);
                poReList = poReList.OrderBy(P => P.CreateDate).ToList();
                if (poReList == null || poReList.Count <= 0)
                {
                    result.msg = "请确认单当前工单是否存在或工单状态是否正确";
                    return result;
                }
                po_reID = poReList[0].ID;
                mid = poReList[0].MaterialId;
                unitID = poReList[0].UnitId;
                #endregion

                #region 判断板号

                var poActualList = await _dal2.FindList(p => p.PalletIdentifier == bNumber);
                if (poActualList == null && poActualList.Count > 0)
                {
                    result.msg = "当前板号数据已存在";
                    return result;
                }

                #endregion

                #region 判断批次信息是否存在，并拿批次ID                

                //新增批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotNo && x.MaterialId == mid);
                if (lotList != null && lotList.Count > 0)
                {
                    lotID = lotList[0].ID;
                    lotState = lotList[0].ExternalStatus;
                }
                else
                {
                    #region 有效期

                    MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mid);
                    if (materialData != null)
                    {
                        string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                        int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                        expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                    }

                    #endregion

                    ////这里查询物料属性
                    //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mid);
                    //if (property != null)
                    //{
                    //    string propertyValue = property.PropertyValue;
                    //    if (propertyValue != string.Empty)
                    //    {
                    //        string type = property.MaterialType;
                    //        int value = Convert.ToInt32(propertyValue);
                    //        expirationDate = expirationDate.AddDays(value);
                    //    }
                    //}
                    //保存批次
                    MaterialLotEntity model = new MaterialLotEntity();
                    model.Create(_user.Name.ToString());
                    lotID = model.ID;
                    model.LotId = lotNo;
                    model.MaterialId = mid;
                    model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                    model.ExternalStatus = "2"; //(1:B 上锁 2)
                    model.Type = "0";
                    // request.LotId = lotID;
                    inserLotList.Add(model);

                }

                #endregion

                #region 判断子批次

                //新增子批次（判断是否存在批次信息，无创建，有无需操作）
                List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == sscc);
                if (subLotList != null && subLotList.Count > 0)
                {
                    result.msg = "子批次存在重复数据";
                    return result;
                }
                else
                {
                    MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                    submodel.Create(_user.Name.ToString());
                    subLotID = submodel.ID;
                    submodel.SubLotId = sscc;
                    string subLotCode = String.Empty;

                    #region 构造实体

                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _user.GetToken();
                    //var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                    //if (ssccString.success == true)
                    //{
                    //    //ssccString = ssccString.response;
                    //    subLotCode = ssccString.response.ToString();
                    //}

                    //submodel.SubLotId = subLotCode;

                    #endregion

                    submodel.ExternalStatus = "3";
                    submodel.Type = "0";
                    inserSubLotList.Add(submodel);
                }

                #endregion

                #endregion


                #region 判断执行工单

                var exData = await _PoProducedExecutionEntitydal.FindList(p => p.ProductionOrderId == poID);

                if (exData == null)
                {
                    result.msg = "请确认是否有执行工单";
                    return result;

                }
                var orderData = exData.OrderByDescending(p => p.CreateDate).ToList();
                #endregion

                #region 创建产出历史

                PoProducedActualEntity poProducedActualEntity = new PoProducedActualEntity();
                poProducedActualEntity.Create(_user.Name.ToString());
                poProducedActualEntity.ProductionOrderId = poID;
                poProducedActualEntity.PoProducedRequirementId = orderData[0].PoProducedRequirementId;
                //poProducedActualEntity.PoProducedRequirementId = po_reID;
                poProducedActualEntity.ProductExecutionId = orderData[0].ID;

                string eCode = await GetEqupmentID(orderData[0].RunEquipmentId);
                if (string.IsNullOrEmpty(eCode))
                {
                    var model = await _EquipmentEntityDal.FindEntity(p => p.ID == orderData[0].RunEquipmentId);
                    result.msg = "请确认工厂节点是否配置Associated Node" + "执行设备：" + model.EquipmentName;

                    //               result.msg = "请确认工厂节点是否配置Associated Node" + "执行ID" + orderData[0].RunEquipmentId;
                    return result;
                }
                var eEntity = await _EquipmentEntityDal.FindEntity(p => p.EquipmentCode == eCode);//PSA_FG09
                if (eEntity == null)
                {
                    result.msg = $"物料模型未找到设备({eCode})，请检查配置Associated Node";
                    return result;
                }
                poProducedActualEntity.EquipmentId = eEntity == null ? "" : eEntity.ID;
                poProducedActualEntity.DesinationEquipmentId = eEntity == null ? "" : eEntity.ID;
                poProducedActualEntity.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                poProducedActualEntity.UnitId = unitID;
                poProducedActualEntity.LotId = lotID;
                poProducedActualEntity.LotStatus = Convert.ToInt32(lotState == string.Empty ? "2" : lotState);
                poProducedActualEntity.SubLotId = subLotID;
                poProducedActualEntity.ShiftId = idshfit;
                poProducedActualEntity.SubLotStatus = 3;
                poProducedActualEntity.PalletIdentifier = bNumber;
                poProducedActualEntity.SourceType = 0;
                poProducedActualEntity.SendExternal = 0;
                poProducedActualEntity.Deleted = 0;

                insertPoProduced.Add(poProducedActualEntity);

                #endregion

                #region 创建库存

                #region 创建库存

                MaterialInventoryEntity request = new MaterialInventoryEntity();

                //创建数据
                request.Create(_user.Name.ToString());
                //默认是kg
                request.QuantityUomId = unitID;
                request.EquipmentId = eEntity.ID;
                request.QuantityUomId = unitID;//"5";
                request.LotId = lotID;
                request.SublotId = subLotID;
                request.ContainerId = "";
                request.StorageLocation = "";
                request.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3);
                inserMaterialInventoryList.Add(request);

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldLotId = "";

                trans.OldSublotId = "";

                trans.OldExpirationDate = null;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(partNumber), 3); // Convert.ToDecimal(Quantity);
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Create Inventory";
                trans.Comment = "产出-创建库存";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = "";
                trans.NewEquipmentId = eEntity.ID;
                trans.OldContainerId = "";
                trans.NewContainerId = "";
                trans.OldLotExternalStatus = "";
                trans.OldSublotExternalStatus = "";
                trans.NewMaterialId = mid;
                trans.OldMaterialId = mid;
                trans.NewLotExternalStatus = lotState;
                trans.NewSublotExternalStatus = sbLotState;

                tranList.Add(trans);

                #endregion

                #endregion

                #endregion

                _unitOfWork.BeginTran();

                bool insertLotResult = true;
                bool insertSubLotResult = true;
                bool insertPoProducedResult = true;

                bool insertInvent = true;
                bool inserttRan = true;

                _unitOfWork.RollbackTran();
                if (inserLotList.Count > 0)
                {
                    insertLotResult = await _materialLotEntityDal.Add(inserLotList) > 0;
                }
                if (inserSubLotList.Count > 0)
                {
                    insertSubLotResult = await _materialSubLotServicesDal.Add(inserSubLotList) > 0;
                }
                if (insertPoProduced.Count > 0)
                {
                    insertPoProducedResult = await _dal2.Add(insertPoProduced) > 0;
                }

                if (inserMaterialInventoryList.Count > 0)
                {
                    insertInvent = await _MaterialInventoryEntitydal.Add(inserMaterialInventoryList) > 0;
                }
                if (tranList.Count > 0)
                {
                    inserttRan = await _MaterialTransferEntitydal.Add(tranList) > 0;
                }

                if (insertLotResult != true || insertSubLotResult != true || insertPoProducedResult != true || insertInvent != true || inserttRan != true)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "产出失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "产出消耗成功";

                return result;
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                _unitOfWork.RollbackTran();
                return result;

            }


        }


        public async Task<string> GetEqupmentID(string equpmentID)
        {
            #region 拿设备id

            try
            {
                string runEqup = equpmentID;

                MessageModel<List<EquipmentActionModel>> apiResult_equipmentActionModels = await HttpHelper.PostAsync<List<EquipmentActionModel>>("DFM", "api/EquipmentAction/GetEquipmentAction", _user.GetToken(), new { EquipmentId = runEqup });
                var equipmentActionModels = apiResult_equipmentActionModels.response.Where(p => p.ActionCode == "Production").ToList();
                if (equipmentActionModels == null || equipmentActionModels.Count == 0)
                {
                    string msg = string.Format(@"接口地址:{0},传参:{1},返回结果{2}", "api/EquipmentAction/GetEquipmentAction", equpmentID, equipmentActionModels.Count);
                    SerilogServer.LogDebug(msg, "AssociatedNodeHis");
                    //result.msg = "equipmentActionModels为空";
                    return "";
                }
                string equpmentCode = string.Empty;
                for (int i = 0; i < equipmentActionModels.Count; i++)
                {
                    var aIDs = equipmentActionModels[i].ID;
                    MessageModel<List<EquipmentActionPropertyModel>> apiResult_equipmentActionProperties = await HttpHelper.PostAsync<List<EquipmentActionPropertyModel>>("DFM", "api/ActionPropertyValue/GetEquipmentActionProperty", _user.GetToken(), new { EquipmentId = runEqup, EquipmentActionId = aIDs });
                    var equipmentActionProperties = apiResult_equipmentActionProperties.response;
                    if (equipmentActionProperties != null || equipmentActionProperties.Count > 0)
                    {

                        var defaultValue = equipmentActionProperties.Where(p => p.ActualValue == "1" && p.PropertyCode == "Is Default").ToList();
                        if (defaultValue != null && defaultValue.Count > 0)
                        {
                            var proEquList = equipmentActionProperties.Where(p => p.PropertyCode == "Associated Node").ToList();
                            if (proEquList != null && proEquList.Count > 0)
                            {
                                equpmentCode = proEquList[0].ActualValue;
                                return equpmentCode;
                            }
                            else
                            {
                                string msg = string.Format(@"接口地址:{0},传参:{1},返回结果{2}", "api/ActionPropertyValue/GetEquipmentActionProperty", equpmentID + "equipmentActionProperties.Where(p => p.PropertyCode == \"Associated Node\")", proEquList.Count);
                                SerilogServer.LogDebug(msg, "AssociatedNodeHis");
                            }
                        }
                        else
                        {
                            string msg = string.Format(@"接口地址:{0},传参:{1},返回结果{2}", "api/ActionPropertyValue/GetEquipmentActionProperty", equpmentID + "equipmentActionProperties.Where(p => p.ActualValue == \"1\" && p.PropertyCode == \"Is Default\")", defaultValue.Count);
                            SerilogServer.LogDebug(msg, "AssociatedNodeHis");
                            continue;
                        }
                        //continue;
                        //result.msg = "equipmentActionProperties为空";
                        //return result;
                    }
                    else
                    {
                        string msg = string.Format(@"接口地址:{0},传参:{1},返回结果{2}", "api/ActionPropertyValue/GetEquipmentActionProperty", equpmentID, equipmentActionProperties);
                        SerilogServer.LogDebug(msg, "AssociatedNodeHis");
                    }
                }




                //var action = await HttpHelper.PostAsync<List<EquipmentActionModel>>("DFM", "api/EquipmentAction/GetEquipmentAction", _user.GetToken(), new { EquipmentId = runEqup });
                //if (action.success == true)
                //{
                //    var aList = action.response;
                //    if (aList != null && aList.Count > 0)
                //    {
                //        var aID = aList[0].ID;
                //        var actionP = await HttpHelper.PostAsync<List<EquipmentActionPropertyModel>>("DFM", "ap//ActionPropertyValue/GetEquipmentActionProperty", _user.GetToken(), new { Equipmentld = runEqup, EquipmentActionld = aID });
                //        if (actionP.success == true)
                //        {
                //            var pList = actionP.response;
                //            if (pList != null && pList.Count > 0)
                //            {
                //                var sList = pList.Where(p => p.DefaultValue == "1").ToList();
                //                if (sList != null && sList.Count > 0)
                //                {
                //                    return sList[0].EquipmentId;
                //                }
                //            }
                //            else
                //            {
                //                return "";
                //            }

                //        }
                //        else
                //        {
                //            return "";
                //        }
                //    }
                //    else
                //    {
                //        return "";
                //    }
                //}
                //else
                //{
                //    return "";
                //}
                return "";
            }
            catch (Exception)
            {

                return "";
            }

            #endregion
        }


        #endregion

    }
}
