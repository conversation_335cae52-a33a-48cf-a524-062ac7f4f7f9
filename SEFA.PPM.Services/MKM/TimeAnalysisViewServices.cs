
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using System;
using SEFA.PPM.Model.ViewModels.MKM.View;
using System.Data;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SEFA.Base.Common.WebApiClients.HttpApis;

namespace SEFA.PPM.Services
{
    public class TimeAnalysisViewServices : BaseServices<TimeAnalysisViewEntity>, ITimeAnalysisViewServices
    {
        private readonly IBaseRepository<TimeAnalysisViewEntity> _dal;
        private readonly IBaseRepository<TimeAnalysisIiViewEntity> _TimeAnalysisIiViewEntity;
        private readonly IBaseRepository<DowntimeCategroyEntity> _DowntimeCategroyEntity;
        public TimeAnalysisViewServices(IBaseRepository<TimeAnalysisViewEntity> dal, IBaseRepository<TimeAnalysisIiViewEntity> timeAnalysisIiViewEntity, IBaseRepository<DowntimeCategroyEntity> downtimeCategroyEntity)
        {
            _dal = dal;
            base.BaseDal = dal;
            _TimeAnalysisIiViewEntity = timeAnalysisIiViewEntity;
            _DowntimeCategroyEntity = downtimeCategroyEntity;
        }

        public async Task<List<TimeAnalysisViewEntity>> GetList(TimeAnalysisViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TimeAnalysisViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<TimeAnalysisViewEntity>> GetPageList(TimeAnalysisViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<TimeAnalysisViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(TimeAnalysisViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        #region NEW 计划、非计划停机时间分析（停机）
        public class AnalyzeD
        {
            public decimal Duration { get; set; }
            public string Category { get; set; }
            public string Reason { get; set; }
            public string RensonGroup { get; set; }
        }
        public static string GetAnalyzeDowntime1Sql(DateTime startTime, DateTime endTime,string DownTimeCategory, string MaterialCode, string FormulaType, string LineName, string WorkCenter, int GroupbyReason,int GroupbyReasonGroup,int GroupByFormula,int GroupByWorkCenter, int GroupByLine)
        { 
            return string.Format("exec [dbo].[sp_Report_GetDownTimeSummaryInfo] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}'", startTime, endTime, DownTimeCategory, MaterialCode, FormulaType, LineName, WorkCenter, GroupbyReason, GroupbyReasonGroup, GroupByFormula, GroupByWorkCenter, GroupByLine);
        }
        /// <summary>
        /// 计划、非计划停机时间分析（停机）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<AnalyzeDowntimeModel> AnalyzeDowntime(TimeAnalysisViewRequestModel reqModel)
        {
            var StartTime = new DateTime(reqModel.StartTime.Year, reqModel.StartTime.Month, reqModel.StartTime.Day, 00, 00, 00);
            var EndTime = new DateTime(reqModel.EndTime.Year, reqModel.EndTime.Month, reqModel.EndTime.Day, 23, 59, 59);
            //二级、三级
            var sql = GetAnalyzeDowntime1Sql(StartTime, EndTime, reqModel.ShutdownType, reqModel.MaterialCode, reqModel.FormulaType, reqModel.LineId, reqModel.WorkCenter, 1,1,0,0,0);
            var Model = await Task.Run(() =>
              _dal.Db.Ado.GetDataTable(sql)
            );
              List< AnalyzeD > analyzes = new List< AnalyzeD >();
            foreach (DataRow item in Model.Rows)
            {
                AnalyzeD analyzeD = new AnalyzeD();
                analyzeD.Duration = Convert.ToDecimal(item["Duration"]);
                analyzeD.Category = item["Category"].ToString();
                analyzeD.Reason = item["Reason"].ToString();
                analyzeD.RensonGroup= item["ReasonGroup"].ToString();
                analyzes.Add( analyzeD );
            }

            //Planned Stoppages
            //计划停机
            var data1 = await _TimeAnalysisIiViewEntity.FindList(p => p.Category == reqModel.ShutdownType);
            //二类
            var groupModel = data1.GroupBy(p => new { p.Groups, p.GroupsId }).Select(p => new { p.Key.Groups, p.Key.GroupsId }).ToList();
            //三类
            var reasonModel = data1.GroupBy(p => new { p.Reason, p.ReasonId, p.GroupsId }).Select(p => new { p.Key.Reason, p.Key.ReasonId, p.Key.GroupsId }).ToList();
            List<ReasonModels> models = new List<ReasonModels>();
            AnalyzeDowntimeModel model1 = new AnalyzeDowntimeModel();
            decimal? TotalaHours2 = 0;
            ////总停机时间
            var list = Math.Round(Convert.ToDecimal(analyzes.Sum(t => t.Duration)), 2);
            int id = 1;
            foreach (var item in groupModel)
            {
                List<ReasonModel> reasonModels = new List<ReasonModel>();
                ReasonModels model = new ReasonModels();
                decimal? Hours = 0;
                decimal? Hours2 = 0;
                var reansonList = reasonModel.Where(p => p.GroupsId == item.GroupsId).ToList();
                
                foreach (var item1 in reansonList)
                {
                    decimal? Hour = 0;
                    //查询单独三类时长
                    var reasonEntiy = analyzes.Where(p => p.Reason == item1.Reason && p.RensonGroup== item.Groups).FirstOrDefault();
                    ReasonModel reasonModel1 = new ReasonModel();
                    id=id += 1;
                    reasonModel1.id =id ;
                    reasonModel1.GroupName = item1.Reason;
                    if (reasonEntiy != null)
                    {
                        Hour = Math.Round(Convert.ToDecimal(reasonEntiy.Duration), 2);
                        Hours += Hour;
                    }
                    reasonModel1.Time1 = Hour;
                    decimal time2 = 0;
                    //计算时间2
                    if (list>0)
                    {
                        time2 = Math.Round(Convert.ToDecimal(Hour / list), 3);
                        Hours2 += time2;
                        TotalaHours2 += time2;
                    }
                    
                    reasonModel1.Time2 = time2;
                    reasonModels.Add(reasonModel1);
                }
                id = id += 1;
                model.id += id;
                model.GroupName = item.Groups;
                model.Time1 = Math.Round(Convert.ToDecimal(Hours), 2);
                model.Time2 = Math.Round(Convert.ToDecimal(Hours2), 3);
                List<ReasonList> reasonLists = new List<ReasonList>();
                foreach (var item1 in reasonModels)
                {

                    ReasonList reasonList = new ReasonList();
                    id = id += 1;
                    reasonList.id = id;
                    reasonList.GroupName = item1.GroupName;
                    reasonList.Time1 = Math.Round(Convert.ToDecimal(item1.Time1), 2);
                    reasonList.Time2 = Math.Round(Convert.ToDecimal(item1.Time2), 3);
                    reasonLists.Add(reasonList);
                }
                model.ReasonLists = reasonLists;
                models.Add(model);

            }
            model1.reasonModels = models;
            model1.TotalTime1 = list;
            model1.TotalTime2 = Math.Round(Convert.ToDecimal(TotalaHours2), 3);
            return model1;
        }



        /*
       /// <summary>
       /// 计划、非计划停机时间分析（停机）
       /// </summary>
       /// <param name="reqModel"></param>
       /// <returns></returns>
       public async Task<AnalyzeDowntimeModel> AnalyzeDowntime(TimeAnalysisViewRequestModel reqModel)
       {

           ////停机时间
           //var whereExpression = Expressionable.Create<TimeAnalysisViewEntity>()
           //     //.And(P=>P.LineId==reqModel.LineId)
           //     //.And(P=>P.StartTimeUtc>=reqModel.StartTime)
           //     //.And(p=>p.EndTimeUtc<=reqModel.EndTime)
           //     .AndIF(!string.IsNullOrEmpty(reqModel.LineId), a => a.LineId == reqModel.LineId)
           //      .AndIF(reqModel.StartTime!=null, a => a.StartTimeUtc >= reqModel.StartTime)
           //       .AndIF(reqModel.EndTime != null, a => a.EndTimeUtc <= reqModel.EndTime)
           //    .ToExpression();
           //var data = await _dal.FindList(whereExpression);
           //var datas= data.GroupBy(p =>new { p.ReasonId }).Select(p=>new { reasonId = p.Key.ReasonId,hour=p.Sum(t=>t.Hour) });

           ////总停机时间
           //var list=Math.Round(Convert.ToDecimal(data.Sum(t=>t.Hour)),2);

           //Planned Stoppages
           //计划停机
           var data1 = await _TimeAnalysisIiViewEntity.FindList(p=>p.Category== reqModel.ShutdownType);
           //二类
           var groupModel= data1.GroupBy(p=>new { p.Groups,p.GroupsId }).Select(p=> new { p.Key.Groups,p.Key.GroupsId }).ToList();
           //三类
           var reasonModel= data1.GroupBy(p=>new { p.Reason,p.ReasonId,p.GroupsId }).Select(p=> new { p.Key.Reason, p.Key.ReasonId ,p.Key.GroupsId}).ToList();
           //List<AnalyzeDowntimeModel> analyzeDowntimes = new List<AnalyzeDowntimeModel>();
           List<ReasonModels> models=new List<ReasonModels>();
           AnalyzeDowntimeModel model1 = new AnalyzeDowntimeModel();
           decimal? TotalaHours2 = 0;
           foreach (var item in groupModel)
           {
               List<ReasonModel> reasonModels = new List<ReasonModel>();
               //AnalyzeDowntimeModel model=new AnalyzeDowntimeModel();
               ReasonModels model=new ReasonModels();
               decimal? Hours = 0;
               decimal? Hours2 = 0;
               var reansonList = reasonModel.Where(p => p.GroupsId == item.GroupsId).ToList();

               foreach (var item1 in reansonList)
               {
                   decimal? Hour = 0;
                   //查询单独三类时长
                   var reasonEntiy = datas.Where(p => p.reasonId == item1.ReasonId).FirstOrDefault();
                   ReasonModel reasonModel1 = new ReasonModel();
                   reasonModel1.GroupName=item1.Reason;
                   if (reasonEntiy!=null)
                   {
                       Hour = Math.Round(Convert.ToDecimal(reasonEntiy.hour),2);
                       Hours += Hour;
                   }
                   reasonModel1.Time1=Hour;
                   //计算时间2
                   decimal time2 =Math.Round(Convert.ToDecimal( Hour / list),4);
                   Hours2+= time2;
                   TotalaHours2 += time2;
                   reasonModel1.Time2=time2;
                   reasonModels.Add(reasonModel1);
               }
               model.ReasonName = item.Groups;
               model.ReasonTime1 = Math.Round(Convert.ToDecimal(Hours),2);
               model.ReasonTime2 = Math.Round(Convert.ToDecimal(Hours2), 3);
               List<ReasonList> reasonLists = new List<ReasonList>();
               foreach (var item1 in reasonModels)
               {

                   ReasonList reasonList = new ReasonList();
                   reasonList.GroupName= item1.GroupName;
                   reasonList.Time1= Math.Round(Convert.ToDecimal(item1.Time1), 2);
                   reasonList.Time2 = Math.Round(Convert.ToDecimal(item1.Time2), 3);
                   reasonLists.Add( reasonList);
               }
               model.ReasonLists = reasonLists;
               models.Add(model);

           }
           model1.reasonModels = models;
           model1.TotalTime1 = list;
           model1.TotalTime2 = Math.Round(Convert.ToDecimal(TotalaHours2), 3);
          // analyzeDowntimes.Add(model1);

           //非计划停机
           //var data2 = await _TimeAnalysisIiViewEntity.FindList(p => p.Category == "Unplanned Stoppages");
           return model1;
       }
       */
        #endregion

    }
}