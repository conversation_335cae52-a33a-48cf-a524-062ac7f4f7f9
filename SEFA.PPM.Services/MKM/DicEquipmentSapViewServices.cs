
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class DicEquipmentSapViewServices : BaseServices<DicEquipmentSapViewEntity>, IDicEquipmentSapViewServices
    {
        private readonly IBaseRepository<DicEquipmentSapViewEntity> _dal;
        public DicEquipmentSapViewServices(IBaseRepository<DicEquipmentSapViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<DicEquipmentSapViewEntity>> GetList(DicEquipmentSapViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DicEquipmentSapViewEntity>().
                AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode.Equals(reqModel.EquipmentCode))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<DicEquipmentSapViewEntity>> GetPageList(DicEquipmentSapViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DicEquipmentSapViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(DicEquipmentSapViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}