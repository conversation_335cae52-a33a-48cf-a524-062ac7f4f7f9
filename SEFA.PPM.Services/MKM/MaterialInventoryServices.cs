
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Collections.Generic;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using SqlSugar;
using MongoDB.Driver;
using SEFA.Base.Model;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Common.WebApiClients.HttpApis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.HttpContextUser;


using System.Data.SqlTypes;
using System.Security.Cryptography;

using SEFA.Base.Common.HttpRestSharp;
using System.IO;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using static MongoDB.Driver.WriteConcern;

using SEFA.Base.Common.Common;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.Base.ESB;
using SEFA.DFM.Model.ViewModels;
using System.Text.RegularExpressions;
using System.Reflection;
using SEFA.PPM.Model.Models;

using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using Castle.MicroKernel.Registration;
using static SEFA.PTM.Services.ConsumeViewServices;
using SEFA.PPM.Model.ViewModels.MKM.View;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;
using System.Net.Sockets;
using System.Collections;
using System.Diagnostics;
using Microsoft.AspNetCore.Http;
using SEFA.PPM.Model.Models.Interface;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Numeric;
using SEFA.PPM.Model.ViewModels;
using Newtonsoft.Json.Linq;
using InfluxDB.Client.Api.Domain;
using System.Xml.Linq;
using OfficeOpenXml.Style;
using System.Net.NetworkInformation;
using Castle.Core.Internal;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;

using static SEFA.PPM.Services.TippingMlistViewServices;
using SEFA.PPM.IServices;
using SEFA.PPM.Services;
using StackExchange.Profiling.Internal;
using Abp;
using Microsoft.Data.SqlClient.Server;
using System.Globalization;
using System.DirectoryServices.ActiveDirectory;
using Dm;
using Abp.Extensions;
using SEFA.Base.Model.Models;
using MongoDB.Bson.Serialization.Serializers;
using SEFA.Base.IServices.BASE;
using NetTaste;
using System.Threading.Tasks;
using SEFA.Base.Common.LogHelper;
using NodaTime.TimeZones;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;


namespace SEFA.MKM.Services
{
    public class MaterialInventoryServices : BaseServices<MaterialInventoryEntity>, IMaterialInventoryServices
    {

        private readonly IBaseRepository<ProductionHistoryViewEntity> _dalProductionHistoryViewEntity;
        private readonly IBaseRepository<SappackorderrecordEntity> _dalSappackorderrecordEntity;

        private readonly IBaseRepository<SappackorderEntity> _dalSappackorderEntity;

        private readonly IBaseRepository<WorkorderthroatEntity> _dalWorkorderthroatEntity;
        private readonly IBaseRepository<ColdAWarehouseViewEntity> _dalColdAWarehouseViewEntity;

        private readonly IBaseRepository<DFM.Model.Models.UnitConvertEntity> _dalUnitConvertEntity;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _dalUnitmanageEntity;

        //注册打印类
        private readonly IBaseRepository<PrintSelectViewEntity> _PrintSelectViewEntityDal;
        private readonly IPrintSelectViewServices _IPrintSelectViewServices;

        private readonly IBaseRepository<MaterialEntity> _materialEntitydal;
        private readonly IBaseRepository<PPM.Model.Models.UnitmanageEntity> _UnitEntityDal;
        private readonly IBaseRepository<MaterialInventoryEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<MaterialSubLotEntity> _materialSubLotServicesDal;
        private readonly IBaseRepository<MaterialLotEntity> _materialLotEntityDal;
        public IUser _user;
        private readonly IBaseRepository<MMaterialPropertyViewEntity> _MaterialPropertyDal;
        private readonly IBaseRepository<ContainerEntity> _ContainerEntity;
        private readonly IBaseRepository<InventorylistingViewEntity> _InventorylistingViewEntityDal;
        private readonly IBaseRepository<MaterialInventoryEntity> _MaterialInventoryEntityDal;
        private readonly IBaseRepository<MaterialTransferEntity> _MaterialTransferEntityDal;
        private readonly IBaseRepository<ContainerHistoryEntity> _ContainerHistoryEntityDal;
        private readonly IBaseRepository<DicDestinationEntity> _dalDicDestinationEntity;
        private readonly IBaseRepository<RequestDetailEntity> _RequestDetailEntity;
        private readonly IBaseRepository<InventoryRequestEntity> _InventoryRequestEntity;
        private readonly IBaseRepository<TransferHistoryViewEntity> _TransferHistoryViewEntityDal;

        private readonly IInventorylistingViewServices _inventorylistingViewServices;

        private readonly IBaseRepository<EquipmentEntity> _equipmentEntity;

        private readonly LKKESBHelper _esbHelper;
        private readonly IBaseRepository<ScaleSelectViewEntity> _dalScaleSelectViewEntity;

        private readonly IBaseRepository<PPM.Model.Models.ProduceActualViewEntity> _dalProduceActualViewEntity;

        //注册接口类
        private readonly IRequestInventoryViewServices _requestInventoryViewServices;
        private readonly IBaseRepository<PoConsumeMaterialListViewEntity> _PoConsumeMaterialListViewEntity;

        public MaterialInventoryServices(
            IBaseRepository<UnitmanageEntity> unitEntityDal, IBaseRepository<Model.Models.MaterialEntity> materialEntitydal,
            IBaseRepository<MaterialInventoryEntity> dal, IBaseRepository<MaterialSubLotEntity> materialSubLotServicesDal, IBaseRepository<MaterialLotEntity> materialLotEntityDal,
            IBaseRepository<MMaterialPropertyViewEntity> materialPropertyDal,
            IUser user, IUnitOfWork unitOfWork,
            IBaseRepository<ContainerEntity> ContainerEntity, IBaseRepository<InventorylistingViewEntity> InventorylistingViewEntityDal,
            IBaseRepository<MaterialInventoryEntity> MaterialInventoryEntityDal, IBaseRepository<MaterialTransferEntity> MaterialTransferEntityDal, IBaseRepository<ContainerHistoryEntity> ContainerHistoryEntityDal
            , IBaseRepository<DicDestinationEntity> dalDicDestinationEntity,
            LKKESBHelper esbHelper, IBaseRepository<RequestDetailEntity> RequestDetailEntity, IBaseRepository<InventoryRequestEntity> InventoryRequestEntity, IInventorylistingViewServices inventorylistingViewServices,
            IBaseRepository<ScaleSelectViewEntity> dalScaleSelectViewEntity
, IBaseRepository<ProduceActualViewEntity> dalProduceActualViewEntity, IRequestInventoryViewServices requestInventoryViewServices, IBaseRepository<TransferHistoryViewEntity> transferHistoryViewEntityDal, IBaseRepository<PrintSelectViewEntity> printSelectViewEntityDal, IPrintSelectViewServices iPrintSelectViewServices, IBaseRepository<EquipmentEntity> equipmentEntity, IBaseRepository<PoConsumeMaterialListViewEntity> poConsumeMaterialListViewEntity, IBaseRepository<DFM.Model.Models.UnitConvertEntity> dalUnitConvertEntity, IBaseRepository<DFM.Model.Models.UnitmanageEntity> dalUnitmanageEntity = null, IBaseRepository<ColdAWarehouseViewEntity> dalColdAWarehouseViewEntity = null, IBaseRepository<WorkorderthroatEntity> dalWorkorderthroatEntity = null, IBaseRepository<ProductionHistoryViewEntity> dalProductionHistoryViewEntity = null, IBaseRepository<SappackorderrecordEntity> dalSappackorderrecordEntity = null, IBaseRepository<SappackorderEntity> dalSappackorderEntity = null)

        {
            _inventorylistingViewServices = inventorylistingViewServices;
            _dalDicDestinationEntity = dalDicDestinationEntity;
            _materialEntitydal = materialEntitydal;
            _UnitEntityDal = unitEntityDal;
            this._dal = dal;
            base.BaseDal = dal;
            _user = user;
            _unitOfWork = unitOfWork;
            _materialSubLotServicesDal = materialSubLotServicesDal;
            _materialLotEntityDal = materialLotEntityDal;
            _MaterialPropertyDal = materialPropertyDal;
            _ContainerEntity = ContainerEntity;
            _InventorylistingViewEntityDal = InventorylistingViewEntityDal;
            _MaterialInventoryEntityDal = MaterialInventoryEntityDal;
            _MaterialTransferEntityDal = MaterialTransferEntityDal;
            _ContainerHistoryEntityDal = ContainerHistoryEntityDal;
            _user = user;
            _unitOfWork = unitOfWork;
            _esbHelper = esbHelper;
            _RequestDetailEntity = RequestDetailEntity;
            _InventoryRequestEntity = InventoryRequestEntity;
            _dalScaleSelectViewEntity = dalScaleSelectViewEntity;
            _dalProduceActualViewEntity = dalProduceActualViewEntity;
            _requestInventoryViewServices = requestInventoryViewServices;
            _TransferHistoryViewEntityDal = transferHistoryViewEntityDal;
            _PrintSelectViewEntityDal = printSelectViewEntityDal;
            _IPrintSelectViewServices = iPrintSelectViewServices;
            _equipmentEntity = equipmentEntity;
            _PoConsumeMaterialListViewEntity = poConsumeMaterialListViewEntity;
            _dalUnitConvertEntity = dalUnitConvertEntity;
            _dalUnitmanageEntity = dalUnitmanageEntity;
            _dalColdAWarehouseViewEntity = dalColdAWarehouseViewEntity;
            _dalWorkorderthroatEntity = dalWorkorderthroatEntity;
            _dalProductionHistoryViewEntity = dalProductionHistoryViewEntity;
            _dalSappackorderrecordEntity = dalSappackorderrecordEntity;
            _dalSappackorderEntity = dalSappackorderEntity;
        }

        #region 获取库存数据源

        /// <summary>
        /// 库存数据（获取库存得数据源）
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<List<object>> GetEntityInvent(string inventID)
        {
            List<object> objList = new List<object>();
            //查询视图
            var result = await _inventorylistingViewServices.FindEntity(p => p.ID == inventID);
            if (result != null)
            {
                string supplier_Name = string.Empty;
                string material_Name = string.Empty;
                string material_Code = string.Empty;
                string lot_Code = string.Empty;
                string material_Inventory_Qty = string.Empty;
                string unit_Code = string.Empty;
                string sublot_Code = string.Empty;
                object objs = new PPM.Model.ViewModels.MKM.PrintView.VerifiyDetailTotalData
                {
                    ///WMS标签数据源
                    //Supplier_Name = "",
                    Plant = "三厂",
                    Material_Name = result.MaterialName,
                    Material_Code = result.MaterialCode,
                    Batch_Code = result.BatchId,
                    NUM = result.Quantity.ToString(),
                    PO_NUM = result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                    Unit = result.MinUnit,
                    Receive_Date = result.CreateDate.ToString("yyyy-MM-dd"),
                    SSCC = result.Sscc
                };
                objList.Add(objs);

            }
            return objList;

        }

        #endregion

        #region 转移记录历史-反冲

        /// <summary>
        /// 转移记录历史-反冲
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> TranferHisReverse(TranferReverse reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //查询转移历史
                var transfer = await _MaterialTransferEntityDal.FindEntity(p => p.ID == reqModel.ID);
                if (transfer == null)
                {
                    result.msg = "不存在对应转移历史数据";
                    return result;
                }

                string inventID = string.Empty;
                string subLotID = string.Empty;
                DateTime exTime = DateTime.Now;
                string lotStatus = string.Empty;
                string subLotStatus = string.Empty;
                string mID = string.Empty;
                //根据Code查询subLoitID
                var subLotModel = await _materialSubLotServicesDal.FindEntity(x => x.SubLotId == reqModel.SubLotCode);

                //获取ID
                if (subLotModel != null)
                {
                    subLotID = subLotModel.SubLotId;
                    subLotStatus = subLotModel.ExternalStatus;
                }
                else
                {
                    result.msg = "不存在对应的子批次数据";
                    return result;
                }

                //查询批次信息
                var lotModel = await _materialLotEntityDal.FindEntity(p => p.ID == transfer.OldLotId);
                if (lotModel != null)
                {
                    exTime = lotModel.ExpirationDate;
                    lotStatus = lotModel.ExternalStatus;
                    mID = lotModel.MaterialId;
                }
                else
                {
                    result.msg = "不存在对应的批次数据";
                    return result;

                }
                //查询库存明细
                var materialInventory = await _dal.FindEntity(x => x.SublotId == subLotID);
                if (materialInventory != null)
                {
                    result.msg = "库存信息存在";
                    return result;
                }

                //更新最终数量（退库反向给累加库存）
                //decimal quantity = reqModel.Qty;
                //inventID = materialInventoryEntity.ID;
                //materialInventoryEntity.Modify(inventID, _user.Name.ToString());

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldStorageLocation = transfer.OldStorageLocation;
                trans.NewStorageLocation = transfer.OldStorageLocation;
                trans.OldLotId = transfer.OldLotId;
                trans.NewLotId = transfer.OldLotId;
                trans.OldSublotId = transfer.OldSublotId;
                trans.NewSublotId = transfer.OldSublotId;
                trans.OldExpirationDate = transfer.OldExpirationDate;
                trans.NewExpirationDate = exTime;
                trans.Quantity = Math.Round(Convert.ToDecimal(transfer.Quantity), 3); //Convert.ToInt32(transfer.Quantity);
                trans.QuantityUomId = transfer.QuantityUomId;
                trans.ProductionExecutionId = transfer.ProductionExecutionId;
                trans.Type = "cancel scarp";
                trans.Comment = "取消报废";
                trans.NewEquipmentRequirementId = transfer.OldEquipmentRequirementId;
                trans.OldEquipmentRequirementId = transfer.OldEquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = transfer.NewEquipmentId;
                trans.NewEquipmentId = transfer.OldEquipmentId;
                trans.OldContainerId = transfer.OldContainerId;
                trans.NewContainerId = transfer.OldContainerId;
                //status
                trans.OldMaterialId = transfer.OldMaterialId;
                trans.NewMaterialId = mID;
                trans.OldLotExternalStatus = transfer.OldLotExternalStatus;
                trans.OldSublotExternalStatus = transfer.OldSublotExternalStatus;
                trans.NewLotExternalStatus = lotStatus;
                trans.NewSublotExternalStatus = subLotStatus;
                trans.PhysicalQuantity = transfer.PhysicalQuantity; //物理数量
                trans.TareQuantity = transfer.TareQuantity;  //皮数量

                #endregion

                #region 复原库存信息

                MaterialInventoryEntity request = new MaterialInventoryEntity();
                request.Create(_user.Name.ToString());
                //数量
                request.Quantity = Math.Round(Convert.ToDecimal(reqModel.Qty), 3);// Convert.ToDecimal(reqModel.Qty);
                //单位
                request.QuantityUomId = trans.QuantityUomId;
                //存储节点
                request.EquipmentId = transfer.OldEquipmentId;
                //容器
                request.ContainerId = trans.OldContainerId;
                //批次
                request.LotId = trans.OldLotId;
                //子批次
                request.SublotId = trans.OldSublotId;

                #endregion
                _unitOfWork.BeginTran();

                //更新库存
                bool saveIninventory = await _dal.Add(request) > 0;
                //新增记录
                bool tranResult = await _MaterialTransferEntityDal.Add(trans) > 0;

                if (!saveIninventory || !tranResult)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "反冲失败,新增库存状态:" + saveIninventory + ",新增转移记录状态:" + tranResult;
                    return result;
                }
                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "反冲成功";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "反冲失败，原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }


        #endregion

        #region 转移记录历史-退库返还（调用SPA接口，调用WMS接口，修改本身历史记录，创建新的历史记录，还原库存信息）
        /// <summary>
        /// 转移记录历史-退库返还（调用SPA转移接口，调用WMS接口，修改本身历史记录，创建新的历史记录）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ReturnWMS(TranferReverse reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {

                //查询转移历史(实体)
                var transfer = await _MaterialTransferEntityDal.FindEntity(p => p.ID == reqModel.ID);
                if (transfer == null)
                {
                    result.msg = "不存在对应转移历史数据";
                    return result;
                }
                var transferView = await _TransferHistoryViewEntityDal.FindEntity(p => p.ID == reqModel.ID);
                if (transferView == null)
                {
                    result.msg = "不存在对应转移历史视图数据";
                    return result;
                }
                #region 找MESpro(需要发送数据给WMS)

                string proNumber = string.Empty;
                var pActual = await _dalProduceActualViewEntity.FindEntity(P => P.SsccId == transfer.OldSublotId);
                if (pActual != null)
                {
                    proNumber = pActual.ProductionOrderNo;
                }
                #endregion

                string newLocation = transferView.NewEcode;
                string oldLocation = transferView.OldEcode;
                #region 调用SAP接口


                SapTransfer sapTransfer = new SapTransfer();
                sapTransfer.BKTXT = "SAP-TO-MES";
                sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
                List<SendTran> list = new List<SendTran>();
                SendTran model = new SendTran();
                model.LINE_ID = "1";
                model.BWART = "311";

                model.MATNR = transferView.NewMaterialCode;
                model.MENGE = Math.Round(transferView.Quantity.Value, 3);  //transferView.Quantity.Value;
                model.MEINS = transferView.HUnit;


                //if (transferView.NewMaterialCode.Substring(0, 1) == "4")
                //{
                //    sendLocationType = "PKG3"; //"MFG3";
                //}
                //else
                //{
                //    sendLocationType = "MFG3"; //"MFG3";
                //}
                model.WERKS = "2010";
                model.LGORT = newLocation;
                model.CHARG = transferView.NewLotId;

                model.UMWRK = "2010";
                model.UMLGO = oldLocation;
                model.UMCHA = transferView.NewLotId;

                model.SGTXT = "MES Transfer";
                list.Add(model);
                sapTransfer.SendList = list;
                List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                if (sapStockTranList == null)
                {
                    result.msg = "SAP接口调用失败";
                    return result;
                }
                if (sapStockTranList.Count == 0)
                {
                    result.msg = "SAP返回数据为空";
                    return result;
                }

                if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                {
                    result.msg = "SAP返回数据为空";
                    return result;
                }

                //获取对应的物料凭证编码
                string sapNo = sapStockTranList[0].MCodeNameplateNo;

                #endregion

                #region 调用WMS接口

                ////构造实体
                //List<PrintSendDataItems> wmsSend = new List<PrintSendDataItems>();
                //PrintSendDataItems print = new PrintSendDataItems();
                //print.qty = Convert.ToDecimal(transfer.Quantity);
                //print.sapVo = sapNo;
                //print.printtype = "retwarTCItem";
                //// print.workOrderNo = "6300200002R2480500100004";//transfer.WmsPrintno;
                //print.batch = transferView.NewLotId;
                //print.type = 1;
                //print.invstatus = 2;
                //print.itemcode = transferView.OldMaterialCode;
                //print.itemunitcode = transferView.HUnit;
                //print.workshopStockcode = sendLocationType;
                //wmsSend.Add(print);

                //var wmsResult = _requestInventoryViewServices.PrintLabelSynchro(wmsSend);
                //bool flag = wmsResult.Result.Response.flag;
                //if (flag == false)
                //{
                //    result.msg = wmsResult.Result.Response.msg;
                //    return result;
                //}
                //string wmsPrintNO = wmsResult.Result.Response.printNo;

                #endregion

                string inventID = string.Empty;
                string subLotID = string.Empty;
                DateTime exTime = DateTime.Now;
                string lotStatus = string.Empty;
                string subLotStatus = string.Empty;
                string mID = string.Empty;
                //根据Code查询subLoitID
                var subLotModel = await _materialSubLotServicesDal.FindEntity(x => x.ID == transfer.NewSublotId);

                //获取ID
                if (subLotModel != null)
                {
                    subLotID = subLotModel.SubLotId;
                    subLotStatus = subLotModel.ExternalStatus;
                }
                else
                {
                    result.msg = "不存在对应的子批次数据";
                    return result;
                }

                //查询批次信息
                var lotModel = await _materialLotEntityDal.FindEntity(p => p.ID == transfer.OldLotId);
                if (lotModel != null)
                {
                    exTime = lotModel.ExpirationDate;
                    lotStatus = lotModel.ExternalStatus;
                    mID = lotModel.MaterialId;
                }
                else
                {
                    result.msg = "不存在对应的批次数据";
                    return result;

                }
                //查询库存明细
                var materialInventory = await _dal.FindEntity(x => x.SublotId == subLotID);
                if (materialInventory != null)
                {
                    result.msg = "库存信息存在";
                    return result;
                }

                #region 写入转移历史（新增返还数据历史记录）

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldStorageLocation = transfer.OldStorageLocation;
                trans.NewStorageLocation = transfer.OldStorageLocation;
                trans.OldLotId = transfer.OldLotId;
                trans.NewLotId = transfer.OldLotId;
                trans.OldSublotId = transfer.OldSublotId;
                trans.NewSublotId = transfer.OldSublotId;
                trans.OldExpirationDate = transfer.OldExpirationDate;
                trans.NewExpirationDate = exTime;
                trans.Quantity = Math.Round(Convert.ToDecimal(transfer.Quantity), 3); // Convert.ToInt32(transfer.Quantity);
                trans.QuantityUomId = transfer.QuantityUomId;
                trans.ProductionExecutionId = transfer.ProductionExecutionId;
                trans.Type = "cancel Return";
                trans.Comment = "退库-返还";
                trans.NewEquipmentRequirementId = transfer.OldEquipmentRequirementId;
                trans.OldEquipmentRequirementId = transfer.OldEquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = transfer.NewEquipmentId;
                trans.NewEquipmentId = transfer.OldEquipmentId;
                trans.OldContainerId = transfer.OldContainerId;
                trans.NewContainerId = transfer.OldContainerId;
                //status
                trans.OldMaterialId = transfer.OldMaterialId;
                trans.NewMaterialId = mID;
                trans.OldLotExternalStatus = transfer.OldLotExternalStatus;
                trans.OldSublotExternalStatus = transfer.OldSublotExternalStatus;
                trans.NewLotExternalStatus = lotStatus;
                trans.NewSublotExternalStatus = subLotStatus;
                trans.PhysicalQuantity = transfer.PhysicalQuantity; //物理数量
                trans.TareQuantity = transfer.TareQuantity;  //皮数量

                //更新
                //trans.WmsPrintno = wmsPrintNO;
                trans.SapPrintno = sapNo;
                trans.MesProno = proNumber;

                #endregion

                #region 复原库存信息

                MaterialInventoryEntity request = new MaterialInventoryEntity();
                request.Create(_user.Name.ToString());
                //数量
                request.Quantity = Math.Round(Convert.ToDecimal(transfer.Quantity), 3); //Convert.ToDecimal(transfer.Quantity);
                //单位
                request.QuantityUomId = trans.QuantityUomId;
                //存储节点
                request.EquipmentId = transfer.OldEquipmentId;
                //容器
                request.ContainerId = trans.OldContainerId;
                //批次
                request.LotId = trans.OldLotId;
                //子批次
                request.SublotId = trans.OldSublotId;


                #endregion

                #region 更新原有转移历史记录

                transfer.Modify(reqModel.ID, _user.Name.ToString());
                transfer.WmsPrintno = "";
                transfer.SapPrintno = "";
                transfer.MesProno = "";

                #endregion

                _unitOfWork.BeginTran();

                //更新库存
                bool saveIninventory = await _dal.Add(request) > 0;
                //新增记录
                bool tranResult = await _MaterialTransferEntityDal.Add(trans) > 0;
                //更新记录
                bool upTranResult = await _MaterialTransferEntityDal.Update(transfer);

                bool deleteInvent = true;
                //删除数据
                InventorylistingViewEntity inventMode = await _InventorylistingViewEntityDal.FindEntity(p => p.SlotId == trans.OldLotId);
                if (inventMode != null && !string.IsNullOrEmpty(inventMode.ID))
                {
                    //删除库存
                    deleteInvent = await _InventorylistingViewEntityDal.DeleteById(inventMode.ID);
                }

                if (!saveIninventory || !tranResult || !upTranResult || !deleteInvent)
                {

                    _unitOfWork.RollbackTran();
                    result.msg = "退库返还失败,新增库存状态:" + saveIninventory + ",新增转移记录状态:" + tranResult + ",更新转移记录状态:" + upTranResult;
                    return result;
                }
                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "退库返还成功";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "退库返还失败，原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }

        public List<string> MES_SAP_WMS(MES_SAP_WMS reqModel)
        {
            //#region 调用SAP接口


            //SapTransfer sapTransfer = new SapTransfer();
            //sapTransfer.BKTXT = "MES-TO-SAP";
            //sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
            //List<SendTran> list = new List<SendTran>();
            //SendTran model = new SendTran();
            //model.LINE_ID = "1";
            //model.BWART = reqModel.ReturnType;

            //model.MATNR = reqModel.MCode;
            //model.MENGE = reqModel.Qty;
            //model.MEINS = reqModel.MUinit;

            //string sendLocationType = string.Empty;
            //if (reqModel.MCode.Substring(0, 1) == "4")
            //{
            //    sendLocationType = "PKG3"; //"MFG3";
            //}
            //else
            //{
            //    sendLocationType = "MFG3"; //"MFG3";
            //}
            //model.WERKS = "2010";
            //model.LGORT = sendLocationType;
            //model.CHARG = transferView.NewLotId;

            //model.UMWRK = "2010";
            //model.UMLGO = sendLocationType;
            //model.UMCHA = transferView.NewLotId;

            //model.SGTXT = "MES Transfer";
            //list.Add(model);
            //sapTransfer.SendList = list;
            //List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
            //if (sapStockTranList == null)
            //{
            //    result.msg = "SAP接口调用失败";
            //    return result;
            //}
            //if (sapStockTranList.Count == 0)
            //{
            //    result.msg = "SAP返回数据为空";
            //    return result;
            //}

            //if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
            //{
            //    result.msg = "SAP返回数据为空";
            //    return result;
            //}

            ////获取对应的物料凭证编码
            //string sapNo = sapStockTranList[0].MCodeNameplateNo;

            //#endregion

            //#region 调用WMS接口

            ////构造实体
            //List<PrintSendDataItems> wmsSend = new List<PrintSendDataItems>();
            //PrintSendDataItems print = new PrintSendDataItems();
            //print.qty = Convert.ToDecimal(transfer.Quantity);
            //print.sapVo = sapNo;
            //print.printtype = "retwarTCItem";
            //// print.workOrderNo = "6300200002R2480500100004";//transfer.WmsPrintno;
            //print.batch = transferView.NewLotId;
            //print.type = 1;
            //print.invstatus = 2;
            //print.itemcode = transferView.OldMaterialCode;
            //print.itemunitcode = transferView.HUnit;
            //print.workshopStockcode = sendLocationType;
            //wmsSend.Add(print);

            //var wmsResult = _requestInventoryViewServices.PrintLabelSynchro(wmsSend);
            //bool flag = wmsResult.Response.flag;
            //if (flag == false)
            //{
            //    result.msg = wmsResult.Response.msg;
            //    return result;
            //}
            //string wmsPrintNO = wmsResult.Response.printNo;

            //#endregion

            return null;
        }

        #endregion


        #region 交仓

        /// <summary>
        /// 交仓
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> DeliveryInventory(TransferModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                string positionName = "311";
                string remarkstr = "交仓";
                string[] IDS = reqModel.ID;
                if (IDS == null || IDS.Length <= 0)
                {
                    result.msg = "请选择交仓数据";
                    return result;
                }
                #region 操作实体
                List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
                List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
                List<string> deleteInvent = new List<string>();
                #endregion

                string errorMsg = string.Empty;
                SapTransfer sapTransfer = new SapTransfer();
                sapTransfer.BKTXT = "交仓";
                sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");

                //查询数据(库存)
                var inventoryModel = await _InventorylistingViewEntityDal.Db.Queryable<InventorylistingViewEntity>().In(p => p.ID, IDS).ToListAsync();
                if (inventoryModel == null || (inventoryModel.Count != reqModel.ID.Length))
                {
                    result.msg = "请确认选中库存是否存在";
                    return result;
                }

                //拿物料
                string[] ClassCodeS = inventoryModel.GroupBy(P => P.ClassCode).Select(P => P.Key).ToArray();
                if (ClassCodeS != null && ClassCodeS.Length != 1)
                {
                    result.msg = "请确认选中数据是否是同类型物料";
                    return result;
                }

                string[] MCODES = inventoryModel.GroupBy(P => P.MaterialCode).Select(P => P.Key).ToArray();
                if (MCODES == null || MCODES.Length <= 0)
                {
                    result.msg = "不存在物料信息";
                    return result;
                }

                //string[] MIDS = inventoryModel.GroupBy(P => P.MaterialId).Select(P => P.Key).ToArray();
                //if (MIDS != null && MIDS.Length != 1)
                //{
                //    result.msg = "不存在物料信息";
                //    return result;
                //}

                var ProductionList = await _dalProductionHistoryViewEntity.Db.Queryable<ProductionHistoryViewEntity>().In(p => p.MaterialCode, MCODES).ToListAsync();
                if (ProductionList == null || ProductionList.Count <= 0)
                {
                    result.msg = "不存在产出记录";
                    return result;
                }

                var mAndPro = ProductionList.GroupBy(p => new { SSCC = p.SUB_LOT_ID, PNO = p.PNo }).Select(p => new { SSCC = p.Key.SSCC, PNO = p.Key.PNO }).ToList();

                string[] proList = ProductionList.GroupBy(p => p.PNo).Select(p => p.Key).ToArray();
                if (proList == null || proList.Length <= 0)
                {
                    result.msg = "不存在工单信息";
                    return result;
                }

                var sapList = await _dalSappackorderEntity.Db.Queryable<SappackorderEntity>().In(p => p.Aufnr, proList).ToListAsync();
                //var maList = await _materialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, MIDS).ToListAsync();

                List<SendTran> list = new List<SendTran>();
                string mID = string.Empty;
                string toLocation = string.Empty;
                string fromLocation = string.Empty;
                string mCode = string.Empty;
                string sscc = string.Empty;
                for (int i = 0; i < inventoryModel.Count; i++)
                {
                    mID = inventoryModel[i].MaterialId;
                    mCode = inventoryModel[i].MaterialCode;
                    sscc = inventoryModel[i].Sscc;
                    fromLocation = inventoryModel[i].LocationS.ToString();
                    #region 业务处理

                    #region 拿目的地

                    //拿工单
                    var mModel = mAndPro.Where(p => p.SSCC == sscc).FirstOrDefault();
                    if (mModel == null)
                    {
                        result.msg = "不存在产出历史" + sscc;
                        return result;
                    }

                    //拿数据
                    string pNumber = mModel.PNO;
                    var sap = sapList.Where(p => p.Aufnr == pNumber).FirstOrDefault();
                    if (sap == null)
                    {
                        result.msg = "SAP工单不存在" + pNumber;
                        return result;
                    }
                    toLocation = sap.Lgort;

                    #endregion




                    //var mMode = maList.Where(p => p.ID == mID).ToList().FirstOrDefault();
                    //if (mMode == null)
                    //{
                    //    result.msg = "物料信息不存在" + mID;
                    //    return result;
                    //}


                    #region 交仓信息SAP创建

                    SendTran model = new SendTran();
                    model.LINE_ID = (i + 1).ToString();
                    model.BWART = positionName;

                    model.MATNR = inventoryModel[i].MaterialCode;//transferView.NewMaterialCode;
                    model.MENGE = Math.Round(inventoryModel[i].Quantity.Value, 3); // inventoryModel.Quantity.Value;
                    model.MEINS = inventoryModel[i].MaxUnit;

                    string sendLocationType = string.Empty;
                    sendLocationType = toLocation;

                    model.WERKS = "2010";
                    model.LGORT = fromLocation;
                    model.CHARG = inventoryModel[i].BatchId;

                    model.UMWRK = "2010";
                    model.UMLGO = sendLocationType;
                    model.UMCHA = inventoryModel[i].BatchId;

                    model.SGTXT = "MES Transfer";
                    list.Add(model);


                    #endregion

                    #region 交仓转移历史

                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.WmsPrintno = inventoryModel[i].SubLotId;
                    trans.MesProno = inventoryModel[i].ProBatch;
                    trans.NewEquipmentId = inventoryModel[i].EquipmentId;
                    trans.Type = "Transfer Inventory";
                    trans.Comment = "交仓";

                    trans.Create(_user.Name);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel[i].LocationF;
                    trans.NewStorageLocation = inventoryModel[i].LocationF;
                    trans.OldLotId = inventoryModel[i].LotId;
                    trans.NewLotId = inventoryModel[i].LotId;
                    trans.OldSublotId = inventoryModel[i].SlotId;
                    trans.NewSublotId = inventoryModel[i].SlotId;
                    trans.OldExpirationDate = inventoryModel[i].ExpirationDate;
                    trans.NewExpirationDate = inventoryModel[i].ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel[i].Quantity), 3);
                    trans.QuantityUomId = inventoryModel[i].QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel[i].ProductionRequestId;


                    // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel[i].EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel[i].EquipmentId;

                    trans.OldContainerId = inventoryModel[i].ContainerId;
                    trans.NewContainerId = inventoryModel[i].ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel[i].MaterialId;
                    trans.NewMaterialId = inventoryModel[i].MaterialId;
                    trans.OldLotExternalStatus = inventoryModel[i].StatusF;
                    trans.OldSublotExternalStatus = inventoryModel[i].StatusS;
                    trans.NewLotExternalStatus = inventoryModel[i].StatusF;
                    trans.NewSublotExternalStatus = inventoryModel[i].StatusS;
                    trans.WmsPrintno = positionName;
                    //trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                    //trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    trans.Remark = remarkstr;
                    addTransferSap.Add(trans);

                    #endregion

                    #region 删除

                    deleteInvent.Add(inventoryModel[i].ID);

                    #endregion

                    #endregion
                }

                try
                {
                    _unitOfWork.BeginTran();
                    if (list != null && list.Count > 0)
                    {
                        #region 构造发送数据

                        //这里合并下数量
                        List<SendTran> resultDis = (from a in list
                                                    group a by new
                                                    {
                                                        BWART = a.BWART,
                                                        MATNR = a.MATNR,
                                                        MEINS = a.MEINS,
                                                        WERKS = a.WERKS,
                                                        LGORT = a.LGORT,
                                                        CHARG = a.CHARG,
                                                        UMWRK = a.UMWRK,
                                                        UMLGO = a.UMLGO,
                                                        UMCHA = a.UMCHA,
                                                        SGTXT = a.SGTXT
                                                    } into g
                                                    select new SendTran
                                                    {
                                                        BWART = g.Key.BWART,
                                                        MATNR = g.Key.MATNR,
                                                        MEINS = g.Key.MEINS,
                                                        WERKS = g.Key.WERKS,
                                                        LGORT = g.Key.LGORT,
                                                        CHARG = g.Key.CHARG,
                                                        UMWRK = g.Key.UMWRK,
                                                        UMLGO = g.Key.UMLGO,
                                                        UMCHA = g.Key.UMCHA,
                                                        SGTXT = g.Key.SGTXT,
                                                        MENGE = g.Sum(p => p.MENGE)
                                                    }).ToList();

                        List<SendTran> resultSend = new List<SendTran>();
                        for (int i = 0; i < resultDis.Count; i++)
                        {
                            resultDis[i].LINE_ID = (i + 1).ToString();
                            resultSend.Add(resultDis[i]);
                        }
                        sapTransfer.SendList = resultSend;

                        #endregion
                    }

                    if (addTransfer.Count <= 0)
                    {
                        if (!string.IsNullOrEmpty(errorMsg))
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = errorMsg;
                            return result;
                        }
                    }

                    //删除
                    bool deleteInvents = true;
                    if (deleteInvent.Count > 0)
                    {
                        string[] ids = deleteInvent.ToArray();
                        deleteInvents = await _dal.DeleteByIds(ids);
                    }

                    #region 调用SAP接口(调用失败直接跳过)

                    string sapNo = string.Empty;
                    if (sapTransfer.SendList.Count > 0 && addTransferSap.Count > 0)
                    {
                        //这里筛选对应的转移历史记录

                        List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                        if (sapStockTranList == null)
                        {
                            result.msg = "SAP接口调用失败";
                            return result;
                        }
                        if (sapStockTranList.Count == 0)
                        {
                            _unitOfWork.RollbackTran();

                            result.msg = "SAP返回数据为空";
                            return result;
                        }
                        else if (sapStockTranList[0].Sucess == "NOK")
                        {
                            _unitOfWork.RollbackTran();

                            result.msg = "SAP接口调用失败:" + sapStockTranList[0].Msg;
                            return result;
                        }

                        if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                        {
                            _unitOfWork.RollbackTran();

                            result.msg = "SAP返回数据为空";
                            return result;
                        }

                        //默认认为这里的数据是一样条数
                        for (int i = 0; i < sapStockTranList.Count; i++)
                        {    //获取对应的物料凭证编码插入数据到新增记录里
                            string sapNos = sapStockTranList[i].MCodeNameplateNo;
                            sapNo = sapStockTranList[i].MCodeNameplateNo;
                            if (sapNo != string.Empty)
                            {
                                continue;
                            }
                        }
                        for (int i = 0; i < addTransferSap.Count; i++)
                        {
                            addTransferSap[i].SapPrintno = sapNo;
                            addTransfer.Add(addTransferSap[i]);
                        }
                    }

                    #endregion

                    bool tranHis = true;
                    if (addTransfer.Count > 0)
                    {
                        tranHis = await _MaterialTransferEntityDal.Add(addTransfer) > 0;
                    }
                    if (!tranHis || !deleteInvents)
                    {
                        _unitOfWork.RollbackTran();

                        result.msg = "交仓失败:转移历史状态：" + tranHis + "删除状态：" + deleteInvents;
                        return result;
                    }

                    _unitOfWork.CommitTran();
                    if (sapNo != string.Empty)
                    {
                        string msgs = sapNo + ";" + toLocation + ";" + positionName + ";" + remarkstr + ";";

                        result.success = true;
                        result.msg = "交仓成功";
                        return result;
                    }

                    result.success = true;
                    result.msg = "交仓成功";
                    return result;

                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "交仓失败:转移历史状态：" + ex;
                    return result;
                }
            }
            catch (Exception ex)
            {

                result.msg = "交仓失败，原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }


        #endregion



        #region Post请求 获取条码信息


        public async Task<MessageModel<string>> InterFACesSC()
        {
            var result = new MessageModel<string>();
            result.success = false;
            TextInterFace_Getsscc();
            return result;
        }
        public async void TextInterFace_Getsscc()
        {
            ScanSend sd = new ScanSend();
            sd.itembarcode = "6000015230";
            //	am.itemcode = "6000015230,6000015231,6000015232";
            await GetSSCCInfo(sd);
        }



        //获取条码信息(调用接口)
        public async Task<LKKESBResponse<ScanRootResult>> GetSSCCInfo(ScanSend model)
        {
            try
            {
                string json = JsonConvert.SerializeObject(model);
                string sendMsg = "key=WCS&data=" + json;
                var ssResult = await _esbHelper.PostString<ScanRootResult>("WMS_FindBarcodeInfo", sendMsg);
                return ssResult;
            }
            catch (Exception ex)
            {

                return new LKKESBResponse<ScanRootResult>();
            }

        }

        /// <summary>
        /// 获取SSCC码
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<List<MpModel>> Get_MaterialLabelData(List<MaterialLabelModel> list)
        {
            //判断当前批次号是否输入，数量是否填写
            var lotCodes = list.Where(p => p.LotCode == string.Empty || p.LotCode == null).ToList();
            var qtyS = list.Where(p => p.Qty == string.Empty || p.Qty == null).ToList();

            if (lotCodes.Count != 0 || qtyS.Count != 0)
            {
                return new List<MpModel>();
            }

            //生成批次信息
            List<MpModel> rList = new List<MpModel>();

            for (int i = 0; i < list.Count; i++)
            {
                MpModel model = new MpModel();
                string mID = list[i].MaterialID.Trim();
                if (string.IsNullOrEmpty(list[i].ExDay))
                {
                    list[i].ExDay = "1";
                }
                DateTime exTime = string.IsNullOrEmpty(list[i].ExTime) ? DateTime.Now : Convert.ToDateTime(list[i].ExTime.Trim());
                int exDay = Convert.ToInt32(list[i].ExDay.Trim());
                string bNumber = list[i].BatchID.Trim();

                #region 调用获取SSCC接口和有效期（改为直接打印条码）               


                #region 生成子批次ID


                string subLotCode = string.Empty;

                #region 构造实体

                SSCCModel models = new SSCCModel();
                models.Type = "";
                models.NextCode = "";
                models.MaxCode = "";
                models.MinCode = "";
                models.Prefix = "";
                models.TableName = "";
                models.TableId = "";
                models.SequenceType = "";
                models.ResetType = "";
                models.FeatureId = "";
                models.pageIndex = 1;
                models.pageSize = 10;
                models.orderByFileds = "";
                string token = _user.GetToken();
                var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                if (ssccString.success == true)
                {
                    //ssccString = ssccString.response;
                    subLotCode = ssccString.response.ToString();
                }
                #endregion

                #endregion
                model.SSCCCode = subLotCode;


                #region 有效期

                MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mID);
                if (materialData != null)
                {
                    string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                    int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                    model.ExDateTime = GetExpirationDate(addNumber, type, exTime).ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    model.ExDateTime = DateTime.Now.AddDays(exDay).ToString("yyyy-MM-dd HH:mm:ss");

                }

                #endregion


                rList.Add(model);


                #endregion
            }

            return rList;
        }

        /// <summary>
        /// 获取SSCC码
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<List<MpModel>> Get_MaterialLabelDataOld(List<MaterialLabelModel> list)
        {
            //判断当前批次号是否输入，数量是否填写
            var lotCodes = list.Where(p => p.LotCode == string.Empty || p.LotCode == null).ToList();
            var qtyS = list.Where(p => p.Qty == string.Empty || p.Qty == null).ToList();

            if (lotCodes.Count != 0 || qtyS.Count != 0)
            {
                return new List<MpModel>();
            }

            //生成批次信息
            List<MpModel> rList = new List<MpModel>();

            for (int i = 0; i < list.Count; i++)
            {
                MpModel model = new MpModel();
                string mCode = list[i].MaterialID.Trim();
                if (string.IsNullOrEmpty(list[i].ExDay))
                {
                    list[i].ExDay = "1";
                }
                int exDay = Convert.ToInt32(list[i].ExDay.Trim());
                string bNumber = list[i].BatchID.Trim();

                #region 调用获取SSCC接口和有效期（改为直接打印条码）

                //ScanSend ScanData = new ScanSend();
                //ScanData.itembarcode = list[i].MaterialCode.Trim();
                //var response = GetSSCCInfo(ScanData);

                PrintSendDataItems wmsPrinit = new PrintSendDataItems
                {
                    itemcode = list[i].MaterialCode.Trim(),
                    itemunitcode = list[i].UnitCode.Trim(),
                    batch = list[i].LotCode.Trim(),
                    printtype = "mesPrint",// "mesPrint",
                    qty = Convert.ToDecimal(list[i].Qty == string.Empty ? 0 : Convert.ToDecimal(list[i].Qty)),
                    type = 1,
                    invstatus = 2
                };
                List<PrintSendDataItems> wmsList = new List<PrintSendDataItems>();
                wmsList.Add(wmsPrinit);
                var response = _requestInventoryViewServices.PrintLabelSynchro(wmsList);

                model.MaterialID = mCode;
                model.BatchID = bNumber;

                if (response.Result.successed == true)
                {
                    PrintResult scanModel = response.Result.Response;
                    //获取批次号和有效期
                    if (scanModel.flag == true)
                    {
                        if (scanModel.data != null && scanModel.data.Count > 0)
                        {
                            //这里只会有一条数据
                            for (int j = 0; j < scanModel.data.Count; j++)
                            {
                                if (j == 0)
                                {
                                    string stringDate = scanModel.data[j].validity_date;
                                    stringDate = stringDate.Replace('.', '-');
                                    try
                                    {
                                        DateTime date = DateTime.ParseExact(stringDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                                        stringDate = date.ToString("yyyy-MM-dd") + " 00:00:00";
                                    }
                                    catch
                                    {
                                        stringDate = DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00";
                                    }

                                    stringDate = stringDate.ToCharArray().Aggregate(new StringBuilder(), (sb, c) => sb.Append(c == 'T' ? ' ' : c)).ToString();
                                    model.SSCCCode = scanModel.data[j].itembarcode;

                                    //重新绑定SSCC,使用MES规则
                                    try
                                    {
                                        #region 构造实体

                                        SSCCModel models = new SSCCModel();
                                        models.Type = "";
                                        models.NextCode = "";
                                        models.MaxCode = "";
                                        models.MinCode = "";
                                        models.Prefix = "";
                                        models.TableName = "";
                                        models.TableId = "";
                                        models.SequenceType = "";
                                        models.ResetType = "";
                                        models.FeatureId = "";
                                        models.pageIndex = 1;
                                        models.pageSize = 10;
                                        models.orderByFileds = "";
                                        string token = _user.GetToken();
                                        var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                                        if (ssccString.success == true)
                                        {
                                            //ssccString = ssccString.response;
                                            model.SSCCCode = ssccString.response.ToString();
                                        }
                                        #endregion
                                    }
                                    catch
                                    {

                                    }
                                    model.ExDateTime = stringDate;//;scanModel.data[j].validity_date;
                                    rList.Add(model);
                                }
                            }
                        }
                        else
                        {
                            #region 生成子批次ID

                            string subLotCode = string.Empty;

                            #region 构造实体

                            SSCCModel models = new SSCCModel();
                            models.Type = "";
                            models.NextCode = "";
                            models.MaxCode = "";
                            models.MinCode = "";
                            models.Prefix = "";
                            models.TableName = "";
                            models.TableId = "";
                            models.SequenceType = "";
                            models.ResetType = "";
                            models.FeatureId = "";
                            models.pageIndex = 1;
                            models.pageSize = 10;
                            models.orderByFileds = "";
                            string token = _user.GetToken();
                            var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                            if (ssccString.success == true)
                            {
                                //ssccString = ssccString.response;
                                subLotCode = ssccString.response.ToString();
                            }
                            #endregion

                            #endregion
                            model.SSCCCode = subLotCode;
                            model.ExDateTime = DateTime.Now.AddDays(exDay).ToString("yyyy-MM-dd HH:mm:ss");
                            rList.Add(model);
                        }
                        continue;
                    }
                    else
                    {
                        #region 生成子批次ID

                        string subLotCode = string.Empty;

                        #region 构造实体

                        SSCCModel models = new SSCCModel();
                        models.Type = "";
                        models.NextCode = "";
                        models.MaxCode = "";
                        models.MinCode = "";
                        models.Prefix = "";
                        models.TableName = "";
                        models.TableId = "";
                        models.SequenceType = "";
                        models.ResetType = "";
                        models.FeatureId = "";
                        models.pageIndex = 1;
                        models.pageSize = 10;
                        models.orderByFileds = "";
                        string token = _user.GetToken();
                        var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                        if (ssccString.success == true)
                        {
                            //ssccString = ssccString.response;
                            subLotCode = ssccString.response.ToString();
                        }
                        #endregion

                        #endregion
                        model.SSCCCode = subLotCode;
                        model.ExDateTime = DateTime.Now.AddDays(exDay).ToString("yyyy-MM-dd HH:mm:ss");
                        rList.Add(model);
                    }
                }
                else
                {
                    #region 生成子批次ID


                    string subLotCode = string.Empty;

                    #region 构造实体

                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _user.GetToken();
                    var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                    if (ssccString.success == true)
                    {
                        //ssccString = ssccString.response;
                        subLotCode = ssccString.response.ToString();
                    }
                    #endregion

                    #endregion
                    model.SSCCCode = subLotCode;
                    model.ExDateTime = DateTime.Now.AddDays(exDay).ToString("yyyy-MM-dd HH:mm:ss");
                    rList.Add(model);
                }

                #endregion
            }

            return rList;
        }

        #endregion

        /// <summary>
        /// 扫码收货
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SugarPrePutBySSCC(SugarPrePutModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                //首先判断当前子批次和EqupmentID是否存在
                if (reqModel.sscc == null || reqModel.sscc == "")
                {
                    result.msg = "子批次为空，请重新扫描";
                    return result;
                }

                if (reqModel.equipmentId == null || reqModel.equipmentId == "")
                {
                    result.msg = "存储地点为空";
                    return result;
                }

                List<MaterialSubLotEntity> lotsscc = await _materialSubLotServicesDal.FindList(p => p.SubLotId == reqModel.sscc);
                if (lotsscc != null && lotsscc.Count > 0)
                {
                    result.msg = "子批次已存在，请重新扫描";
                    return result;
                }

                //调用接口
                ScanSend model = new ScanSend();
                model.itembarcode = "";
                var returnData = GetSSCCInfo(model);
                if (returnData.Result.successed == false)
                {
                    result.msg = "调用接口失败";
                    return result;
                }
                ScanRootResult models = returnData.Result.Response;
                if (models.flag == true)
                {
                    List<MaterialInventoryEntity> addInvent = new List<MaterialInventoryEntity>();
                    List<MaterialLotEntity> addLot = new List<MaterialLotEntity>();
                    List<MaterialLotEntity> upLot = new List<MaterialLotEntity>();
                    List<MaterialSubLotEntity> addSubLot = new List<MaterialSubLotEntity>();
                    List<MaterialTransferEntity> addTran = new List<MaterialTransferEntity>();
                    List<UnitmanageEntity> addUnit = new List<UnitmanageEntity>();
                    List<RequestDetailEntity> upDetail = new List<RequestDetailEntity>();
                    for (int i = 0; i < models.data.Count; i++)
                    {
                        string supplierCode = models.data[i].suppliercode.ToString();
                        string supplierName = models.data[i].suppliername.ToString();
                        string qty = models.data[i].qty.ToString();
                        string unit = models.data[i].itemunitcode;
                        string mcode = models.data[i].itemcode;
                        string unitID = string.Empty;
                        string subLotID = string.Empty;
                        string lotId = models.data[i].batch;
                        //日期格式可能非标准模式需要单独考虑处理 17.03.2018
                        string reciveDate = models.data[i].recivedate.ToCharArray().Aggregate(new StringBuilder(), (sb, c) => sb.Append(c == 'T' ? ' ' : c)).ToString();
                        string expirationDate = models.data[i].validity_date.ToCharArray().Aggregate(new StringBuilder(), (sb, c) => sb.Append(c == 'T' ? ' ' : c)).ToString();

                        DateTime datereciveDate = DateTime.ParseExact(reciveDate, "dd.MM.yyyy", CultureInfo.InvariantCulture);
                        reciveDate = datereciveDate.ToString("yyyy-MM-dd") + " 00:00:00";

                        DateTime dateexpirationDate = DateTime.ParseExact(expirationDate, "dd.MM.yyyy", CultureInfo.InvariantCulture);
                        expirationDate = dateexpirationDate.ToString("yyyy-MM-dd") + " 00:00:00";

                        #region 创建库存

                        MaterialInventoryEntity request = new MaterialInventoryEntity();
                        request.Quantity = Math.Round(Convert.ToDecimal(qty), 3);
                        //创建数据
                        request.Create(_user.Name.ToString());

                        #region 处理单位问题

                        //判断当前单位是否存在
                        List<UnitmanageEntity> uList = await _UnitEntityDal.FindList(p => p.Name == unit);
                        if (uList != null && uList.Count > 0)
                        {
                            unitID = uList[0].ID;
                        }
                        else
                        {
                            UnitmanageEntity uModel = new UnitmanageEntity();
                            uModel.Create(_user.Name.ToString());
                            uModel.Name = unit;
                            uModel.Shortname = unit;
                            uModel.Enable = 1;
                            uModel.Deleted = 0;
                            uModel.Description = "扫码收货创建";
                            uModel.Deleted = 0;
                            unitID = uModel.ID;
                            addUnit.Add(uModel);
                        }

                        #endregion
                        //默认是kg
                        request.QuantityUomId = unitID;
                        request.EquipmentId = request.EquipmentId;
                        request.ContainerId = "";
                        request.StorageLocation = "";
                        request.Remark = "WMS";
                        #endregion

                        #region 创建子批次

                        MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                        submodel.Create(_user.Name.ToString());
                        subLotID = submodel.ID;
                        submodel.SubLotId = reqModel.sscc;

                        submodel.ExternalStatus = "3";
                        submodel.Type = "0";
                        //给库存添加子批次ID
                        request.SublotId = subLotID;
                        addSubLot.Add(submodel);
                        #endregion

                        #region 创建批次                  

                        //新增批次（判断是否存在批次信息，无创建，有无需操作）
                        List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotId);
                        if (lotList != null && lotList.Count > 0)
                        {
                            lotId = lotList[0].ID;
                            MaterialLotEntity upModel = new MaterialLotEntity();
                            upModel = lotList[0];
                            upModel.Modify(upModel.ID, _user.Name.ToString());
                            upModel.CreateDate = Convert.ToDateTime(reciveDate);
                            upModel.ExpirationDate = Convert.ToDateTime(expirationDate);
                            //upModel.Suppiercode = supplierCode;
                            //upModel.Suppiername = supplierName;
                            upLot.Add(upModel);
                            request.LotId = upModel.ID;
                        }
                        else
                        {
                            //保存批次
                            MaterialLotEntity lModel = new MaterialLotEntity();
                            lModel.Create(_user.Name.ToString());
                            lModel.Suppiercode = supplierCode;
                            lModel.Suppiername = supplierName;
                            lotId = lModel.ID;
                            lModel.LotId = lotId;

                            #region 处理物料信息
                            List<Model.Models.MaterialEntity> mList = await _materialEntitydal.FindList(x => x.Code == mcode);
                            if (lotList != null && lotList.Count > 0)
                            {
                                lModel.MaterialId = mList[0].ID;
                            }
                            else
                            {
                                result.msg = "物料信息不存在:" + mcode;
                                return result;
                            }

                            #endregion

                            lModel.ExpirationDate = Convert.ToDateTime(expirationDate);//待定（这里需要去查询当前物料的有效期）
                            lModel.ExternalStatus = "2"; //(1:B 上锁 2:Q 3:U 解锁)
                            lModel.Type = "0";
                            request.LotId = lModel.ID;
                            addLot.Add(lModel);
                        }



                        #endregion

                        #region 更新请料记录

                        //查询物料信息
                        List<Model.Models.MaterialEntity> mList2 = await _materialEntitydal.FindList(x => x.Code == mcode);

                        DateTime date = DateTime.Now;
                        DateTime startOfDay = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0);
                        // 当天23点59分59秒
                        DateTime endOfDay = new DateTime(date.Year, date.Month, date.Day, 23, 59, 59);
                        InventoryRequestEntity getMaterial = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mList2[0].ID);

                        List<RequestDetailEntity> requestd = await _RequestDetailEntity.FindList(p => p.InventoryRequestId == getMaterial.ID && p.ActualTime != null && p.Status != null && p.CreateDate > startOfDay && p.CreateDate < endOfDay);
                        if (requestd.Count >= 1)
                        {
                            List<RequestDetailEntity> list = requestd.OrderBy(p => p.CreateDate).ToList();
                            if (requestd.Count > 1)
                            {
                                for (int j = 0; j < list.Count(); j++)
                                {
                                    //获取需要收货的数量
                                    var quy = list[j].Quantity - list[j].ActualQuantity;
                                    //收货数量
                                    var number = Math.Round(Convert.ToDecimal(qty), 3);
                                    //当收货数量大于需要收货数量
                                    if (Math.Round(Convert.ToDecimal(qty), 3) > quy)
                                    {
                                        //计算差值
                                        number = Math.Round(Convert.ToDecimal(qty), 3) - quy;
                                        var number2 = Math.Round(Convert.ToDecimal(qty), 3) - number;
                                        //收货数量加上差值
                                        list[j].ActualQuantity += number2;
                                        list[j].Status = "已完成";
                                        upDetail.Add(list[j]);
                                    }
                                    else
                                    {
                                        list[j].ActualQuantity += Math.Round(Convert.ToDecimal(qty), 3);
                                    }
                                }
                            }
                            else
                            {
                                list[0].ActualQuantity += Math.Round(Convert.ToDecimal(qty), 3);
                                if (list[0].ActualQuantity >= list[0].Quantity)
                                {
                                    list[0].Status = "已完成";
                                }
                                upDetail.Add(list[0]);
                            }
                        }
                        #endregion

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());
                        trans.OldLotId = "";
                        trans.OldSublotId = "";
                        trans.OldExpirationDate = null;
                        trans.NewExpirationDate = Convert.ToDateTime(expirationDate);
                        trans.Quantity = Math.Round(Convert.ToDecimal(qty), 3);
                        trans.QuantityUomId = unitID;
                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Create Inventory";
                        trans.Comment = "扫码收货-创建库存";
                        trans.OldEquipmentId = "";
                        trans.NewEquipmentId = reqModel.equipmentId;
                        trans.OldContainerId = "";
                        trans.NewContainerId = "";
                        trans.OldLotExternalStatus = "";
                        trans.OldSublotExternalStatus = "";
                        trans.NewLotExternalStatus = "";
                        trans.NewSublotExternalStatus = "";

                        #endregion

                        addInvent.Add(request);

                    }

                    _unitOfWork.BeginTran();

                    bool resultSave = false;
                    if (addInvent.Count > 0)
                    {
                        resultSave = await _dal.Add(addInvent) > 0;
                    }
                    bool resultSave1 = false;
                    if (addLot.Count > 0)
                    {
                        resultSave1 = await _materialLotEntityDal.Add(upLot) > 0;
                    }
                    bool resultSave2 = false;
                    if (upLot.Count > 0)
                    {
                        resultSave2 = await _materialLotEntityDal.Update(upLot);
                    }
                    bool resultSave3 = false;
                    if (addSubLot.Count > 0)
                    {
                        resultSave3 = await _materialSubLotServicesDal.Add(addSubLot) > 0;
                    }
                    bool resultSave4 = false;
                    if (addTran.Count > 0)
                    {
                        resultSave4 = await _MaterialTransferEntityDal.Add(addTran) > 0;
                    }
                    bool resultSave5 = false;
                    if (addUnit.Count > 0)
                    {
                        resultSave5 = await _UnitEntityDal.Add(addUnit) > 0;
                    }
                    bool resultSave6 = false;
                    if (upDetail.Count > 0)
                    {
                        resultSave6 = await _RequestDetailEntity.Update(upDetail);
                    }
                    if (!resultSave || !resultSave1 || !resultSave2 || !resultSave3 || !resultSave4 || !resultSave5)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "物料接收失败,保存失败";
                        return result;
                    }
                    _unitOfWork.CommitTran();

                    result.msg = "接收成功";
                    result.success = true;
                    return result;
                }
                else
                {
                    result.msg = "获取接口失败，失败原因" + models.msg;
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "获取接口失败，失败原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }

        /// <summary>
        /// 有效期换算
        /// </summary>
        /// <param name="addNumber">增加数</param>
        /// <param name="type">单位</param>
        /// <param name="dateTime">传入时间</param>
        /// <returns></returns>
        public DateTime GetExpirationDate(int addNumber, string type, DateTime dateTime)
        {

            switch (type)
            {
                case "Y":
                    {
                        return Convert.ToDateTime(dateTime.AddYears(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "Q":
                    {
                        return Convert.ToDateTime(dateTime.AddYears(addNumber * 3).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "M":
                    {
                        return Convert.ToDateTime(dateTime.AddMonths(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "W":
                    {
                        return Convert.ToDateTime(dateTime.AddDays(addNumber * 7).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "D":
                    {
                        return Convert.ToDateTime(dateTime.AddDays(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "h":
                    {
                        return Convert.ToDateTime(dateTime.AddHours(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "m":
                    {
                        return Convert.ToDateTime(dateTime.AddMinutes(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                case "s":
                    {
                        return Convert.ToDateTime(dateTime.AddSeconds(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
                default:
                    {
                        return Convert.ToDateTime(dateTime.AddDays(addNumber).ToString("yyyy-MM-dd") + " 23:59:59");
                    }
            }
        }


        /// <summary>
        /// 新增库存信息
        /// </summary>
        /// <param name="request">库存实体</param>
        /// <param name="subLotCode">子批次号</param>
        /// <param name="lotCode">批次号</param>
        /// <param name="materialId">物料ID</param>
        /// <returns></returns>
        //[HttpPost]
        public async Task<bool> AddInventory([FromBody] MaterialInventoryModel mModel)
        {
            try
            {
                string subLotCode = mModel.SubLotCode;

                string lotCode = mModel.LotCode;
                string materialId = mModel.MaterialId;
                var data = new MessageModel<string>();

                MaterialInventoryEntity request = new MaterialInventoryEntity();
                request.Quantity = Math.Round(Convert.ToDecimal(mModel.Quantity), 3); // Convert.ToDecimal(mModel.Quantity);
                //默认是kg
                request.QuantityUomId = "5";
                request.EquipmentId = mModel.EquipmentId;
                request.ContainerId = mModel.ContainerId;

                request.StorageLocation = "";
                if (string.IsNullOrEmpty(request.ID))
                {
                    _unitOfWork.BeginTran();

                    //创建数据
                    request.Create(_user.Name.ToString());

                    string sublotIdID = String.Empty;
                    String lotID = String.Empty;

                    bool saveLot = true;
                    bool saveSubLot = true;

                    //新增批次（判断是否存在批次信息，无创建，有无需操作）
                    List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == mModel.MaterialId);
                    if (lotList != null && lotList.Count > 0)
                    {
                        lotID = lotList[0].ID;
                        MaterialLotEntity upModel = new MaterialLotEntity();
                        upModel = lotList[0];
                        upModel.Modify(upModel.ID, _user.Name.ToString());
                        // upModel.ExpirationDate = Convert.ToDateTime(mModel.ExpTime);
                        await _materialLotEntityDal.Update(upModel);

                        request.LotId = lotID;
                    }
                    else
                    {
                        DateTime expirationDate = DateTime.Now;
                        if (!string.IsNullOrEmpty(mModel.ExpTime))
                        {
                            expirationDate = Convert.ToDateTime(mModel.ExpTime);
                        }

                        #region 有效期

                        MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);
                        if (materialData != null)
                        {
                            string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                            int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                            expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                        }

                        #endregion

                        ////这里查询物料属性
                        //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mModel.MaterialId);
                        //if (property != null)
                        //{
                        //    string propertyValue = property.PropertyValue;
                        //    if (propertyValue != string.Empty)
                        //    {
                        //        string type = property.MaterialType;
                        //        int value = Convert.ToInt32(propertyValue);
                        //        expirationDate = expirationDate.AddDays(value);


                        //        //string type = property.MaterialType;

                        //        //if (type.Contains("小时"))
                        //        //{
                        //        //    int value = Convert.ToInt32(propertyValue);
                        //        //    expirationDate = Convert.ToDateTime(expirationDate.AddHours(value).ToString("yyyy-MM-dd") + " 00:00:00").AddSeconds(-1);
                        //        //}
                        //        //else if (type.Contains("天"))
                        //        //{
                        //        //    int value = Convert.ToInt32(propertyValue);
                        //        //    expirationDate = expirationDate.AddDays(value);
                        //        //}
                        //    }
                        //}

                        //保存批次
                        MaterialLotEntity model = new MaterialLotEntity();
                        model.Create(_user.Name.ToString());
                        lotID = model.ID;
                        model.LotId = lotCode;
                        model.MaterialId = materialId;
                        model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                        model.ExternalStatus = "2";
                        model.Type = "0";
                        request.LotId = lotID;
                        saveLot = await _materialLotEntityDal.Add(model) > 0;
                    }

                    //新增子批次（判断是否存在批次信息，无创建，有无需操作）
                    List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == subLotCode);

                    if (subLotList != null && subLotList.Count > 0)
                    {
                        return false;
                    }
                    else
                    {
                        MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                        submodel.Create(_user.Name.ToString());
                        sublotIdID = submodel.ID;


                        #region 构造实体

                        SSCCModel models = new SSCCModel();
                        models.Type = "";
                        models.NextCode = "";
                        models.MaxCode = "";
                        models.MinCode = "";
                        models.Prefix = "";
                        models.TableName = "";
                        models.TableId = "";
                        models.SequenceType = "";
                        models.ResetType = "";
                        models.FeatureId = "";
                        models.pageIndex = 1;
                        models.pageSize = 10;
                        models.orderByFileds = "";
                        string token = _user.GetToken();
                        var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                        if (ssccString.success == true)
                        {
                            //ssccString = ssccString.response;
                            subLotCode = ssccString.response.ToString();
                        }

                        submodel.SubLotId = subLotCode;
                        #endregion

                        submodel.ExternalStatus = "3";
                        submodel.Type = "0";
                        request.SublotId = sublotIdID;

                        saveSubLot = await _materialSubLotServicesDal.Add(submodel) > 0;
                    }

                    //新增库存               
                    bool saveIninventory = await _dal.Add(request) > 0;

                    if (!saveIninventory || !saveLot || !saveSubLot)
                    {
                        _unitOfWork.RollbackTran();

                        return false;
                    }
                    else
                    {
                        _unitOfWork.CommitTran();
                        return true;
                    }
                }
                else
                {
                    data.success = await _dal.Update(request);
                    if (data.success)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();

                return false;
            }

        }

        public class InventObj
        {
            public string additionalProp1 { get; set; }
            public string additionalProp2 { get; set; }
            public string additionalProp3 { get; set; }
        }


        public async Task<MessageModel<string>> NewAddInventory(MaterialInventoryModel mModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            //while (true)
            //{
            //    await ReadCallData("1", 1, "1");
            //    await Task.Delay(1000);
            //}

            try
            {
                List<MaterialInventoryEntity> requests = new List<MaterialInventoryEntity>();
                List<MaterialLotEntity> LotModels = new List<MaterialLotEntity>();
                List<MaterialLotEntity> UpLotModels = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> submodels = new List<MaterialSubLotEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();

                //传入参数为空给提示信息
                if (mModel.EquipmentId == null || mModel.EquipmentId == "")
                {
                    result.msg = "位置不能为空";
                    return result;
                }
                if (mModel.MaterialId == null || mModel.MaterialId == "")
                {
                    result.msg = "物料不能为空";
                    return result;
                }
                if (mModel.LotCode == null || mModel.LotCode == "")
                {
                    result.msg = "批次不能为空";
                    return result;
                }

                if (mModel.Quantity == null || mModel.Quantity == "")
                {
                    result.msg = "数量不能为空";
                    return result;
                }
                decimal Quantity;
                try
                {
                    //这里用fullbage
                    Quantity = Convert.ToDecimal(mModel.FullbagWeight);
                }
                catch (Exception)
                {
                    result.msg = "数量必须是数字";
                    return result;
                }
                int bags = Convert.ToInt32(mModel.Bags);
                String lotId = String.Empty;
                // string subLotCode = "";
                string sublotIdID = String.Empty;
                DateTime expirationDate = DateTime.Now;// = Convert.ToDateTime(mModel.ExpTime);


                List<Object> objList = new List<Object>();

                //这里查询物料
                Model.Models.MaterialEntity materialModel = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);

                if (materialModel == null)
                {
                    result.msg = "请选择物料";
                    return result;
                }
                string mCode = materialModel.Code;
                string mName = materialModel.Name;

                UnitmanageEntity unitModel = await _UnitEntityDal.FindEntity(p => p.ID == mModel.UnitID);
                if (unitModel == null)
                {
                    result.msg = "请选择单位";
                    return result;
                }
                //这里查询单位
                string uName = unitModel.Name;


                //这里查询模板ID 
                //PrintTemplete                   库存标签打印
                //PrintBagTemplete                备料包库存标签模板
                //PrintPalletTemplete             备料托盘库存标签模板

                string templateID = string.Empty;
                string templateclassID = string.Empty;
                string printID = string.Empty;
                if (!string.IsNullOrEmpty(mModel.SelsectPrinter) && mModel.SelsectPrinter != "false")
                {

                    printID = mModel.PeprintSource;
                    if (string.IsNullOrEmpty(printID))
                    {
                        result.msg = "请选择打印机";
                        return result;
                    }
                    var models = await GetTeampID(mModel.EquipmentId, "PrintTemplete");
                    if (models == null)
                    {
                        result.msg = "请配置打印机";
                        return result;
                    }
                    templateID = models.ID;
                    templateclassID = models.TemplateClassId; ;

                }


                //这里查询物料
                for (int i = 0; i < bags; i++)
                {

                    string lotCode = mModel.LotCode;
                    string materialId = mModel.MaterialId;

                    var data = new MessageModel<string>();

                    #region 创建库存
                    MaterialInventoryEntity request = new MaterialInventoryEntity();
                    request.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3);  //Convert.ToDecimal(Quantity);
                    //创建数据
                    request.Create(_user.Name.ToString());
                    //默认是kg
                    request.QuantityUomId = mModel.UnitID;//"5";
                    request.EquipmentId = mModel.EquipmentId;
                    request.ContainerId = mModel.ContainerId == null ? "" : mModel.ContainerId;
                    request.StorageLocation = "";

                    //if (!string.IsNullOrEmpty(request.ID))
                    //{
                    //    //库存创建失败，库存ID必须为空
                    //    result.msg = "库存创建失败，库存ID必须为空";
                    //    return result;
                    //}

                    #endregion

                    if (i == 0)
                    {
                        #region 创建批次                  

                        //新增批次（判断是否存在批次信息，无创建，有无需操作）
                        List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == mModel.MaterialId);
                        if (lotList != null && lotList.Count > 0)
                        {
                            lotId = lotList[0].ID;
                            MaterialLotEntity upModel = new MaterialLotEntity();
                            upModel = lotList[0];
                            upModel.ExternalStatus = mModel.SubStatus; //(状态1: R 上锁 2:U 解锁)
                            upModel.Modify(upModel.ID, _user.Name.ToString());
                            expirationDate = lotList[0].ExpirationDate;
                            // await _materialLotEntityDal.Update(upModel);
                            UpLotModels.Add(upModel);
                            //获取批次ID
                            // lotID = lotID;

                        }
                        else
                        {
                            #region 有效期

                            MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);
                            if (materialData != null)
                            {
                                string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                                int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                                expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                            }

                            #endregion

                            //这里查询物料属性
                            //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mModel.MaterialId);
                            //if (property != null)
                            //{
                            //    string propertyValue = property.PropertyValue;
                            //    if (propertyValue != string.Empty)
                            //    {
                            //        string type = property.MaterialType;

                            //        //if (type.Contains("小时"))
                            //        //{
                            //        //    int value = Convert.ToInt32(propertyValue);
                            //        //    expirationDate = Convert.ToDateTime(expirationDate.AddHours(value).ToString("yyyy-MM-dd") + " 00:00:00").AddSeconds(-1);
                            //        //}
                            //        //else if (type.Contains("天"))
                            //        //{
                            //        int value = Convert.ToInt32(propertyValue);
                            //        expirationDate = expirationDate.AddDays(value);
                            //        //}
                            //    }
                            //}
                            //保存批次
                            MaterialLotEntity model = new MaterialLotEntity();
                            model.Create(_user.Name.ToString());
                            lotId = model.ID;
                            model.LotId = lotCode;
                            model.MaterialId = materialId;
                            model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                            model.ExternalStatus = mModel.SubStatus; //(1:B 上锁 2:Q 3:U 解锁)
                            model.Type = "0";
                            // request.LotId = lotID;
                            LotModels.Add(model);

                        }



                        #endregion
                    }

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    //  trans.OldStorageLocation = storage_location;
                    // trans.NewStorageLocation = storage_location;
                    trans.OldLotId = "";

                    trans.OldSublotId = "";

                    trans.OldExpirationDate = null;
                    trans.NewExpirationDate = expirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3); // Convert.ToDecimal(Quantity);
                    trans.QuantityUomId = mModel.UnitID;
                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Create Inventory";
                    trans.Comment = "创建库存";
                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = "";
                    trans.NewEquipmentId = mModel.EquipmentId;
                    trans.OldContainerId = "";
                    trans.NewContainerId = mModel.ContainerId;
                    trans.OldLotExternalStatus = "";
                    trans.OldSublotExternalStatus = "";
                    trans.NewMaterialId = materialId;
                    trans.OldMaterialId = materialId;
                    trans.NewLotExternalStatus = mModel.SubStatus;


                    #endregion

                    string subLotCode = "";

                    if (mModel.IsSSCC.ToUpper() == "TRUE")
                    {


                        #region 生成子批次ID(需补充)

                        #region 构造实体

                        SSCCModel models = new SSCCModel();
                        models.Type = "";
                        models.NextCode = "";
                        models.MaxCode = "";
                        models.MinCode = "";
                        models.Prefix = "";
                        models.TableName = "";
                        models.TableId = "";
                        models.SequenceType = "";
                        models.ResetType = "";
                        models.FeatureId = "";
                        models.pageIndex = 1;
                        models.pageSize = 10;
                        models.orderByFileds = "";
                        string token = _user.GetToken();
                        var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                        if (ssccString.success == true)
                        {
                            //ssccString = ssccString.response;
                            subLotCode = ssccString.response.ToString();
                        }
                        #endregion

                        #endregion

                        #region 创建子批次

                        //新增子批次（判断是否存在批次信息，无创建，有,无需操作）
                        List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == subLotCode);

                        if (subLotList != null && subLotList.Count > 0)
                        {
                            result.msg = "子批次已存在";
                            return result;
                        }
                        else
                        {
                            MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                            submodel.Create(_user.Name.ToString());
                            sublotIdID = submodel.ID;
                            submodel.SubLotId = subLotCode;

                            if (mModel.SubStatus == "1")
                            {
                                submodel.ExternalStatus = "1";
                            }

                            else
                            {
                                submodel.ExternalStatus = "3";
                            }

                            trans.NewSublotExternalStatus = submodel.ExternalStatus;
                            submodel.Type = "0";
                            request.SublotId = sublotIdID;
                            submodel.Comment = mModel.Remark;
                            submodels.Add(submodel);
                        }

                        #endregion
                    }
                    trans.NewSublotId = sublotIdID;
                    trans.NewLotId = lotId;
                    trans.OldLotId = lotId;
                    listTrans.Add(trans);
                    request.LotId = lotId;
                    requests.Add(request);

                    //加入数据源
                    object obj = new PPM.Model.ViewModels.MKM.PrintView.VerifiyDetailTotalData
                    {
                        ///WMS标签数据源
                        //Supplier_Name = "",
                        //Material_Name = mName,
                        //Material_Code = mCode,
                        //Lot_Code = lotCode,
                        //Material_Inventory_Qty = Quantity.ToString(),
                        //Unit_Code = uName,
                        //Sublot_Code = subLotCode,

                        Plant = "三厂",
                        Material_Name = mName,
                        Material_Code = mCode,
                        Batch_Code = lotCode,
                        NUM = Quantity.ToString(),
                        PO_NUM = "",// result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                        Unit = uName,
                        Receive_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                        SSCC = subLotCode

                        //User = _user.Name.ToString()

                    };
                    objList.Add(obj);
                }

                _unitOfWork.BeginTran();

                if (requests != null && requests.Count > 0)
                {
                    await _dal.Add(requests);
                }
                if (LotModels != null && LotModels.Count > 0)
                {
                    await _materialLotEntityDal.Add(LotModels);
                }
                if (UpLotModels != null && UpLotModels.Count > 0)
                {
                    await _materialLotEntityDal.Update(UpLotModels);
                }
                if (submodels != null && submodels.Count > 0)
                {
                    await _materialSubLotServicesDal.Add(submodels);
                }
                if (listTrans != null && listTrans.Count > 0)
                {
                    await _MaterialTransferEntityDal.Add(listTrans);
                }

                _unitOfWork.CommitTran();

                #region 打印

                #region 模板和数据源

                var resultMsg = new MessageModel<string>();


                if (!string.IsNullOrEmpty(mModel.SelsectPrinter) && mModel.SelsectPrinter != "false")
                {
                    for (int i = 0; i < objList.Count; i++)
                    {
                        //临时赋值
                        //templateID = "02407300-8490-6062-163e-0370f6000003";
                        //templateclassID = "02407300-8490-6062-163e-0370f6000006";
                        resultMsg = await PrintByCode(printID, templateID, templateclassID, objList, objList[i]);
                    }
                }

                #endregion

                // 
                //  return result;

                #endregion

                //提示成功
                result.success = true;
                result.msg = "库存新增成功!" + resultMsg.msg;
                return result;


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增失败";
                return result;
            }

        }

        /// <summary>
        /// 豉油厂新增
        /// </summary>
        /// <param name="mModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> NewAddInvenCYC(MaterialInventoryModel mModel)
        {
            string works = mModel.WorkShop;
            string equpID = mModel.EquipmentId;
            mModel.EquipmentId = works;
            mModel.WorkShop = equpID;
            var result = new MessageModel<string>();
            result.success = false;
            string printID = mModel.PrintId;
            try
            {
                List<MaterialInventoryEntity> requests = new List<MaterialInventoryEntity>();
                List<MaterialLotEntity> LotModels = new List<MaterialLotEntity>();
                List<MaterialLotEntity> UpLotModels = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> submodels = new List<MaterialSubLotEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();

                #region MyRegion  判断空数据和校验

                //传入参数为空给提示信息
                if (mModel.EquipmentId == null || mModel.EquipmentId == "")
                {
                    result.msg = "位置不能为空";
                    return result;
                }
                if (mModel.MaterialId == null || mModel.MaterialId == "")
                {
                    result.msg = "物料不能为空";
                    return result;
                }
                if (mModel.LotCode == null || mModel.LotCode == "")
                {
                    result.msg = "批次不能为空";
                    return result;
                }

                if (mModel.Quantity == null || mModel.Quantity == "")
                {
                    result.msg = "数量不能为空";
                    return result;
                }
                decimal Quantity;
                try
                {
                    //这里用fullbage
                    Quantity = Convert.ToDecimal(mModel.FullbagWeight);
                }
                catch (Exception)
                {
                    result.msg = "数量必须是数字";
                    return result;
                }

                int bags = Convert.ToInt32(mModel.Bags);
                String lotId = String.Empty;
                // string subLotCode = "";
                string sublotIdID = String.Empty;
                //输入的有效期
                DateTime expirationDate = Convert.ToDateTime(mModel.CreateDate + " 23:59:59");


                List<Object> objList = new List<Object>();

                //这里查询物料
                Model.Models.MaterialEntity materialModel = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);

                if (materialModel == null)
                {
                    result.msg = "请选择物料";
                    return result;
                }
                string materialId = materialModel.ID;
                //这里调用接口拿数据       

                string mCode = materialModel.Code;
                string mName = materialModel.Name;

                UnitmanageEntity unitModel = await _UnitEntityDal.FindEntity(p => p.ID == mModel.UnitID);
                if (unitModel == null)
                {
                    result.msg = "请选择单位";
                    return result;
                }
                //这里查询单位
                string uName = unitModel.Name;

                #endregion

                //查询豉油厂节点
                EquipmentEntity eModel = await _equipmentEntity.FindEntity(p => p.EquipmentCode == "SaucePlant");

                if (eModel == null)
                {
                    result.msg = "请配置豉油厂节点";
                    return result;
                }

                #region 打印WMS标签，获取子批次号

                decimal qtyWMS = Math.Round(Convert.ToDecimal(string.IsNullOrEmpty(mModel.Quantity) ? 0 : mModel.Quantity), 3);
                qtyWMS = Math.Round(qtyWMS, 3);
                //构造实体
                PrintSendDataItems wmsPrinitGet = new PrintSendDataItems
                {
                    itemcode = materialModel.Code,
                    itemunitcode = uName,
                    batch = mModel.LotCode,
                    printtype = "semiP",  // retwarTCItem（退仓打印）、semiP（原物料、半成品入仓打印、 mesPrint mes打印
                    qty = qtyWMS, // Convert.ToDecimal(mModel.Quantity),
                    workOrderNo = mModel.PRO_NO,//工单号
                    type = 1,   //1新增、2删除
                    invstatus = 2, // 2(正常)、3(冻结)、4(质检)
                    workshopStockcode = mModel.WorkShop
                };

                string msg = "物料Code/单位:" + materialModel.Code + "/" + uName + ",批次:" + mModel.LotCode + ",retwarTCItem:" + "semiP" + ",qty:" + Convert.ToDecimal(mModel.Quantity)
                    + ",workOrderNo:" + mModel.PRO_NO + ",type:1" + ",invstatus:2" + ",workshopStockcode:" + mModel.WorkShop;
                //SerilogServer.LogDebug(msg, "豉油加工厂.log");
                List<PrintSendDataItems> wList = new List<PrintSendDataItems>();
                wList.Add(wmsPrinitGet);

                //这里去拿WMS的数据
                var response1 = await _requestInventoryViewServices.PrintLabelSynchro(wList);

                if (response1.successed == true)
                {
                    PrintResult scanModel = response1.Response;
                    //获取批次号和有效期
                    if (scanModel.flag == true)
                    {
                        if (scanModel.data != null && scanModel.data.Count > 0)
                        {
                            #region 单独处理批次

                            string[] lotNO = scanModel.data.GroupBy(p => p.batch).Select(p => p.Key).ToArray();

                            if (lotNO.Length > 1)
                            {
                                SerilogServer.LogDebug(msg, "豉油加工厂.log");
                                result.msg = "WMS接口调用成功，存在多批次情况";
                                return result;
                            }
                            #endregion

                            string sapProNo = mModel.PRO_NO;
                            string newSSCC = string.Empty;
                            //这里返回需要创建库存的数据
                            for (int j = 0; j < scanModel.data.Count; j++)
                            {
                                string lotCode = scanModel.data[j].batch;
                                //有效期、供应商、批次
                                string stringDate = scanModel.data[j].validity_date;
                                string supperName = scanModel.data[j].suppliername;
                                string supperCode = scanModel.data[j].suppliercode;
                                newSSCC = scanModel.data[j].itembarcode;
                                string qty = scanModel.data[j].qty;//=="0" ? scanModel.data[j];
                                stringDate = stringDate.Replace('.', '-');
                                string newSubID = string.Empty;
                                try
                                {
                                    DateTime date = DateTime.ParseExact(stringDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                                    stringDate = date.ToString("yyyy-MM-dd") + " 23:59:59";
                                    expirationDate = Convert.ToDateTime(date.ToString("yyyy-MM-dd") + " 23:59:59");
                                }
                                catch
                                {
                                    stringDate = DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00";
                                }

                                if (j == 0)
                                {
                                    #region 创建批次                  

                                    //新增批次（判断是否存在批次信息，无创建，有无需操作）
                                    List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == materialId);
                                    if (lotList != null && lotList.Count > 0)
                                    {
                                        lotId = lotList[0].ID;
                                        MaterialLotEntity upModel = new MaterialLotEntity();
                                        upModel = lotList[0];
                                        upModel.ExternalStatus = mModel.SubStatus; //(状态1: R 上锁 2:U 解锁)
                                        upModel.Modify(upModel.ID, _user.Name.ToString());
                                        expirationDate = lotList[0].ExpirationDate;
                                        UpLotModels.Add(upModel);
                                    }
                                    else
                                    {
                                        //保存批次
                                        MaterialLotEntity model = new MaterialLotEntity();
                                        model.Create(_user.Name.ToString());
                                        lotId = model.ID;
                                        model.LotId = lotCode;
                                        model.MaterialId = materialId;
                                        model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                                        model.ExternalStatus = mModel.SubStatus; //(1:B 上锁 2:Q 3:U 解锁)
                                        model.Type = "0";
                                        model.Suppiercode = supperCode;
                                        model.Suppiername = supperName;

                                        if (!string.IsNullOrEmpty(newSSCC))
                                        {
                                            model.ExpirationDate = Convert.ToDateTime(stringDate);
                                        }
                                        LotModels.Add(model);

                                    }

                                    #endregion
                                }

                                #region 创建库存

                                MaterialInventoryEntity request = new MaterialInventoryEntity();
                                request.Quantity = Math.Round(Convert.ToDecimal(qty), 3); //Convert.ToDecimal(qty);

                                if (request.Quantity <= 0)
                                {
                                    result.msg = "添加失败，WMS接口调用成功，返回库存数量为" + request.Quantity;
                                    SerilogServer.LogDebug(result.msg, "豉油加工厂.log");
                                    return result;
                                }
                                //桶号
                                request.Bucketnum = (j + 1).ToString();
                                //创建数据
                                request.Create(_user.Name.ToString());
                                request.QuantityUomId = mModel.UnitID;//"5";
                                request.EquipmentId = mModel.EquipmentId;
                                request.ContainerId = mModel.ContainerId == null ? "" : mModel.ContainerId;
                                request.StorageLocation = "";

                                #endregion

                                #region 写入转移记录

                                //写入历史记录
                                MaterialTransferEntity trans = new MaterialTransferEntity();
                                trans.Create(_user.Name.ToString());

                                trans.OldLotId = "";
                                trans.OldSublotId = "";
                                trans.OldExpirationDate = null;
                                trans.NewExpirationDate = expirationDate;
                                trans.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3); // Convert.ToDecimal(Quantity);
                                trans.QuantityUomId = mModel.UnitID;

                                trans.Type = "Create Inventory";
                                trans.Comment = "豉油厂创建库存";
                                trans.OldEquipmentId = "";
                                trans.NewEquipmentId = mModel.EquipmentId;
                                trans.OldContainerId = "";
                                trans.NewContainerId = mModel.ContainerId;
                                trans.OldLotExternalStatus = "";
                                trans.OldSublotExternalStatus = "";
                                trans.OldMaterialId = materialId;
                                trans.NewMaterialId = materialId;
                                trans.NewLotExternalStatus = mModel.SubStatus;
                                trans.MesProno = sapProNo;

                                #endregion

                                string subLotCode = "";

                                #region 创建子批次

                                //新增子批次（判断是否存在批次信息，无创建，有,无需操作）
                                List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == newSSCC);

                                if (subLotList != null && subLotList.Count > 0)
                                {
                                    result.msg = "子批次已存在";
                                    return result;
                                }
                                else
                                {
                                    MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                                    submodel.Create(_user.Name.ToString());
                                    sublotIdID = submodel.ID;
                                    submodel.SubLotId = newSSCC;
                                    if (mModel.SubStatus == "1")
                                    {
                                        submodel.ExternalStatus = "1";
                                    }
                                    else
                                    {
                                        submodel.ExternalStatus = "3";
                                    }

                                    trans.NewSublotExternalStatus = submodel.ExternalStatus;
                                    submodel.Type = "0";
                                    request.SublotId = sublotIdID;
                                    submodel.Comment = mModel.Remark;
                                    submodels.Add(submodel);
                                }

                                #endregion

                                trans.NewSublotId = sublotIdID;
                                trans.NewLotId = lotId;
                                listTrans.Add(trans);
                                request.LotId = lotId;
                                requests.Add(request);
                            }

                            _unitOfWork.BeginTran();

                            if (requests != null && requests.Count > 0)
                            {
                                await _dal.Add(requests);
                            }
                            if (LotModels != null && LotModels.Count > 0)
                            {
                                await _materialLotEntityDal.Add(LotModels);
                            }
                            if (UpLotModels != null && UpLotModels.Count > 0)
                            {
                                await _materialLotEntityDal.Update(UpLotModels);
                            }
                            if (submodels != null && submodels.Count > 0)
                            {
                                await _materialSubLotServicesDal.Add(submodels);
                            }
                            if (listTrans != null && listTrans.Count > 0)
                            {
                                await _MaterialTransferEntityDal.Add(listTrans);
                            }

                            _unitOfWork.CommitTran();


                            #region 打印

                            #region 模板和数据源


                            var resultMsg1 = new MessageModel<string>();

                            if (!string.IsNullOrEmpty(mModel.SelsectPrinter) && mModel.SelsectPrinter != "false")
                            {

                                if (mModel.isYLJGC == true)
                                {
                                    //打印备料标签
                                    for (int i = 0; i < requests.Count; i++)
                                    {

                                        //List<object> objs = new List<object>();
                                        //objs.Add(inventViewData);                FactoryRoom         
                                        if (mModel.FactoryRoom == "CY")
                                        {
                                            var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(requests[i].ID, "");
                                            //执行打印
                                            await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                                        }
                                        else
                                        {
                                            var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(requests[i].ID, "");
                                            //执行打印
                                            await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                                        }

                                    }
                                }
                                else
                                {
                                    for (int i = 0; i < objList.Count; i++)
                                    {
                                        var inventData = await _IPrintSelectViewServices.GetInventLabel(requests[i].ID, "");
                                        List<object> objs = new List<object>();
                                        objs.Add(inventData);
                                        resultMsg1 = await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                                    }
                                }

                            }

                            #endregion

                            #endregion

                            //提示成功
                            result.success = true;
                            result.msg = "库存新增成功!" + resultMsg1.msg;
                            return result;
                        }
                        else
                        {
                            SerilogServer.LogDebug(msg, "豉油加工厂.log");
                            result.msg = "WMS接口调用成功，无数据返回";
                            return result;

                        }
                    }
                    else
                    {
                        SerilogServer.LogDebug(msg, "豉油加工厂.log");

                        result.msg = "WMS接口调用成功，无数据返回" + scanModel.flag + scanModel.msg;
                        return result;

                    }
                }
                else
                {
                    SerilogServer.LogDebug(msg, "豉油加工厂.log");
                    result.msg = "WMS接口调用失败" + response1.msg;
                    return result;

                }

                #endregion                
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增失败";
                return result;
            }

        }

        public async Task<MessageModel<string>> NewAddInventoryByTotal(MaterialInventoryModel mModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                List<MaterialInventoryEntity> requests = new List<MaterialInventoryEntity>();
                List<MaterialLotEntity> LotModels = new List<MaterialLotEntity>();
                List<MaterialLotEntity> UpLotModels = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> submodels = new List<MaterialSubLotEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();

                //传入参数为空给提示信息
                if (mModel.EquipmentId == null || mModel.EquipmentId == "")
                {
                    result.msg = "位置不能为空";
                    return result;
                }
                if (mModel.MaterialId == null || mModel.MaterialId == "")
                {
                    result.msg = "物料不能为空";
                    return result;
                }
                if (mModel.LotCode == null || mModel.LotCode == "")
                {
                    result.msg = "批次不能为空";
                    return result;
                }

                if (mModel.Quantity == null || mModel.Quantity == "")
                {
                    result.msg = "数量不能为空";
                    return result;
                }
                decimal Quantity;
                try
                {
                    //这里用fullbage
                    Quantity = Convert.ToDecimal(mModel.FullbagWeight);
                }
                catch (Exception)
                {
                    result.msg = "数量必须是数字";
                    return result;
                }
                int bags = Convert.ToInt32(mModel.Bags);
                String lotId = String.Empty;
                // string subLotCode = "";
                string sublotIdID = String.Empty;
                //输入的有效期
                DateTime expirationDate = Convert.ToDateTime(mModel.CreateDate + " 23:59:59");


                List<Object> objList = new List<Object>();

                //这里查询物料
                Model.Models.MaterialEntity materialModel = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);

                if (materialModel == null)
                {
                    result.msg = "请选择物料";
                    return result;
                }
                string mCode = materialModel.Code;
                string mName = materialModel.Name;

                UnitmanageEntity unitModel = await _UnitEntityDal.FindEntity(p => p.ID == mModel.UnitID);
                if (unitModel == null)
                {
                    result.msg = "请选择单位";
                    return result;
                }
                //这里查询单位
                string uName = unitModel.Name;


                //这里查询模板ID 
                //PrintTemplete                   库存标签打印
                //PrintBagTemplete                备料包库存标签模板
                //PrintPalletTemplete             备料托盘库存标签模板

                string templateID = string.Empty;
                string templateclassID = string.Empty;
                string printID = string.Empty;
                if (!string.IsNullOrEmpty(mModel.SelsectPrinter) && mModel.SelsectPrinter != "false")
                {

                    printID = mModel.PrintId;
                    if (string.IsNullOrEmpty(printID))
                    {
                        result.msg = "请选择打印机";
                        return result;
                    }
                    //    var models = await GetTeampID(mModel.EquipmentId, "PrintTemplete");
                    //PrintSelectViewEntity pModel = await _PrintSelectViewEntityDal.FindEntity(p => p.ID == printID);
                    //if (pModel == null)
                    //{
                    //    result.msg = "请选择打印机";
                    //    return result;
                    //}

                    //if (string.IsNullOrEmpty(pModel.TemplateId) || string.IsNullOrEmpty(pModel.TemplateClassId))
                    //{
                    //    result.msg = "请配置打印机";
                    //    return result;
                    //}
                    ////templateID = models.ID;
                    ////templateclassID = models.TemplateClassId; ;

                    //templateID = pModel.TemplateClassId;
                    //templateclassID = pModel.TemplateClassId;

                    //mModel.TemplateId = templateID;
                    //mModel.TemplateClassId = templateclassID;
                }
                //这里查询物料
                for (int i = 0; i < bags; i++)
                {

                    string lotCode = Regex.Replace(mModel.LotCode, @"\s+", "");

                    //          string lotCode1 = mModel.LotCode;
                    string materialId = mModel.MaterialId;

                    var data = new MessageModel<string>();

                    #region 创建库存
                    MaterialInventoryEntity request = new MaterialInventoryEntity();
                    //判断是否含有零头袋子
                    if (mModel.IsZeroBag == true)
                    {
                        if (i == bags - 1)
                        {
                            //绑定零头袋子
                            request.Quantity = Math.Round(Convert.ToDecimal(mModel.ZeroBagNumber), 3); // Convert.ToDecimal(mModel.ZeroBagNumber);
                        }
                        else
                        {
                            request.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3);// Convert.ToDecimal(Quantity);
                        }
                    }
                    else
                    {
                        request.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3); //Convert.ToDecimal(Quantity);
                    }

                    //桶号
                    request.Bucketnum = (i + 1).ToString();
                    //创建数据
                    request.Create(_user.Name.ToString());
                    //默认是kg
                    request.QuantityUomId = mModel.UnitID;//"5";
                    request.EquipmentId = mModel.EquipmentId;
                    request.ContainerId = mModel.ContainerId == null ? "" : mModel.ContainerId;
                    request.StorageLocation = "";

                    //if (!string.IsNullOrEmpty(request.ID))
                    //{
                    //    //库存创建失败，库存ID必须为空
                    //    result.msg = "库存创建失败，库存ID必须为空";
                    //    return result;
                    //}

                    #endregion


                    #region 调用WMS接口获取属性

                    string stringDate = string.Empty;
                    string supperName = string.Empty;
                    string supperCode = string.Empty;
                    string newSSCC = string.Empty;

                    if (mModel.IsSSCC.ToUpper() == "TRUE")
                    {

                    }
                    #endregion

                    if (i == 0)
                    {
                        #region 创建批次                  

                        //新增批次（判断是否存在批次信息，无创建，有无需操作）
                        List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == mModel.MaterialId);
                        if (lotList != null && lotList.Count > 0)
                        {
                            lotId = lotList[0].ID;
                            MaterialLotEntity upModel = new MaterialLotEntity();
                            upModel = lotList[0];
                            upModel.ExternalStatus = mModel.SubStatus; //(状态1: R 上锁 2:U 解锁)


                            upModel.Modify(upModel.ID, _user.Name.ToString());

                            expirationDate = lotList[0].ExpirationDate;
                            // await _materialLotEntityDal.Update(upModel);
                            UpLotModels.Add(upModel);
                            //获取批次IDs  
                            // lotID = lotID;



                        }
                        else
                        {

                            #region 打印WMS标签，获取子批次号

                            //构造实体
                            PrintSendDataItems wmsPrinit = new PrintSendDataItems
                            {
                                itemcode = mCode,
                                itemunitcode = uName,
                                batch = lotCode,
                                printtype = "mesPrint",  // retwarTCItem（退仓打印）、semiP（原物料、半成品入仓打印、 mesPrint mes打印
                                qty = request.Quantity,
                                type = 1,   //1新增、2删除
                                invstatus = 2  // 2(正常)、3(冻结)、4(质检)
                            };
                            List<PrintSendDataItems> wmsList = new List<PrintSendDataItems>();
                            wmsList.Add(wmsPrinit);

                            //创建库存去拿数据
                            var response = await _requestInventoryViewServices.PrintLabelSynchro(wmsList);

                            if (response.successed == true)
                            {
                                PrintResult scanModel = response.Response;
                                //获取批次号和有效期
                                if (scanModel.flag == true)
                                {
                                    if (scanModel.data != null && scanModel.data.Count > 0)
                                    {
                                        //这里只会有一条数据
                                        for (int j = 0; j < scanModel.data.Count; j++)
                                        {
                                            if (j == 0)
                                            {
                                                //有效期、供应商、批次
                                                stringDate = scanModel.data[j].validity_date;
                                                supperName = scanModel.data[j].suppliername;
                                                supperCode = scanModel.data[j].suppliercode;
                                                //newSSCC = scanModel.data[j].itembarcode;

                                                stringDate = stringDate.Replace('.', '-');
                                                string newSubID = string.Empty;
                                                try
                                                {
                                                    DateTime date = DateTime.ParseExact(stringDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                                                    stringDate = date.ToString("yyyy-MM-dd") + " 23:59:59";
                                                    expirationDate = Convert.ToDateTime(date.ToString("yyyy-MM-dd") + " 23:59:59");
                                                }
                                                catch
                                                {
                                                    stringDate = DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00";
                                                }

                                            }
                                        }
                                    }
                                }
                            }

                            #endregion


                            #region 有效期

                            MaterialEntity materialData = await _materialEntitydal.FindEntity(p => p.ID == mModel.MaterialId);
                            if (materialData != null)
                            {
                                expirationDate = Convert.ToDateTime(mModel.CreateDate.ToString("yyyy-MM-dd") + " 23:59:59");
                                string type = materialData.Iprkz == null ? "d" : materialData.Iprkz;
                                int addNumber = materialData.Mhdhb == null ? 0 : materialData.Mhdhb.Value;
                                expirationDate = GetExpirationDate(addNumber, type, expirationDate);
                            }

                            #endregion

                            ////这里查询物料属性
                            //MMaterialPropertyViewEntity property = await _MaterialPropertyDal.FindEntity(p => p.PropertyCode == "ShelfLife" && p.MaterialId == mModel.MaterialId);
                            //if (property != null)
                            //{
                            //    string propertyValue = property.PropertyValue;
                            //    if (propertyValue != string.Empty)
                            //    {
                            //        string type = property.MaterialType;

                            //        //if (type.Contains("小时"))
                            //        //{
                            //        //    int value = Convert.ToInt32(propertyValue);
                            //        //    expirationDate = Convert.ToDateTime(expirationDate.AddHours(value).ToString("yyyy-MM-dd") + " 00:00:00").AddSeconds(-1);
                            //        //}
                            //        //else if (type.Contains("天"))
                            //        //{
                            //        int value = Convert.ToInt32(propertyValue);
                            //        expirationDate = expirationDate.AddMonths(value);//设置的是月份
                            //        //}
                            //    }
                            //}
                            //保存批次
                            MaterialLotEntity model = new MaterialLotEntity();
                            model.Create(_user.Name.ToString());
                            lotId = model.ID;
                            model.LotId = lotCode;
                            model.MaterialId = materialId;
                            model.ExpirationDate = expirationDate;//待定（这里需要去查询当前物料的有效期）
                            model.ExternalStatus = mModel.SubStatus; //(1:B 上锁 2:Q 3:U 解锁)
                            model.Type = "0";
                            model.Suppiercode = supperCode;
                            model.Suppiername = supperName;

                            if (!string.IsNullOrEmpty(newSSCC))
                            {
                                model.ExpirationDate = Convert.ToDateTime(stringDate);
                            }
                            // request.LotId = lotID;
                            LotModels.Add(model);

                        }
                        #endregion
                    }

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    //  trans.OldStorageLocation = storage_location;
                    // trans.NewStorageLocation = storage_location;
                    trans.OldLotId = "";

                    trans.OldSublotId = "";

                    trans.OldExpirationDate = null;
                    trans.NewExpirationDate = expirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(Quantity), 3); // Convert.ToDecimal(Quantity);
                    trans.QuantityUomId = mModel.UnitID;
                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Create Inventory";
                    trans.Comment = "创建库存";
                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = "";
                    trans.NewEquipmentId = mModel.EquipmentId;
                    trans.OldContainerId = "";
                    trans.NewContainerId = mModel.ContainerId;
                    trans.OldLotExternalStatus = "";
                    trans.OldSublotExternalStatus = "";
                    trans.OldMaterialId = materialId;
                    trans.NewMaterialId = materialId;

                    trans.NewLotExternalStatus = mModel.SubStatus;


                    #endregion

                    string subLotCode = "";

                    if (mModel.IsSSCC.ToUpper() == "TRUE")
                    {
                        #region 生成子批次ID(需补充)
                        //唯一码不用WMS产生的，使用MES唯一码
                        newSSCC = string.Empty;
                        if (string.IsNullOrEmpty(newSSCC))
                        {
                            #region 构造实体

                            SSCCModel models = new SSCCModel();
                            models.Type = "";
                            models.NextCode = "";
                            models.MaxCode = "";
                            models.MinCode = "";
                            models.Prefix = "";
                            models.TableName = "";
                            models.TableId = "";
                            models.SequenceType = "";
                            models.ResetType = "";
                            models.FeatureId = "";
                            models.pageIndex = 1;
                            models.pageSize = 10;
                            models.orderByFileds = "";
                            string token = _user.GetToken();
                            var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                            if (ssccString.success == true)
                            {
                                //ssccString = ssccString.response;
                                subLotCode = ssccString.response.ToString();
                            }
                            #endregion
                        }
                        else
                        {
                            subLotCode = newSSCC;
                        }

                        #endregion

                        #region 创建子批次

                        //新增子批次（判断是否存在批次信息，无创建，有,无需操作）
                        List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == subLotCode);

                        if (subLotList != null && subLotList.Count > 0)
                        {
                            result.msg = "子批次已存在";
                            return result;
                        }
                        else
                        {
                            MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                            submodel.Create(_user.Name.ToString());
                            sublotIdID = submodel.ID;
                            submodel.SubLotId = subLotCode;

                            if (mModel.SubStatus == "1")
                            {
                                submodel.ExternalStatus = "1";
                            }

                            else
                            {
                                submodel.ExternalStatus = "3";
                            }

                            trans.NewSublotExternalStatus = submodel.ExternalStatus;
                            submodel.Type = "0";
                            request.SublotId = sublotIdID;
                            submodel.Comment = mModel.Remark;
                            submodels.Add(submodel);
                        }

                        #endregion
                    }
                    trans.NewSublotId = sublotIdID;
                    trans.NewLotId = lotId;
                    listTrans.Add(trans);
                    request.LotId = lotId;
                    requests.Add(request);

                    //加入数据源
                    object obj = new PPM.Model.ViewModels.MKM.PrintView.VerifiyDetailTotalData
                    {
                        ///WMS标签数据源
                        //Supplier_Name = "",
                        //Material_Name = mName,
                        //Material_Code = mCode,
                        //Lot_Code = lotCode,
                        //Material_Inventory_Qty = Quantity.ToString(),
                        //Unit_Code = uName,
                        //Sublot_Code = subLotCode,
                        //User = _user.Name.ToString()

                        Plant = "三厂",
                        Material_Name = mName,
                        Material_Code = mCode,
                        Batch_Code = lotCode,
                        NUM = Quantity.ToString(),
                        PO_NUM = "",// result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                        Unit = uName,
                        Receive_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                        SSCC = subLotCode

                    };
                    objList.Add(obj);
                }

                _unitOfWork.BeginTran();

                if (requests != null && requests.Count > 0)
                {
                    await _dal.Add(requests);
                }
                if (LotModels != null && LotModels.Count > 0)
                {
                    await _materialLotEntityDal.Add(LotModels);
                }
                if (UpLotModels != null && UpLotModels.Count > 0)
                {
                    await _materialLotEntityDal.Update(UpLotModels);
                }
                if (submodels != null && submodels.Count > 0)
                {
                    await _materialSubLotServicesDal.Add(submodels);
                }
                if (listTrans != null && listTrans.Count > 0)
                {
                    await _MaterialTransferEntityDal.Add(listTrans);
                }

                _unitOfWork.CommitTran();

                #region 打印

                #region 模板和数据源

                var resultMsg = new MessageModel<string>();


                if (!string.IsNullOrEmpty(mModel.SelsectPrinter) && mModel.SelsectPrinter != "false")
                {

                    if (mModel.isYLJGC == true)
                    {
                        //打印备料标签
                        for (int i = 0; i < requests.Count; i++)
                        {

                            //List<object> objs = new List<object>();
                            //objs.Add(inventViewData);                FactoryRoom         
                            if (mModel.FactoryRoom == "CY")
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(requests[i].ID, "");
                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                            }
                            else
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(requests[i].ID, "");
                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                            }

                        }
                    }
                    else
                    {
                        for (int i = 0; i < objList.Count; i++)
                        {
                            var inventData = await _IPrintSelectViewServices.GetInventLabel(requests[i].ID, "");
                            List<object> objs = new List<object>();
                            objs.Add(inventData);
                            resultMsg = await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        }
                    }

                }

                #endregion

                // 
                //  return result;

                #endregion

                //提示成功
                result.success = true;
                result.msg = "库存新增成功!" + resultMsg.msg;
                return result;


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增失败" + ex.Message + ex.StackTrace;
                return result;
            }

        }

        /// <summary>
        /// 重新构造桶号和顺序，排序方式按物料-配方-重量 ， 升序
        /// </summary>
        /// <param name="moldel"></param>
        /// <returns></returns>
        public List<MaterialLabelSaveModel> ReorganizeData(List<MaterialLabelSaveModel> moldel)
        {

            if (moldel == null || moldel.Count <= 0) { return moldel; }
            //先重新整理桶号(筛选物料)
            var mCodes = moldel.GroupBy(p => new { p.Mcode, p.GDH }).Select(p => new { p.Key.Mcode, p.Key.GDH }).ToList();
            //重排桶号
            List<MaterialLabelSaveModel> reorganizeList = new List<MaterialLabelSaveModel>();
            for (int i = 0; i < mCodes.Count; i++)
            {
                string mCode = mCodes[i].Mcode;
                string proNo = mCodes[i].GDH;
                var searchData = moldel.Where(p => p.Mcode.Contains(mCode) && p.GDH.Contains(proNo)).ToList();
                for (int j = 0; j < searchData.Count; j++)
                {
                    searchData[j].TNumber = (j + 1).ToString();
                    reorganizeList.Add(searchData[j]);
                }
            }
            //排序(物料-工单-配方-数量)
            reorganizeList = reorganizeList.OrderBy(p => p.Mcode).ThenBy(p => p.PFH).ThenBy(p => p.GDH).ThenByDescending(p => Convert.ToDecimal(p.FullbagWeight)).ToList();
            return reorganizeList;
        }

        /// <summary>
        /// 标签显示数据
        /// </summary>
        /// <param name="mModels"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddInventByWMSLabel(List<MaterialLabelSaveModel> mModels)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                if (mModels == null || mModels.Count <= 0)
                {
                    result.msg = "请确认是否有添加的数据";
                    return result;
                }
                if (mModels.Where(p => string.IsNullOrEmpty(p.SubLotCode)).Count() > 0)
                {
                    result.msg = "请确认追溯码是否生成完成";
                    return result;
                }
                int count = mModels.Count;

                var disData = mModels.GroupBy(p => p.SubLotCode).Where(p => p.Count() > 1).Select(p => p.Key).ToList();

                if (disData != null && disData.Count > 0)
                {
                    string ssccs = string.Empty;
                    try
                    {

                        for (int i = 0; i < disData.Count; i++)
                        {
                            ssccs += disData[i].Trim() + ",";
                        }
                    }
                    catch
                    {

                    }

                    result.msg = "存在重复追溯码" + ssccs;
                    return result;
                }

                if (mModels.Where(p => string.IsNullOrEmpty(p.ExpTime)).Count() > 0)
                {
                    result.msg = "请确认有效期是否生成完成";
                    return result;
                }


                bool isPrint = mModels[0].isPrinter;
                string printID = mModels[0].SelsectPrinter;
                if (isPrint == true)
                {

                    if (string.IsNullOrEmpty(printID))
                    {
                        result.msg = "请选择打印机";
                        return result;
                    }
                }
                List<MaterialInventoryEntity> requests = new List<MaterialInventoryEntity>();
                List<MaterialLotEntity> LotModels = new List<MaterialLotEntity>();

                List<MaterialLotEntity> UpLotModels = new List<MaterialLotEntity>();
                List<MaterialLotEntity> searchLotData = new List<MaterialLotEntity>();
                List<MaterialSubLotEntity> submodels = new List<MaterialSubLotEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();

                List<Object> objList = new List<Object>();
                List<PrintSendDataItems> wmsList = new List<PrintSendDataItems>();
                int bags = 0;

                #region 处理下同批次问题(单独创建号批次信息)

                //按照物料和标签分组
                var lots = mModels.GroupBy(p => new { p.LotCode, p.MaterialId }).Select(p => new { p.Key.LotCode, p.Key.MaterialId }).ToList();
                for (int i = 0; i < lots.Count; i++)
                {
                    string lotCode = lots[i].LotCode;
                    string mID = lots[i].MaterialId;
                    List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == mID);

                    DateTime exTime = DateTime.Now;

                    try
                    {
                        exTime = Convert.ToDateTime(mModels[i].ExpTime);
                    }
                    catch (Exception)
                    {

                        throw;
                    }
                    string setLotStatus = "2";
                    //存在更新子批次有效期
                    if (lotList != null && lotList.Count > 0)
                    {
                        ////绑定数据
                        //MaterialLotEntity upModel = new MaterialLotEntity();
                        //upModel = lotList[0];                    
                        //upModel.ExpirationDate = exTime;
                        //searchLotData.Add(upModel);
                    }
                    else
                    {
                        MaterialLotEntity model = new MaterialLotEntity();
                        model.Create(_user.Name.ToString());
                        model.LotId = lotCode;
                        model.MaterialId = mID;
                        model.ExpirationDate = exTime;//待定（这里需要去查询当前物料的有效期）
                        model.ExternalStatus = "2"; //(1:R 上锁 2:U 解锁)默认为3
                        model.Type = "0";
                        searchLotData.Add(model);
                    }
                }

                #endregion

                //重新排序和整理桶号
                mModels = ReorganizeData(mModels);

                //循环所有需要新增的数据
                for (int i = 0; i < mModels.Count; i++)
                {
                    //基础属性                  
                    string lotCode = mModels[i].LotCode.Trim();
                    string subLotCode = mModels[i].SubLotCode.Trim();
                    string mID = mModels[i].MaterialId;
                    string lotID = string.Empty;
                    string subLotID = string.Empty;
                    string unitID = mModels[i].UnitID;
                    string fullBagWeight = mModels[i].FullbagWeight;
                    string equipmentId = mModels[i].EquipmentId;
                    string setLotStatus = "2";
                    string type = string.Empty;
                    string proID = mModels[i].ProID;
                    string batchID = mModels[i].BatchID;
                    string mName = mModels[i].Mname;
                    string mCode = mModels[i].Mcode;
                    string uName = mModels[i].UnitName;
                    string tNumber = mModels[i].TNumber;
                    string reamrk = mModels[i].Remark;
                    string cTIME = mModels[i].CreateTime == null ? DateTime.Now.ToString() : mModels[i].CreateTime;
                    if (Convert.ToDecimal(mModels[i].BagSize) == Convert.ToDecimal(mModels[i].FullbagWeight))
                    { type = "Full"; }
                    else { type = "Partial"; }
                    DateTime exTime = DateTime.Now;

                    try
                    {
                        exTime = Convert.ToDateTime(mModels[i].ExpTime);
                    }
                    catch (Exception)
                    {

                        throw;
                    }

                    //根据单包数量创建
                    if (string.IsNullOrEmpty(mModels[i].Bags))
                    {
                        mModels[i].Bags = "1";
                    }
                    bags = Convert.ToInt32(mModels[i].Bags);

                    for (int j = 0; j < bags; j++)
                    {
                        #region 批次

                        //判断是否存在批次信息
                        List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotCode && x.MaterialId == mID);
                        //存在更新子批次有效期
                        if (lotList != null && lotList.Count > 0)
                        {
                            //绑定数据
                            MaterialLotEntity upModel = new MaterialLotEntity();
                            upModel = lotList[0];
                            lotID = lotList[0].ID;
                            upModel.ExternalStatus = setLotStatus; //默认解锁状态(状态1: R 上锁 2:U 解锁)
                            upModel.Modify(upModel.ID, _user.Name.ToString());
                            upModel.ExpirationDate = exTime;
                            UpLotModels.Add(upModel);
                        }
                        else
                        {
                            var lotData = searchLotData.Where(x => x.LotId == lotCode && x.MaterialId == mID).ToList();
                            lotID = lotData[0].ID;

                            //LotModels.Add(lotData[0]);

                            //MaterialLotEntity model = new MaterialLotEntity();
                            //model.Create(_user.Name.ToString());
                            //lotID = model.ID;
                            //model.LotId = lotCode;
                            //model.MaterialId = mID;
                            //model.ExpirationDate = exTime;//待定（这里需要去查询当前物料的有效期）
                            //model.ExternalStatus = "2"; //(1:R 上锁 2:U 解锁)默认为3
                            //model.Type = "0";
                            //LotModels.Add(model);
                        }

                        #endregion

                        #region 子批次

                        //新增子批次（判断是否存在批次信息，无创建，有,无需操作）
                        List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == subLotCode);
                        if (subLotList != null && subLotList.Count > 0)
                        {
                            result.msg = "子批次:" + subLotCode + "已存在";
                            return result;
                        }
                        else
                        {
                            MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                            submodel.Create(_user.Name.ToString());
                            subLotID = submodel.ID;
                            submodel.SubLotId = subLotCode;
                            submodel.Comment = reamrk;
                            if (setLotStatus == "1")
                            {
                                submodel.ExternalStatus = "1";
                            }
                            else
                            {
                                submodel.ExternalStatus = "3";
                            }
                            submodel.Type = "0";
                            submodels.Add(submodel);
                        }

                        #endregion

                        #region 转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());
                        trans.OldLotId = "";
                        trans.OldSublotId = "";
                        trans.OldExpirationDate = null;
                        trans.NewExpirationDate = exTime;
                        trans.Quantity = Math.Round(Convert.ToDecimal(fullBagWeight), 3); // Convert.ToDecimal(fullBagWeight);
                        trans.QuantityUomId = unitID;
                        trans.Type = "Create Inventory";
                        trans.Comment = "原料加工厂创建库存";
                        trans.OldEquipmentId = "";
                        trans.NewEquipmentId = equipmentId;
                        trans.OldContainerId = "";
                        trans.NewContainerId = "";
                        trans.OldLotExternalStatus = "";
                        trans.OldSublotExternalStatus = "";
                        trans.NewLotExternalStatus = "2";
                        trans.NewSublotExternalStatus = "3";
                        trans.NewSublotId = subLotID;
                        trans.NewLotId = lotID;
                        trans.OldLotId = lotID;
                        trans.NewMaterialId = mID;
                        trans.OldMaterialId = mID;
                        listTrans.Add(trans);

                        #endregion

                        #region 库存

                        MaterialInventoryEntity inventModel = new MaterialInventoryEntity();
                        inventModel.Create(_user.Name.ToString());

                        inventModel.Quantity = Math.Round(Convert.ToDecimal(fullBagWeight), 3);//  Convert.ToDecimal(fullBagWeight);
                        inventModel.QuantityUomId = unitID;//"5";
                        inventModel.EquipmentId = equipmentId;
                        inventModel.ContainerId = "";
                        inventModel.ProductionRequestId = proID;
                        inventModel.BatchId = batchID;
                        inventModel.Createtime = Convert.ToDateTime(cTIME);
                        inventModel.LotId = lotID;
                        inventModel.SublotId = subLotID;
                        inventModel.StorageLocation = "";
                        inventModel.InventoryType = type;
                        inventModel.Bucketnum = tNumber;
                        //       inventModel.Remark = reamrk;
                        requests.Add(inventModel);

                        #endregion

                        #region 这里按照规定打印标签（打印数据源）

                        //加入数据源
                        object obj = new PPM.Model.ViewModels.MKM.PrintView.VerifiyDetailTotalData
                        {
                            ///WMS标签数据源
                            //Supplier_Name = "",
                            //Material_Name = mName,
                            //Material_Code = mCode,
                            //Lot_Code = lotCode,
                            //Material_Inventory_Qty = fullBagWeight,
                            //Unit_Code = uName,
                            //Sublot_Code = subLotCode,
                            //User = _user.Name.ToString()

                            Plant = "三厂",
                            Material_Name = mName,
                            Material_Code = mCode,
                            Batch_Code = lotCode,
                            NUM = fullBagWeight,
                            PO_NUM = "",// result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                            Unit = uName,
                            Receive_Date = DateTime.Now.ToString("yyyy-MM-dd"),
                            SSCC = subLotCode

                        };
                        objList.Add(obj);

                        PrintSendDataItems wmsPrinit = new PrintSendDataItems
                        {
                            itemcode = mCode,
                            itemunitcode = uName,
                            batch = lotCode,
                            printtype = "mesPrint",
                            qty = Convert.ToDecimal(fullBagWeight == string.Empty ? 0 : Convert.ToDecimal(fullBagWeight)),
                            type = 1,
                            invstatus = 2
                        };
                        wmsList.Add(wmsPrinit);
                        #endregion
                    }
                }

                _unitOfWork.BeginTran();

                bool r1 = true;
                bool r2 = true;
                bool r3 = true;
                bool r4 = true;
                bool r5 = true;


                //新增库存
                if (requests != null && requests.Count > 0)
                {
                    r1 = await _dal.Add(requests) > 0;
                }
                //新增批次
                //if (LotModels != null && LotModels.Count > 0)
                //{
                //    await _materialLotEntityDal.Add(LotModels);
                //}
                if (searchLotData != null && searchLotData.Count > 0)
                {
                    r2 = await _materialLotEntityDal.Add(searchLotData) > 0;
                }

                //更新批次数据
                if (UpLotModels != null && UpLotModels.Count > 0)
                {
                    r3 = await _materialLotEntityDal.Update(UpLotModels);
                }
                //新增子批次
                if (submodels != null && submodels.Count > 0)
                {
                    r4 = await _materialSubLotServicesDal.Add(submodels) > 0;
                }
                //写入历史记录
                if (listTrans != null && listTrans.Count > 0)
                {
                    r5 = await _MaterialTransferEntityDal.Add(listTrans) > 0;
                }

                if (r1 && r2 && r3 && r4 && r5)
                {
                    _unitOfWork.CommitTran();
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "添加库存失败";
                    return result;
                }


                #region WMS打印

                //var wmsResult = _requestInventoryViewServices.PrintLabelSynchro(wmsList);              
                //if (wmsResult.successed == false)
                //{
                //    result.msg = "标签打印失败，接口访问失败";
                //    result.success = false;
                //    return result;
                //}

                #endregion

                #region 打印(自身)

                #region 模板和数据源（暂时注释） 

                //string teampId = string.Empty;


                if (mModels.Count > 0)
                {
                    //获取对应数据的标签

                    if (isPrint == true)
                    {
                        string[] printIDS = requests.GroupBy(P => P.ID).Select(P => P.Key).ToArray();

                        for (int i = 0; i < printIDS.Length; i++)
                        {
                            string id = printIDS[i];//.ID;
                            var lableData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                            await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID, "02308281-5121-6559-163e-0370f6000000", lableData, lableData[0], 1, "PrintProcessPlantTemplete");
                        }
                    }

                }
                ////这里数据源需要和最外层循环一致
                //for (int i = 0; i < objList.Count; i++)
                //{
                //    //   // List<object> model = objList[i].ToList();
                //    //await PrintByCode(mModels[i].SelsectPrinter, mModels[i].tempID, mModels[i].teamClassID, objList, objList[i]);
                //}


                #endregion

                #endregion

                //提示成功
                result.success = true;
                return result;


            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "添加库存失败" + ex;
                return result;
            }

        }

        #region 扫码收货查询


        /// <summary>
        /// 获取工作中心
        /// </summary>
        /// <param name="mID"></param>
        /// <returns></returns>
        public async Task<string> GetWorkCentersByMCodeID(string mID)
        {
            //获取物料组分组
            DateTime starTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime endTime = starTime.AddDays(2).AddSeconds(-1);
            var poList = await _PoConsumeMaterialListViewEntity.FindList(p => p.PlanStartTime >= starTime && p.PlanStartTime <= endTime && p.MaterialId.Contains(mID));
            //根据物料分组
            var groupMatail = poList.GroupBy(p => new { p.MaterialId, p.MaterialCode, p.SegmentName }).Select(p => new { p.Key.MaterialId, p.Key.MaterialCode, p.Key.SegmentName }).ToList();

            string remark = string.Empty;
            if (groupMatail != null)
            {
                for (int j = 0; j < groupMatail.Count; j++)
                {
                    string name = groupMatail[j].SegmentName;
                    if (!string.IsNullOrEmpty(name))
                    {
                        if (j == groupMatail.Count - 1)
                        {
                            remark += name;
                        }
                        else
                        {
                            remark += name + "/";

                        }
                    }
                }
            }

            return remark;
        }

        public async Task<PageModel<TransferHistoryViewEntity>> GetScanDataList(InventorylistingViewRequestModel reqModel)
        {
            PageModel<TransferHistoryViewEntity> result = new PageModel<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;

            reqModel.pageSize = 500;
            if (reqModel.ScanName == null)
            {

            }
            else
            {
                var eData = await _equipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.ScanName);
                reqModel.eqpID = eData.ID;

            }
            //查询明后天的数据

            if (string.IsNullOrEmpty(reqModel.Comment))
            {
                var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
        //加入查询条件(时间)
        .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
        .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
        .AndIF(!string.IsNullOrEmpty(reqModel.NewMaterialCode), a => a.NewMaterialCode.Contains(reqModel.NewMaterialCode))
         .AndIF(!string.IsNullOrEmpty(reqModel.MCode), a => a.NewMaterialCode.Contains(reqModel.MCode))
         .AndIF(!string.IsNullOrEmpty(reqModel.key),
          a => a.NewMaterialCode.Contains(reqModel.key)
         || a.NewMaterialName.Contains(reqModel.key)
          || a.NewSubLotId.Contains(reqModel.key)
           || a.NewLotId.Contains(reqModel.key))
        .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.NewEquipmentId.Contains(reqModel.eqpID)).And(p => p.CComment.Contains("收料-转移") || p.CComment.Contains("创建库存-WMS")).ToExpression();

                if (reqModel.StartTime == null && reqModel.StartTime == null)
                {
                    DateTime now = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
                    DateTime dateStar = now;
                    DateTime dateEnd = now.AddDays(2).AddSeconds(-1);
                    whereExpression = Expressionable.Create<TransferHistoryViewEntity>().And(a => a.CreateDate >= Convert.ToDateTime(dateStar))
                    .And(a => a.CreateDate <= Convert.ToDateTime(dateEnd))
                     .AndIF(!string.IsNullOrEmpty(reqModel.NewMaterialCode), a => a.NewMaterialCode.Contains(reqModel.NewMaterialCode))
                      .AndIF(!string.IsNullOrEmpty(reqModel.key),
          a => a.NewMaterialCode.Contains(reqModel.key)
         || a.NewMaterialName.Contains(reqModel.key)
          || a.NewSubLotId.Contains(reqModel.key)
           || a.NewLotId.Contains(reqModel.key))
                    .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.NewEquipmentId.Contains(reqModel.eqpID)).And(p => p.CComment.Contains("收料-转移") || p.CComment.Contains("创建库存-WMS")).ToExpression();
                }

                //var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
                //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data;
                //return result;

                var data2 = await _dal.Db.Queryable<TransferHistoryViewEntity>().Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
                var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                result.dataCount = data2.Count;
                result.data = rDat;
                return result;
            }
            else
            {
                var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
            //加入查询条件(时间)
            .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
            .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
            .AndIF(!string.IsNullOrEmpty(reqModel.NewMaterialCode), a => a.NewMaterialCode.Contains(reqModel.NewMaterialCode))
             .AndIF(!string.IsNullOrEmpty(reqModel.key),
          a => a.NewMaterialCode.Contains(reqModel.key)
         || a.NewMaterialName.Contains(reqModel.key)
          || a.NewSubLotId.Contains(reqModel.key)
           || a.NewLotId.Contains(reqModel.key))
            .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.NewEquipmentId.Contains(reqModel.eqpID)).And(p => p.CComment.Contains(reqModel.Comment)).ToExpression();

                if (reqModel.StartTime == null && reqModel.StartTime == null)
                {
                    DateTime dateStar = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
                    DateTime dateEnd = dateStar.AddDays(2).AddSeconds(-1);
                    whereExpression = Expressionable.Create<TransferHistoryViewEntity>().And(a => a.CreateDate >= Convert.ToDateTime(dateStar))
                    .And(a => a.CreateDate <= Convert.ToDateTime(dateEnd))
                     .AndIF(!string.IsNullOrEmpty(reqModel.NewMaterialCode), a => a.NewMaterialCode.Contains(reqModel.NewMaterialCode))
                    .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.NewEquipmentId.Contains(reqModel.eqpID)).And(p => p.CComment.Contains(reqModel.Comment)).ToExpression();
                }

                //var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
                //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                //result.dataCount = dataCount;
                //result.data = data;
                //return result;

                var data2 = await _dal.Db.Queryable<TransferHistoryViewEntity>().Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();

                int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引
                                                                               // 
                if (reqModel.Comment.Contains("收料"))
                {

                    var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                    result.dataCount = data2.Count;
                    result.data = rDat;
                    return result;
                }
                else
                {
                    var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
                    result.dataCount = data2.Count;
                    result.data = rDat;
                    return result;
                }
            }

        }

        #endregion

        #region  白糖预处理


        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetSugarPreCount(InventorylistingViewRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc == reqModel.Sscc)
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                //Bin
                .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.EquipmentId.Contains(reqModel.eqpID))
                  .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.LocationF.Contains(reqModel.key) || a.LocationS.Contains(reqModel.key)
                  || a.StatusS.Contains(reqModel.key) || a.StatusF.Contains(reqModel.key) || a.Sscc.Contains(reqModel.key) || a.MaterialCode.Contains(reqModel.key) || a.MaterialName.Contains(reqModel.key))
                 //这里判断对应的物料类型
                 //.AndIF(!string.IsNullOrEmpty(reqModel.materialType), a => a.ClassDec.Contains(reqModel.materialType))
                 //如果WMS就是已经收货的料
                 .AndIF(!string.IsNullOrEmpty(reqModel.SugarType), a => a.Remark.Contains(reqModel.SugarType))
                             .ToExpression();



            var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate).ToListAsync();


            if (data != null && data.Count > 0)
            {
                result.success = true;
                result.msg = data.Sum(p => p.Quantity).ToString();

            }
            else
            {
                result.success = true;
                result.msg = "0";
            }

            return result;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<InventorylistingViewEntity>> GetSugarPre(InventorylistingViewRequestModel reqModel)
        {
            PageModel<InventorylistingViewEntity> result = new PageModel<InventorylistingViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原料
                .AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                                                   || a.MaterialName.Contains(reqModel.Material))
                //批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                //SSCC
                .AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc == reqModel.Sscc)
                //批次状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                //sscc状态
                .AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                //Bin
                .AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                 //Container     
                 .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                 .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                 .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.EquipmentId.Contains(reqModel.eqpID))
                  .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.LocationF.Contains(reqModel.key) || a.LocationS.Contains(reqModel.key)
                  || a.StatusS.Contains(reqModel.key) || a.StatusF.Contains(reqModel.key) || a.ClassDec.Contains(reqModel.key) || a.Sscc.Contains(reqModel.key) || a.MaterialCode.Contains(reqModel.key) || a.MaterialName.Contains(reqModel.key))
                 //这里判断对应的物料类型
                 //.AndIF(!string.IsNullOrEmpty(reqModel.materialType), a => a.ClassDec.Contains(reqModel.materialType))
                 //如果WMS就是已经收货的料
                 .AndIF(!string.IsNullOrEmpty(reqModel.SugarType), a => a.Remark.Contains(reqModel.SugarType))
                             .ToExpression();
            //var data = await _dal.Db.Queryable<InventorylistingViewEntity>()
            //    .Where(whereExpression).OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            //result.dataCount = dataCount;
            //result.data = data;



            var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>()
 .Where(whereExpression).
 Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").OrderByDescending(p => p.CreateDate).ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;


        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<InventorylistingViewEntityModel>> GetSugarPreAll(InventorylistingViewRequestModel reqModel)
        {
            PageModel<InventorylistingViewEntityModel> result = new PageModel<InventorylistingViewEntityModel>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<InventorylistingViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                  //原料
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Material), a => a.MaterialCode.Contains(reqModel.Material)
                  //                                   || a.MaterialName.Contains(reqModel.Material))
                  ////批次
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Batch), a => a.BatchId.Contains(reqModel.Batch))
                  ////SSCC
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Sscc), a => a.Sscc == reqModel.Sscc)
                  ////批次状态
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Statusf), a => a.StatusF == reqModel.Statusf)
                  ////sscc状态
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Statuss), a => a.StatusS == reqModel.Statuss)
                  ////新批子批次
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Location), a => a.LocationF.Contains(reqModel.Location))
                  ////Bin
                  //.AndIF(!string.IsNullOrEmpty(reqModel.Bin), a => a.LocationS.Contains(reqModel.Bin))
                  // //Container     
                  // .AndIF(!string.IsNullOrEmpty(reqModel.Container), a => a.ContainerName.Contains(reqModel.Container))
                  // .AndIF(!string.IsNullOrEmpty(reqModel.ProcessOrder), a => a.ProductionOrderNo.Contains(reqModel.ProcessOrder))
                  // .AndIF(!string.IsNullOrEmpty(reqModel.ContainerID), a => a.ContainerId.Contains(reqModel.ContainerID))
                  .AndIF(!string.IsNullOrEmpty(reqModel.eqpID), a => a.EquipmentId.Contains(reqModel.eqpID))
                  .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.LocationF.Contains(reqModel.key) || a.LocationS.Contains(reqModel.key)
                  || a.StatusS.Contains(reqModel.key) || a.StatusF.Contains(reqModel.key)
                  || a.ClassDec.Contains(reqModel.key) || a.Sscc.Contains(reqModel.key)
                  || a.MaterialCode.Contains(reqModel.key) || a.MaterialName.Contains(reqModel.key)
                  || a.BatchId.Contains(reqModel.key) || a.Sapformula.Contains(reqModel.key))
                 //这里判断对应的物料类型
                 //.AndIF(!string.IsNullOrEmpty(reqModel.materialType), a => a.ClassDec.Contains(reqModel.materialType))
                 //如果WMS就是已经收货的料
                 .AndIF(!string.IsNullOrEmpty(reqModel.SugarType), a => a.Remark.Contains(reqModel.SugarType))
                             .ToExpression();
            var data2 = await _dal.Db.Queryable<InventorylistingViewEntity>().Where(whereExpression).Where(p => p.LocationS != "FG09" && p.LocationS != "RS01" && p.LocationS != "RSBK" && p.LocationS != "WP04").ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderBy(p => p.ExpirationDate).ThenByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();


            //result.dataCount = data2.Count;
            //result.data = rDat;
            //return result;

            InventorylistingViewEntityModel historyViewEntityModel = new InventorylistingViewEntityModel();
            historyViewEntityModel.historyViewEntities = rDat;
            if (data2 != null)
            {
                historyViewEntityModel.Total = data2.Sum(p => p.Quantity).Value;
            }
            else
            {
                historyViewEntityModel.Total = 0;
            }
            List<InventorylistingViewEntityModel> list = new List<InventorylistingViewEntityModel>();
            list.Add(historyViewEntityModel);
            result.dataCount = data2.Count;
            result.data = list;
            return result;
        }

        #region  合并并打印条码
        ///// <summary>
        /////库存合并并打印条码
        ///// </summary>
        ///// <param name="sscsArray">删除的子批次（这里如果和列表重复返回时候需要给删掉）</param>
        ///// <param name="sscc">输入框的批次</param>
        ///// <param name="quantity">合并总量</param>
        ///// <param name="isPrint">是否打印</param>
        ///// <param name="selectPrint">打印地址</param>
        ///// <returns></returns>
        /// <summary>
        /// 合并并打印条码
        /// </summary>
        /// <param name="models">models</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPreMerges([FromBody] InventoryMergesModel reqModel)//(string[] sscsArray, string sscc, string quantity, string isPrint, string selectPrint)
        {
            var result = new MessageModel<string>();
            result.success = false;

            string sscc = reqModel.sscc;
            decimal quantity = Math.Round(Convert.ToDecimal(reqModel.quantity), 3); //reqModel.quantity;
            bool isPrint = reqModel.isPrint;
            string selectPrint = reqModel.selectPrint;
            string[] idArray = reqModel.sscsArray;//库存id
            string upID = sscc;
            //var data = new MessageModel<string>();
            List<string> list = new List<string>(idArray);
            if (string.IsNullOrEmpty(sscc))
            {
                result.msg = "请输入子批次信息";
                return result;
            }

            if (list == null || list.Count <= 1)
            {
                result.msg = "无法合并单条数据";
                return result;
            }

            //判断当前选中数据是否为同批次
            var dataList = await _dal.Db.Queryable<MaterialInventoryEntity>().In(P => P.ID, list).ToListAsync();
            if (dataList.GroupBy(p => p.LotId).Count() != 1)
            {
                result.msg = "非同批次数据无法合并";
                return result;
            }


            #region 查询子批次
            var subModel = await _materialSubLotServicesDal.FindEntity(p => p.SubLotId == sscc);
            if (subModel == null)
            {
                result.msg = "合并到的子批次不存在";
                return result;
            }
            //这里需要判断是否存在子批次信息如果存在需要返回提示信息
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.SlotId == subModel.ID)
                .ToExpression();
            var model = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);


            if (model == null || model.ID == string.Empty)
            {
                result.msg = "不存在该子批次信息请重新扫描";
                return result;
            }

            #region 判断当前批次和子批次数据(有效期)

            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            List<string> inventIDs = new List<string>();

            for (int i = 0; i < idArray.Length; i++)
            {
                inventIDs.Add(idArray[i]);
            }
            //     inventIDs.Add(model.ID);

            string msg = await _inventorylistingViewServices.GetStateByInventID(inventIDs.ToArray());

            if (!string.IsNullOrEmpty(msg))
            {
                result.msg = msg;
                return result;
            }

            msg = await _inventorylistingViewServices.GetProductionByInventID(inventIDs.ToArray());
            if (!string.IsNullOrEmpty(msg))
            {
                result.msg = msg;
                return result;
            }

            #endregion


            if (model != null && model.ID != string.Empty)
            {
                //不存在移除相同元素,重置需要删除的数据
                list.Remove(model.ID);
                idArray = list.ToArray();
            }
            else
            {
                result.msg = "不存在该子批次信息请重新扫描";
                return result;
            }
            #endregion





            if (isPrint == true)
            {
                //执行打印
                //selectPrint 打印机
            }
            #region 业务处理

            try
            {
                _unitOfWork.BeginTran();
                #region 更新库存

                //获取需要更新的库存信息
                MaterialInventoryEntity upmodel = new MaterialInventoryEntity();
                upmodel = await _dal.QueryById(model.ID);
                upmodel.Modify(upmodel.ID, _user.Name.ToString());
                //upmodel.SublotId = sscc;
                upmodel.Quantity = Math.Round(Convert.ToDecimal(quantity), 3); //Convert.ToDecimal(quantity);
                bool updateResult = await _dal.Update(upmodel);

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans1 = new MaterialTransferEntity();
                trans1.Create(_user.Name.ToString());
                //trans.ID = Guid.NewGuid().ToString();
                trans1.OldStorageLocation = model.LocationF;
                trans1.NewStorageLocation = model.LocationF;
                trans1.OldLotId = model.LotId;
                trans1.NewLotId = model.LotId;
                trans1.OldSublotId = model.SlotId;
                trans1.NewSublotId = model.SlotId;
                trans1.OldExpirationDate = model.ExpirationDate;
                trans1.NewExpirationDate = model.ExpirationDate;
                trans1.Quantity = Math.Round(Convert.ToDecimal(quantity), 3);  // Convert.ToDecimal(quantity); 
                trans1.QuantityUomId = model.QuantityUomId;
                trans1.Type = "Batch Merge";
                trans1.Comment = "库存合并";
                trans1.NewEquipmentRequirementId = model.EquipmentRequirementId;
                trans1.OldEquipmentRequirementId = model.EquipmentRequirementId;
                trans1.OldEquipmentId = model.EquipmentId;
                trans1.NewEquipmentId = model.EquipmentId;
                trans1.OldContainerId = model.ContainerId;
                trans1.NewContainerId = model.ContainerId;
                trans1.OldMaterialId = model.MaterialId;
                trans1.NewMaterialId = model.MaterialId;
                trans1.OldLotExternalStatus = model.StatusF;
                trans1.OldSublotExternalStatus = model.StatusS;
                trans1.NewLotExternalStatus = model.StatusF;
                trans1.NewSublotExternalStatus = model.StatusS;
                trans1.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                trans1.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                bool tranHis = await _MaterialTransferEntityDal.Add(trans1) > 0;

                #endregion

                #endregion

                #region 删除多余的物料库存信息

                for (int i = 0; i < idArray.Length; i++)
                {
                    string id = idArray[i];
                    if (id != string.Empty)
                    {
                        string deleteSScc = idArray[i];
                        var modelDelete = await _InventorylistingViewEntityDal.QueryById(deleteSScc);
                        string deleteID = modelDelete.ID;
                        // var modelDelete = await _inventorylistingViewServices.f(id);

                        #region 写入转移历史

                        ////写入历史记录
                        //MaterialTransferEntity trans = new MaterialTransferEntity();
                        //trans.Create(_user.Name.ToString());
                        ////trans.ID = Guid.NewGuid().ToString();
                        //trans.OldStorageLocation = modelDelete.LocationF;
                        //trans.NewStorageLocation = modelDelete.LocationF;
                        //trans.OldLotId = modelDelete.LotId;
                        //trans.NewLotId = modelDelete.LotId;
                        //trans.OldSublotId = modelDelete.SlotId;
                        //trans.NewSublotId = modelDelete.SlotId;
                        //trans.OldExpirationDate = modelDelete.ExpirationDate;
                        //trans.NewExpirationDate = modelDelete.ExpirationDate;
                        //trans.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3);  // Convert.ToInt32(model.Quantity);
                        //trans.QuantityUomId = modelDelete.QuantityUomId;
                        //// trans.ProductionExecutionId = model.ProductionRequestId;
                        //trans.Type = "Batch Merge";
                        //trans.Comment = "库存合并";
                        //trans.NewEquipmentRequirementId = modelDelete.EquipmentRequirementId;
                        //trans.OldEquipmentRequirementId = modelDelete.EquipmentRequirementId;
                        ////trans.TransferGroupId
                        //trans.OldEquipmentId = modelDelete.EquipmentId;
                        //trans.NewEquipmentId = modelDelete.EquipmentId;
                        //trans.OldContainerId = modelDelete.ContainerId;
                        //trans.NewContainerId = modelDelete.ContainerId;
                        ////status
                        //trans.OldMaterialId = modelDelete.MaterialId;
                        //trans.NewMaterialId = modelDelete.MaterialId;
                        //trans.OldLotExternalStatus = modelDelete.StatusF;
                        //trans.OldSublotExternalStatus = modelDelete.StatusS;
                        //trans.NewLotExternalStatus = modelDelete.StatusF;
                        //trans.NewSublotExternalStatus = modelDelete.StatusS;
                        //trans1.PhysicalQuantity = modelDelete.MaxVolume == null ? "" : modelDelete.MaxVolume.ToString(); //物理数量
                        //trans1.TareQuantity = modelDelete.TareWeight == null ? 0 : modelDelete.TareWeight.Value;  //皮数量
                        //bool tranHisDelele = await _MaterialTransferEntityDal.Add(trans) > 0;
                        #endregion

                        #region 删除数据                     

                        await _dal.DeleteById(deleteID);

                        #endregion
                    }
                }

                #endregion

                _unitOfWork.CommitTran();
                if (isPrint == true)
                {
                    //执行打印
                    //selectPrint 打印机
                }

                result.success = true;
                return result;//合并成功
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                result.msg = "合并失败";
                return result;
            }

            #endregion

        }

        #endregion

        #region 转移
        /// <summary>
        /// 白糖预处理/转移
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> TransferEquipment(TransferEquipment reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            bool isDelete = false;
            string inventID = string.Empty;
            try
            {
                _unitOfWork.BeginTran();

                List<MaterialInventoryEntity> listMaterialInventory = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();
                List<ContainerEntity> listCon = new List<ContainerEntity>();
                List<ContainerHistoryEntity> listContainerHis = new List<ContainerHistoryEntity>();
                if (reqModel.sscc == string.Empty)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "转移失败,请输入追溯码";
                    return result;
                }
                if (reqModel.sscc != string.Empty)
                {
                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.Sscc == reqModel.sscc)
                     .ToExpression();
                    var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "转移失败,未找到库存信息";
                        return result;

                    }

                    #region 判断当前批次和子批次数据(有效期)

                    //查询批次状态是否为锁定
                    //查询子批次状态是否为锁定
                    List<string> inventIDs = new List<string>();
                    inventIDs.Add(inventoryModel.ID);
                    string msg = await _inventorylistingViewServices.GetStateByInventID(inventIDs.ToArray());

                    if (!string.IsNullOrEmpty(msg))
                    {
                        result.msg = msg;
                        return result;
                    }

                    #endregion


                    msg = await _inventorylistingViewServices.GetProductionByInventID(inventIDs.ToArray());
                    if (!string.IsNullOrEmpty(msg))
                    {
                        result.msg = msg;
                    }

                    //更新库房容器信息
                    MaterialInventoryEntity models = _MaterialInventoryEntityDal.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                    if (models == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "转移失败,未找到库存信息";
                        return result;
                    }
                    if (models != null)
                    {
                        inventID = models.ID;
                        models.EquipmentId = reqModel.equipmentId;
                        listMaterialInventory.Add(models);
                    }
                    string userID = _user.Name.ToString();


                    #region 这里执行判断数据并控制删除或新增

                    string identicalmsg = await EquipmentIsIdentical(inventoryModel.EquipmentId, reqModel.equipmentId);
                    if (identicalmsg != string.Empty)
                    {
                        string[] split = identicalmsg.Split(';');
                        if (split[0].Trim() == "NO" && split[0].Trim() == "0")
                        {
                            isDelete = true;
                        }
                    }

                    #endregion               

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.Create(userID);
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = reqModel.name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3); // Convert.ToInt32(inventoryModel.Quantity);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                    trans.Type = "Transfer Inventory";
                    trans.Comment = "转移";
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = reqModel.equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;

                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                    //if (isDelete == true)
                    //{
                    //    trans.Type = "Delete";
                    //    trans.Comment = "删除-转移";
                    //}

                    listTrans.Add(trans);

                    #endregion

                    #region 修改容器位置

                    if (!string.IsNullOrEmpty(inventoryModel.ContainerId))
                    {
                        //查询之前的位置
                        var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                        var containerModel = await _ContainerEntity.FindEntity(whereLocation);
                        //更新容器新的位置
                        ContainerEntity model = await _ContainerEntity.QueryById(inventoryModel.ContainerId);
                        model.Modify(inventoryModel.ContainerId, _user.Name.ToString());
                        model.ID = inventoryModel.ContainerId;
                        model.EquipmentId = reqModel.equipmentId;
                        listCon.Add(model);

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);
                        hisModel.ContainerId = inventoryModel.ContainerId;
                        hisModel.Type = "";
                        hisModel.EquipmentId = reqModel.equipmentId;
                        hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        hisModel.State = containerModel.Status;
                        hisModel.Comment = containerModel.Comment;
                        hisModel.MaterialId = inventoryModel.MaterialId;
                        hisModel.SublotId = inventoryModel.SubLotId;
                        hisModel.Quantity = inventoryModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                        hisModel.LotId = inventoryModel.LotId;
                        hisModel.ExpirationDate = inventoryModel.ExpirationDate;

                        listContainerHis.Add(hisModel);
                    }
                    #endregion

                }

                if (isDelete == true)
                {
                    bool addResult = await _MaterialTransferEntityDal.Add(listTrans) > 0;//新增转移历史
                    bool deleteResult = await _MaterialInventoryEntityDal.DeleteById(inventID);//删除数据

                    if (!addResult || !deleteResult)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "转移失败";
                        return result;
                    }
                }
                else
                {
                    //插入
                    bool tranHis = await _MaterialTransferEntityDal.Add(listTrans) > 0;//新增转移历史
                    bool upResult = await _MaterialInventoryEntityDal.Update(listMaterialInventory);//修改库存
                    bool hisUp = true;
                    if (listCon.Count > 0)
                    {
                        hisUp = await _ContainerEntity.Update(listCon);//更新容器
                    }
                    bool rqResult = true;
                    if (listContainerHis.Count > 0)
                    {
                        rqResult = await _ContainerHistoryEntityDal.Add(listContainerHis) > 0;//更新容器历史
                    }

                    if (!upResult || !tranHis || !rqResult || !hisUp)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "转移失败";
                        return result;
                    }
                }

                _unitOfWork.CommitTran();
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                result.msg = "转移失败";
                return result;
            }
            result.success = true;
            return result;
        }

        #endregion

        #region 扫码收货

        /// <summary>
        /// 扫码收货（收货按钮事件）需要更新请料记录
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SugarPrePut(SugarPrePutModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                //根据设备名称拿equpmentID
                var eData = await _equipmentEntity.FindEntity(p => p.EquipmentCode == reqModel.name || p.EquipmentName == reqModel.name);
                if (eData == null)
                {
                    result.msg = "没有找到该节点（EquipmentCode或EquipmentName）";
                    return result;
                }
                reqModel.equipmentId = eData.ID;
                //首先判断当前子批次和EqupmentID是否存在
                if (reqModel.sscc == null || reqModel.sscc == "")
                {
                    result.msg = "追溯码为空，请重新扫描";
                    return result;
                }
                reqModel.sscc = reqModel.sscc.Trim();
                if (reqModel.equipmentId == null || reqModel.equipmentId == "")
                {
                    result.msg = "存储地点为空";
                    return result;
                }

                #region 查询属性

                bool isConvert = false;
                string kgUnitID = string.Empty;

                //查询属性表
                MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + reqModel.equipmentId, _user.GetToken(), new { });
                var equipmentAllData = apiResult_equipmentAllData.response;
                if (equipmentAllData != null)
                {
                    var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                    if (item != null)
                    {
                        isConvert = true;
                        var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                        if (umanagerModel != null)
                        {
                            DFM.Model.Models.UnitmanageEntity modelUnit = umanagerModel.FirstOrDefault();
                            kgUnitID = modelUnit.ID;
                        }
                    }
                }

                #endregion

                //List<InventorylistingViewEntity> lotsscc = await _InventorylistingViewEntityDal.FindList(p => p.Sscc == reqModel.sscc);
                //if (lotsscc == null || lotsscc.Count <= 0)
                //{
                //    result.msg = "当前库存不存在";
                //    return result;
                //}
                //else
                //{
                //    var qty = lotsscc[0].Quantity;
                //    if (qty == null || qty <= 0)
                //    {
                //        result.msg = "当前库存数为零不存在";
                //        return result;
                //    }
                //}

                List<MaterialSubLotEntity> lotsscc = await _materialSubLotServicesDal.FindList(p => p.SubLotId == reqModel.sscc);
                if (lotsscc != null && lotsscc.Count > 0)
                {
                    result.msg = "子批次已存在，请重新扫描";
                    return result;
                }

                //默认测试接口对应条码
                string wmsCode = "";
                if (wmsCode.Length > reqModel.sscc.Length)
                {
                    reqModel.sscc = wmsCode;
                }
                //调用接口
                ScanSend model = new ScanSend();
                model.itembarcode = reqModel.sscc;
                var sResult = GetSSCCInfo(model).Result;
                if (sResult.successed == false)
                {
                    SerilogServer.LogDebug($"获取接口失败，失败原因:{sResult.msg}");
                    result.msg = "调用获取条码信息接口失败";
                    return result;
                }
                ScanRootResult models = sResult.Response;
                //成功获取到接口信息
                string suppeirName = string.Empty;
                if (models.flag == true)
                {
                    List<MaterialInventoryEntity> addInvent = new List<MaterialInventoryEntity>();
                    List<MaterialLotEntity> addLot = new List<MaterialLotEntity>();
                    List<MaterialLotEntity> upLot = new List<MaterialLotEntity>();
                    List<MaterialSubLotEntity> addSubLot = new List<MaterialSubLotEntity>();
                    List<MaterialTransferEntity> addTran = new List<MaterialTransferEntity>();
                    List<UnitmanageEntity> addUnit = new List<UnitmanageEntity>();

                    List<RequestDetailEntity> upDetail = new List<RequestDetailEntity>();

                    for (int i = 0; i < models.data.Count; i++)
                    {
                        string supplierCode = models.data[i].suppliercode.ToString();
                        string supplierName = models.data[i].suppliername.ToString();
                        string qty = models.data[i].qty.ToString() == "0" ? models.data[i].bcqty.ToString() : models.data[i].qty;//取QTY为0取bcqty WMS要求2241217
                        string unit = models.data[i].itemunitcode;
                        string mcode = models.data[i].itemcode;
                        string unitID = string.Empty;
                        string subLotID = string.Empty;
                        string lotId = models.data[i].batch;



                        //供应商
                        suppeirName = supplierName;
                        string reciveDate = string.Empty;
                        string expirationDate = string.Empty;
                        try
                        {
                            //日期格式可能非标准模式需要单独考虑处理 17.03.2018
                            reciveDate = models.data[i].recivedate.ToCharArray().Aggregate(new StringBuilder(), (sb, c) => sb.Append(c == 'T' ? ' ' : c)).ToString().Trim();
                            expirationDate = models.data[i].validity_date.ToCharArray().Aggregate(new StringBuilder(), (sb, c) => sb.Append(c == 'T' ? ' ' : c)).ToString().Trim();
                        }
                        catch (Exception ex)
                        {
                            result.msg = "接口返回时间转换失败" + models.data[i].recivedate + "," + models.data[i].validity_date + ex.Message;
                            return result;
                        }


                        //DateTime.ParseExact("27/03/2005 10:46:02 AM", "dd/MM/yyyy HH:mm:ss tt", System.Globalization.CultureInfo.InvariantCulture)

                        DateTime datereciveDate = DateTime.ParseExact(reciveDate, "dd.MM.yyyy", CultureInfo.InvariantCulture);
                        reciveDate = datereciveDate.ToString("yyyy-MM-dd") + " 00:00:00";

                        DateTime dateexpirationDate = DateTime.ParseExact(expirationDate, "dd.MM.yyyy", CultureInfo.InvariantCulture);
                        expirationDate = dateexpirationDate.ToString("yyyy-MM-dd") + " 23:59:59";


                        string reviceMsg = "supplierCode:" + supplierCode + " supplierName:" + supplierName
                                               + " qty:" + qty + " unit:" + unit + " mcode:" + mcode + " lotId:" + lotId + " reciveDate:" + reciveDate + " expirationDate:" + expirationDate
                                               + " SSCC:" + reqModel.sscc;
                        SerilogServer.LogDebug($"WMS收货，收货内容:{reviceMsg}", "收货记录LOG ");

                        #region 创建库存

                        MaterialInventoryEntity request = new MaterialInventoryEntity();
                        request.Quantity = Math.Round(Convert.ToDecimal(qty), 3);
                        //创建数据
                        request.Create(_user.Name.ToString());

                        #region 处理单位问题

                        //判断当前单位是否存在
                        List<UnitmanageEntity> uList = await _UnitEntityDal.FindList(p => p.Name == unit);
                        if (uList != null && uList.Count > 0)
                        {
                            unitID = uList[0].ID;
                        }
                        else
                        {
                            UnitmanageEntity uModel = new UnitmanageEntity();
                            uModel.Create(_user.Name.ToString());
                            uModel.Name = unit;
                            uModel.Shortname = unit;
                            uModel.Enable = 1;
                            uModel.Deleted = 0;
                            uModel.Description = "扫码收货创建";
                            uModel.Deleted = 0;
                            unitID = uModel.ID;
                            addUnit.Add(uModel);
                        }

                        #endregion
                        //默认是kg
                        request.QuantityUomId = unitID;

                        #region 判断当前物料是否需要单位转换并改变数量

                        if (isConvert == true)
                        {
                            string unitF = unit;

                            if (unitF.Trim() != "KG")
                            {
                                //这里进行查询并转换
                                var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == mcode && P.FormUnitName == unitF && P.ToUnitName == "KG");
                                if (uConventList != null)
                                {
                                    var uConvertModel = uConventList.FirstOrDefault();
                                    //标准值
                                    decimal fValue = uConvertModel.ConvertFormQty.Value;
                                    //目标单位
                                    decimal toValue = uConvertModel.ConvertToQty.Value;
                                    //这里处理数量
                                    decimal inventQty = Math.Round(Convert.ToDecimal(qty), 3) / fValue * toValue;

                                    request.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3); // inventQty;//更改数量
                                    request.QuantityUomId = kgUnitID;//更改单位
                                }

                            }
                        }

                        #endregion

                        request.EquipmentId = reqModel.equipmentId;
                        request.ContainerId = "";
                        request.StorageLocation = "";
                        request.Remark = "WMS";
                        #endregion

                        #region 创建子批次

                        MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                        submodel.Create(_user.Name.ToString());
                        subLotID = submodel.ID;
                        submodel.SubLotId = reqModel.sscc;

                        submodel.ExternalStatus = "3";
                        submodel.Type = "0";
                        //给库存添加子批次ID
                        request.SublotId = subLotID;
                        addSubLot.Add(submodel);
                        #endregion

                        #region 创建批次                  
                        string mID = string.Empty;


                        List<Model.Models.MaterialEntity> mList = await _materialEntitydal.FindList(x => x.Code == mcode);
                        if (mList == null || mList.Count <= 0)
                        {
                            result.msg = "物料信息不存在:" + mcode;
                            return result;
                        }
                        //新增批次（判断是否存在批次信息，无创建，有无需操作）
                        List<MaterialLotEntity> lotList = await _materialLotEntityDal.FindList(x => x.LotId == lotId && x.MaterialId == mList[0].ID);
                        if (lotList != null && lotList.Count > 0)
                        {
                            lotId = lotList[0].ID;
                            mID = lotList[0].MaterialId;
                            MaterialLotEntity upModel = new MaterialLotEntity();
                            upModel = lotList[0];
                            upModel.Modify(upModel.ID, _user.Name.ToString());
                            upModel.CreateDate = Convert.ToDateTime(reciveDate);
                            //upModel.ExpirationDate = Convert.ToDateTime(expirationDate); 这里不用动
                            //upModel.Suppiercode = supplierCode;
                            //upModel.Suppiername = supplierName;
                            upLot.Add(upModel);
                            request.LotId = lotList[0].ID;
                        }
                        else
                        {
                            //保存批次
                            MaterialLotEntity lModel = new MaterialLotEntity();
                            lModel.Create(_user.Name.ToString());
                            lModel.Suppiercode = supplierCode;
                            lModel.Suppiername = supplierName;
                            lModel.MaterialId = mList[0].ID;
                            lotId = lModel.ID;
                            lModel.LotId = lotId;
                            //lModel.LotId = lotId;

                            #region 处理物料信息


                            if (mList != null && mList.Count > 0)
                            {
                                lModel.MaterialId = mList[0].ID;
                                mID = mList[0].ID;
                            }
                            else
                            {
                                result.msg = "物料信息不存在:" + mcode;
                                return result;
                            }

                            #endregion

                            lModel.ExpirationDate = Convert.ToDateTime(expirationDate);//待定（这里需要去查询当前物料的有效期）
                            lModel.ExternalStatus = "2"; //(1:R 上锁 2:U 解锁)
                            lModel.Type = "0";
                            lModel.LotId = models.data[i].batch;
                            request.LotId = lModel.ID;
                            addLot.Add(lModel);
                        }



                        #endregion

                        #region 更新请料记录


                        try
                        {
                            string materialID = mList[0].ID;

                            //拿当前时间
                            DateTime date = DateTime.Now;
                            DateTime startOfDay = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0).AddDays(-3);
                            //拿当前请料数据
                            InventoryRequestEntity getMaterial = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == materialID);

                            //存在请料才进行更新
                            if (getMaterial != null)
                            {
                                string inventoryRequestId = getMaterial.ID;

                                //获取请料明细（未完成的）
                                List<RequestDetailEntity> requestd = await _RequestDetailEntity.FindList(p => p.InventoryRequestId == inventoryRequestId && p.CreateDate >= startOfDay);
                                if (requestd != null && requestd.Count > 0)
                                {
                                    //获取当前所有的数据，降序排序
                                    List<RequestDetailEntity> list = requestd.Where(p => p.Status != "已完成").OrderBy(p => p.CreateDate).ToList();
                                    //收货数量
                                    var number = Math.Round(Convert.ToDecimal(qty), 3);
                                    if (list != null && list.Count > 0)
                                    {
                                        decimal yscha = 0;
                                        for (int j = 0; j < list.Count(); j++)
                                        {
                                            if (number <= 0)
                                            {
                                                break;
                                            }

                                            RequestDetailEntity reModel = new RequestDetailEntity();
                                            reModel = list[j];
                                            //获取需要收货的数量（获取每行需求数量）
                                            var ys = list[j].Quantity - list[j].ActualQuantity;
                                            //差值
                                            yscha = number - ys;

                                            //表示还有剩余数量
                                            if (yscha >= 0)
                                            {
                                                //收货数量加上差值
                                                list[j].ActualQuantity += ys;
                                                list[j].Status = "已完成";

                                                SerilogServer.LogDebug($"WMS收货，收货数量完成{list[j].ActualQuantity}", "更新请料记录LOG ");
                                            }
                                            else
                                            {
                                                //收货数量加上差值
                                                list[j].ActualQuantity += number;

                                                SerilogServer.LogDebug($"WMS收货，收货数量增加:{list[j].ActualQuantity}", "更新请料记录LOG ");
                                            }

                                            number = yscha;
                                            reModel.Modify(reModel.ID, _user.UserName);
                                            upDetail.Add(reModel);
                                        }
                                    }
                                    else
                                    {
                                        RequestDetailEntity reModel = requestd.Where(p => p.Status == "已完成").OrderByDescending(p => p.CreateDate).FirstOrDefault();
                                        if (reModel != null)
                                        {
                                            reModel.ActualQuantity += Math.Round(Convert.ToDecimal(qty), 3);
                                            reModel.Modify(reModel.ID, _user.UserName);
                                            reModel.Status = "已完成";
                                            upDetail.Add(reModel);
                                        }
                                    }
                                }
                            }
                        }
                        catch
                        {


                        }


                        #endregion


                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());

                        trans.OldSublotId = "";
                        trans.OldExpirationDate = null;
                        trans.NewExpirationDate = Convert.ToDateTime(expirationDate);
                        trans.Quantity = Math.Round(Convert.ToDecimal(qty), 3);
                        trans.QuantityUomId = unitID;

                        trans.Type = "Create Inventory";
                        trans.Comment = "创建库存-WMS";
                        if (!string.IsNullOrEmpty(reqModel.comment))
                        {
                            trans.Comment = reqModel.comment;
                        }
                        trans.OldEquipmentId = "";
                        trans.NewEquipmentId = reqModel.equipmentId;
                        trans.OldContainerId = "";
                        trans.NewContainerId = "";
                        trans.OldLotExternalStatus = "";
                        trans.OldSublotExternalStatus = "";
                        trans.NewMaterialId = mID;
                        trans.OldMaterialId = mID;
                        trans.NewLotExternalStatus = "2";
                        trans.NewSublotExternalStatus = "3";
                        trans.NewSublotId = subLotID;
                        trans.NewLotId = lotId;
                        trans.OldLotId = lotId;
                        trans.WmsPrintno = reqModel.sscc;

                        try
                        {
                            trans.Workcenter = await GetWorkCentersByMCodeID(mID);
                        }
                        catch
                        {

                        }


                        addTran.Add(trans);
                        #endregion

                        if (request.Quantity > 0)
                        {
                            addInvent.Add(request);
                        }
                    }

                    int sumData = addInvent.Where(p => p.Quantity <= 0).Count();
                    if (sumData > 0)
                    {
                        result.msg = "WMS返回数据为零";
                        return result;
                    }

                    _unitOfWork.BeginTran();

                    bool resultSave = true;
                    if (addInvent.Count > 0)
                    {
                        resultSave = await _dal.Add(addInvent) > 0;
                    }
                    bool resultSave1 = true;
                    if (addLot.Count > 0)
                    {
                        resultSave1 = await _materialLotEntityDal.Add(addLot) > 0;
                    }
                    bool resultSave2 = true;
                    if (upLot.Count > 0)
                    {
                        resultSave2 = await _materialLotEntityDal.Update(upLot);
                    }
                    bool resultSave3 = true;
                    if (addSubLot.Count > 0)
                    {
                        resultSave3 = await _materialSubLotServicesDal.Add(addSubLot) > 0;
                    }
                    bool resultSave4 = true;
                    if (addTran.Count > 0)
                    {
                        resultSave4 = await _MaterialTransferEntityDal.Add(addTran) > 0;
                    }
                    bool resultSave5 = true;
                    if (addUnit.Count > 0)
                    {
                        resultSave5 = await _UnitEntityDal.Add(addUnit) > 0;
                    }

                    bool upDetailSave = true;
                    if (upDetail.Count > 0)
                    {
                        upDetailSave = await _RequestDetailEntity.Update(upDetail);

                        SerilogServer.LogDebug($"WMS收货，更新请料记录:{upDetailSave}", "更新请料记录LOG ");
                    }

                    if (!resultSave || !resultSave1 || !resultSave2 || !resultSave3 || !resultSave4 || !resultSave5 || !upDetailSave)
                    {
                        _unitOfWork.RollbackTran();
                        if (upDetailSave == false)
                        {
                            result.msg = "物料接收失败,保存失败:收料更新状态：" + upDetailSave;
                        }
                        else
                        {
                            result.msg = "物料接收失败,保存失败";
                        }

                        return result;
                    }
                    _unitOfWork.CommitTran();

                    result.msg = "接收成功";
                    result.success = true;
                    return result;
                }
                else
                {
                    SerilogServer.LogDebug($"获取接口失败，失败原因:{models.msg}");

                    result.msg = "获取接口失败，失败原因" + models.msg;
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = "获取接口失败，失败原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }
        #endregion

        #region 收料



        /// <summary>
        /// 判断是否开启节点
        /// </summary>
        /// <param name="eqpID"></param>
        /// <returns></returns>
        public async Task<bool> ISStorage(string eqpID)
        {
            List<DicDestinationEntity> result = new List<DicDestinationEntity>();
            RefAsync<int> dataCount = 0;
            //var whereExpression = Expressionable.Create<DicDestinationEntity>()
            //                 .ToExpression();
            var data = await _dalDicDestinationEntity.Db.Queryable<DicDestinationEntity>()
                .Where(P => P.ID == eqpID).ToListAsync();
            if (data != null && data.Count > 0) { return true; }
            else
            {
                return false;
            }

        }


        /// <summary>
        /// 收料（收已经有的库存数据 收货/收料按钮）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SugarPrePutMtr(SugarPrePutMtr reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                bool isPG = false;
                string oldEqupmentID = string.Empty;
                string newEqupmentID = reqModel.equipmentId;
                string pgEqupmentID = string.Empty;

                _unitOfWork.BeginTran();

                List<MaterialInventoryEntity> listMaterialInventory = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTrans = new List<MaterialTransferEntity>();
                List<ContainerEntity> listCon = new List<ContainerEntity>();
                List<ContainerHistoryEntity> listContainerHis = new List<ContainerHistoryEntity>();

                if (reqModel.sscc != string.Empty)
                {
                    reqModel.sscc = reqModel.sscc.Trim();
                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.Sscc == reqModel.sscc)
                     .ToExpression();
                    var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);
                    if (inventoryModel == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "未找到该追溯码库存清单";
                        return result;
                    }
                    else
                    {
                        var qty = inventoryModel.Quantity;
                        if (qty == null || qty <= 0)
                        {
                            result.msg = "当前追溯码库存数为零不存在";
                            return result;
                        }
                    }


                    //List<InventorylistingViewEntity> lotsscc = await _InventorylistingViewEntityDal.FindList(p => p.Sscc == reqModel.sscc);
                    //if (lotsscc == null || lotsscc.Count <= 0)
                    //{
                    //    result.msg = "当前库存不存在";
                    //    return result;
                    //}
                    //else
                    //{
                    //    var qty = lotsscc[0].Quantity;
                    //    if (qty == null || qty <= 0)
                    //    {
                    //        result.msg = "当前库存数为零不存在";
                    //        return result;
                    //    }
                    //}

                    oldEqupmentID = inventoryModel.EquipmentId;

                    if (!string.IsNullOrEmpty(inventoryModel.ContainerId))
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "当前追溯码已拼锅";
                        return result;
                    }
                    //更新库房位置
                    MaterialInventoryEntity models = _MaterialInventoryEntityDal.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();


                    if (models != null)
                    {
                        if (reqModel.equipmentId == models.EquipmentId)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "当前物料已收料";
                            return result;
                        }

                        #region 查询属性

                        bool isConvert = false;
                        string kgUnitID = string.Empty;

                        //查询属性表
                        MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + reqModel.equipmentId, _user.GetToken(), new { });
                        var equipmentAllData = apiResult_equipmentAllData.response;
                        if (equipmentAllData != null)
                        {
                            var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                            if (item != null)
                            {
                                isConvert = true;
                                var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                                if (umanagerModel != null)
                                {
                                    DFM.Model.Models.UnitmanageEntity model = umanagerModel.FirstOrDefault();
                                    kgUnitID = model.ID;
                                }
                            }
                        }

                        #endregion


                        string batchID = models.BatchId;
                        string proID = models.ProductionRequestId;

                        //不为空直接进拼锅区域
                        if (!string.IsNullOrEmpty(batchID) && !string.IsNullOrEmpty(proID))
                        {
                            var eList = await _equipmentEntity.FindList(p => p.EquipmentCode.Contains("MergePalletArea") || p.EquipmentCode.Contains("拼锅区域"));
                            var equpmentData = eList.FirstOrDefault();
                            if (equpmentData != null)
                            {
                                #region 在这里再次判断下

                                //查询属性表
                                apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equpmentData.ID, _user.GetToken(), new { });
                                equipmentAllData = apiResult_equipmentAllData.response;
                                if (equipmentAllData != null)
                                {
                                    var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                                    if (item != null)
                                    {
                                        isConvert = true;
                                        var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                                        if (umanagerModel != null)
                                        {
                                            DFM.Model.Models.UnitmanageEntity model = umanagerModel.FirstOrDefault();
                                            kgUnitID = model.ID;
                                        }
                                    }
                                }

                                #endregion


                                models.EquipmentId = equpmentData.ID;
                                pgEqupmentID = equpmentData.ID;

                                isPG = true;
                            }
                            else
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "收货到拼锅区域失败";
                                return result;
                            }
                        }
                        else
                        {
                            models.EquipmentId = reqModel.equipmentId;
                        }
                        models.Modify(models.ID, _user.Name.ToString());

                        #region 判断当前物料是否需要单位转换并改变数量

                        if (isConvert == true)
                        {
                            string unitF = inventoryModel.MaxUnit;

                            if (unitF.Trim() != "KG")
                            {
                                //这里进行查询并转换
                                var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == inventoryModel.MaterialCode && P.FormUnitName == unitF && P.ToUnitName == "KG");
                                if (uConventList != null)
                                {
                                    var uConvertModel = uConventList.FirstOrDefault();
                                    //标准值
                                    decimal fValue = uConvertModel.ConvertFormQty.Value;
                                    //目标单位
                                    decimal toValue = uConvertModel.ConvertToQty.Value;
                                    //这里处理数量
                                    decimal inventQty = models.Quantity / fValue * toValue;

                                    models.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3);//  inventQty;//更改数量
                                    models.QuantityUomId = kgUnitID;
                                }

                            }
                        }

                        #endregion

                        listMaterialInventory.Add(models);

                    }
                    string userID = _user.Name.ToString();


                    //这里校验下当前节点

                    var eEntityModel = await _equipmentEntity.FindEntity(p => p.ID == newEqupmentID);
                    if (eEntityModel == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "未找到节点" + newEqupmentID;
                        return result;
                    }



                    //拼锅写两条记录 原始-到目的地   目的地-拼锅
                    if (isPG == true && eEntityModel.EquipmentCode != "MergePalletArea")
                    {
                        if (oldEqupmentID != string.Empty && pgEqupmentID != string.Empty)
                        {
                            if (oldEqupmentID == pgEqupmentID)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "当前追溯码已经在拼锅区域";
                                return result;
                            }
                        }

                        #region 写入转移历史 原始-到目的地

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.Create(userID);
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = reqModel.name;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3); //Convert.ToInt32(inventoryModel.Quantity);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "收料-转移";
                        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentId = oldEqupmentID;
                        trans.NewEquipmentId = newEqupmentID;
                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                        try
                        {
                            trans.Workcenter = await GetWorkCentersByMCodeID(inventoryModel.MaterialId);
                        }
                        catch
                        {

                        }

                        listTrans.Add(trans);

                        #endregion

                        #region 写入转移历史 目的地-拼锅

                        //写入历史记录
                        MaterialTransferEntity transNew = new MaterialTransferEntity();
                        //trans.ID = Guid.NewGuid().ToString();
                        transNew.Create(userID);
                        transNew.OldStorageLocation = inventoryModel.LocationF;
                        transNew.NewStorageLocation = reqModel.name;
                        transNew.OldLotId = inventoryModel.LotId;
                        transNew.NewLotId = inventoryModel.LotId;
                        transNew.OldSublotId = inventoryModel.SlotId;
                        transNew.NewSublotId = inventoryModel.SlotId;
                        transNew.OldExpirationDate = inventoryModel.ExpirationDate;
                        transNew.NewExpirationDate = inventoryModel.ExpirationDate;
                        transNew.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3); //Convert.ToInt32(inventoryModel.Quantity);
                        transNew.QuantityUomId = inventoryModel.QuantityUomId;
                        transNew.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        transNew.Type = "Transfer Inventory";
                        transNew.Comment = "收料-转移";
                        transNew.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        transNew.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        transNew.OldEquipmentId = newEqupmentID;
                        transNew.NewEquipmentId = pgEqupmentID;
                        transNew.OldContainerId = inventoryModel.ContainerId;
                        transNew.NewContainerId = inventoryModel.ContainerId;
                        transNew.OldMaterialId = inventoryModel.MaterialId;
                        transNew.NewMaterialId = inventoryModel.MaterialId;
                        transNew.OldLotExternalStatus = inventoryModel.StatusF;
                        transNew.OldSublotExternalStatus = inventoryModel.StatusS;
                        transNew.NewLotExternalStatus = inventoryModel.StatusF;
                        transNew.NewSublotExternalStatus = inventoryModel.StatusS;
                        transNew.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                        transNew.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                        try
                        {
                            transNew.Workcenter = await GetWorkCentersByMCodeID(inventoryModel.MaterialId);
                        }
                        catch
                        {

                        }

                        listTrans.Add(transNew);

                        #endregion
                    }
                    else
                    {
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.Create(userID);
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = reqModel.name;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3); //Convert.ToInt32(inventoryModel.Quantity);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "收料-转移";
                        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentId = inventoryModel.EquipmentId;
                        trans.NewEquipmentId = reqModel.equipmentId;
                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                        try
                        {
                            trans.Workcenter = await GetWorkCentersByMCodeID(inventoryModel.MaterialId);
                        }
                        catch
                        {

                        }

                        listTrans.Add(trans);

                        #endregion
                    }

                    #region 修改容器位置(存在则进行转移位置)

                    //if (string.IsNullOrEmpty(inventoryModel.ContainerId))
                    //{
                    //    result.msg = "转移失败,容器信息缺失,请到批次托盘菜单进行操作";
                    //    return result;
                    //}
                    if (!string.IsNullOrEmpty(inventoryModel.ContainerId))
                    {
                        //result.msg = "不存在容器信息";
                        //return result;

                        //查询之前的位置
                        var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                        var containerModel = await _ContainerEntity.FindEntity(whereLocation);
                        //更新容器新的位置
                        ContainerEntity model = await _ContainerEntity.QueryById(inventoryModel.ContainerId);
                        model.Modify(model.ID, _user.Name.ToString());
                        model.EquipmentId = reqModel.equipmentId;
                        listCon.Add(model);


                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);
                        hisModel.ContainerId = inventoryModel.ContainerId;
                        hisModel.Type = "";
                        hisModel.EquipmentId = reqModel.equipmentId;
                        hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        hisModel.State = containerModel.Status;
                        hisModel.Comment = containerModel.Comment;
                        hisModel.MaterialId = inventoryModel.MaterialId;
                        hisModel.SublotId = inventoryModel.SubLotId;
                        hisModel.Quantity = inventoryModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                        hisModel.LotId = inventoryModel.LotId;
                        hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                        listContainerHis.Add(hisModel);
                    }

                    #endregion

                }
                else
                {
                    result.msg = "追溯码为空，请重新扫描";
                    return result;
                }

                //插入
                bool tranHis = await _MaterialTransferEntityDal.Add(listTrans) > 0;
                bool upResult = await _MaterialInventoryEntityDal.Update(listMaterialInventory);

                bool hisUp = true;
                if (listCon.Count > 0)
                {

                    hisUp = await _ContainerEntity.Update(listCon);
                }
                bool rqResult = true;
                if (listContainerHis.Count > 0)
                {
                    rqResult = await _ContainerHistoryEntityDal.Add(listContainerHis) > 0;
                }
                if (!upResult || !tranHis || !rqResult || !hisUp)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "收料失败！";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.msg = "收料成功！";

            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                result.msg = "收料失败！";
                return result;
            }
            result.success = true;
            return result;
        }
        #endregion

        #region 合并收料和收货


        public async Task<MessageModel<string>> SugarPutOrPrePutMtrDP(SugarPrePutMtr reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            //判断当前库存数据是否存在
            //var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.Sscc == reqModel.sscc)
            //         .ToExpression();

            //   var inventoryModel = await _materialSubLotServicesDal.FindEntity(p => p.SubLotId == reqModel.sscc);
            var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(p => p.SubLotId == reqModel.sscc);


            if (inventoryModel != null)
            {
                result.success = true;
                result.msg = "收料";
                return result;//拆分成功
            }
            else
            {
                result.success = true;
                result.msg = "扫码收货";
                return result;//拆分成功
            }
        }

        /// <summary>
        /// 合并收料和收货
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SugarPutOrPrePutMtr(SugarPrePutMtr reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;



            //首先判断当前子批次和EqupmentID是否存在
            if (reqModel.sscc == null || reqModel.sscc == "")
            {
                result.msg = "追溯码为空，请重新扫描";
                return result;
            }

            reqModel.sscc = reqModel.sscc.Trim();

            if (reqModel.equipmentId == null || reqModel.equipmentId == "")
            {
                result.msg = "存储地点为空";
                return result;
            }

            //判断当前库存数据是否存在
            //var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.Sscc == reqModel.sscc)
            //         .ToExpression();
            //var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);


            //var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.Sscc == reqModel.sscc)
            //      .ToExpression();
            var inventoryModel = await _materialSubLotServicesDal.FindEntity(p => p.SubLotId == reqModel.sscc);
            if (inventoryModel != null)
            {
                //收料
                var resultPutMtr = await SugarPrePutMtr(reqModel);
                return resultPutMtr;
            }
            else
            {
                //收货
                SugarPrePutModel suModel = new SugarPrePutModel();
                suModel.equipmentId = reqModel.equipmentId;
                suModel.sscc = reqModel.sscc;
                suModel.name = reqModel.name;
                var resultPutMtr = await SugarPrePut(suModel);
                return resultPutMtr;
            }
        }

        #endregion

        #region 白糖预处理/拆分

        /// <summary>
        /// 白糖预处理/拆分
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPreSplit(SugarPreSplitModel models)
        {
            var result = new MessageModel<string>();
            result.success = false;
            #region 构造实体
            SSCCModel model = new SSCCModel();
            model.Type = "";
            model.NextCode = "";
            model.MaxCode = "";
            model.MinCode = "";
            model.Prefix = "";
            model.TableName = "";
            model.TableId = "";
            model.SequenceType = "";
            model.ResetType = "";
            model.FeatureId = "";
            model.pageIndex = 1;
            model.pageSize = 10;
            model.orderByFileds = "";
            string token = _user.GetToken();

            #endregion

            var ssccStrings1 = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);

            string splitID = models.splitID;
            string equipmentId = models.equipmentId;
            string quantity = models.quantity;
            bool isPrint = models.isPrint;
            string selectPrint = models.selectPrint;
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(splitID))
            {
                result.msg = "请确认合并信息是否存在";
                return result;
            }


            //查询更新之前的数据
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.ID == splitID)
                 .ToExpression();
            var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);
            if (inventoryModel == null)
            {
                result.msg = "拆分失败，未能找到合并数据";
                return result;
            }
            try
            {
                _unitOfWork.BeginTran();

                List<MaterialTransferEntity> inserTrans = new List<MaterialTransferEntity>();

                List<MaterialInventoryEntity> inserInvens = new List<MaterialInventoryEntity>();
                List<MaterialSubLotEntity> inserSubLots = new List<MaterialSubLotEntity>();

                //查询原数据
                var modelInven = await _dal.QueryById(splitID);

                if (modelInven == null)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "拆分失败，未能找到数据";
                    return result;
                }

                if (modelInven.Quantity < Convert.ToDecimal(models.quantity))
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "拆分失败，拆分数量大于原有数量";
                    return result;
                }
                string newSScc = string.Empty;

                var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                if (ssccStrings.success == true)
                {
                    newSScc = ssccStrings.response;
                }
                #region 新建子批次

                string subID = string.Empty;
                MaterialSubLotEntity materialsub = new MaterialSubLotEntity();
                materialsub.Create(_user.Name.ToString());
                materialsub.SubLotId = newSScc;
                materialsub.Type = "0";
                materialsub.ExternalStatus = "3";
                subID = materialsub.ID.ToString();

                inserSubLots.Add(materialsub);
                #endregion

                #region 写入历史记录

                //写入历史记录
                MaterialTransferEntity trans1 = new MaterialTransferEntity();
                trans1.Create(_user.Name.ToString());
                trans1.OldStorageLocation = inventoryModel.LocationF;
                trans1.NewStorageLocation = inventoryModel.LocationF;
                trans1.OldLotId = inventoryModel.LotId;
                trans1.NewLotId = inventoryModel.LotId;
                trans1.OldSublotId = inventoryModel.SubLotId;
                trans1.NewSublotId = subID;
                trans1.OldExpirationDate = inventoryModel.ExpirationDate;
                trans1.NewExpirationDate = inventoryModel.ExpirationDate;
                trans1.Quantity = Math.Round(Convert.ToDecimal(models.quantity), 3);  // Convert.ToDecimal(models.quantity);
                trans1.QuantityUomId = inventoryModel.QuantityUomId;
                trans1.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans1.Type = "Split";
                trans1.Comment = "拆分";
                trans1.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                trans1.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                trans1.OldEquipmentId = inventoryModel.EquipmentId;
                trans1.NewEquipmentId = equipmentId;
                trans1.OldContainerId = inventoryModel.ContainerId;
                trans1.NewContainerId = inventoryModel.ContainerId;
                trans1.OldMaterialId = inventoryModel.MaterialId;
                trans1.NewMaterialId = inventoryModel.MaterialId;
                trans1.OldLotExternalStatus = inventoryModel.StatusF;
                trans1.OldSublotExternalStatus = inventoryModel.StatusS;
                trans1.NewLotExternalStatus = inventoryModel.StatusF;
                trans1.NewSublotExternalStatus = inventoryModel.StatusS;
                trans1.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                trans1.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                inserTrans.Add(trans1);


                #endregion

                #region 新增

                //新增
                MaterialInventoryEntity inserInvent = new MaterialInventoryEntity();
                inserInvent.Create(_user.Name.ToString());
                inserInvent.LotId = modelInven.LotId;
                inserInvent.SublotId = subID;
                inserInvent.Quantity = Math.Round(Convert.ToDecimal(models.quantity), 3); //Convert.ToDecimal(models.quantity);
                inserInvent.QuantityUomId = modelInven.QuantityUomId;
                inserInvent.StorageLocation = modelInven.StorageLocation;
                inserInvent.EquipmentId = modelInven.EquipmentId;
                inserInvent.IsPrechecked = modelInven.IsPrechecked;
                inserInvent.ContainerId = modelInven.ContainerId;
                inserInvent.ProductionRequestId = modelInven.ProductionRequestId;
                inserInvent.SortOrder = modelInven.SortOrder;
                inserInvens.Add(inserInvent);
                #endregion

                //这里打印需要考虑数据
                if (isPrint == true)
                {
                    //执行打印
                    //selectPrint 打印机
                }

                #region 处理余量
                //余量
                decimal bagsSurplus = Math.Round(Convert.ToDecimal(modelInven.Quantity), 3) - Math.Round(Convert.ToDecimal(models.quantity), 3);   //modelInven.Quantity - Convert.ToDecimal(models.quantity);
                #region 写入历史记录

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                trans.OldStorageLocation = inventoryModel.LocationF;
                trans.NewStorageLocation = inventoryModel.LocationF;
                trans.OldLotId = inventoryModel.LotId;
                trans.NewLotId = inventoryModel.LotId;
                trans.OldSublotId = inventoryModel.SubLotId;
                trans.NewSublotId = inventoryModel.SubLotId;
                trans.OldExpirationDate = inventoryModel.ExpirationDate;
                trans.NewExpirationDate = inventoryModel.ExpirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(bagsSurplus), 3);  //Convert.ToDecimal(bagsSurplus);
                trans.QuantityUomId = inventoryModel.QuantityUomId;
                trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Split";
                trans.Comment = "拆分";
                trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = inventoryModel.EquipmentId;
                trans.NewEquipmentId = equipmentId;
                trans.OldContainerId = inventoryModel.ContainerId;
                trans.NewContainerId = inventoryModel.ContainerId;
                //status
                trans.OldMaterialId = inventoryModel.MaterialId;
                trans.NewMaterialId = inventoryModel.MaterialId;
                trans.OldLotExternalStatus = inventoryModel.StatusF;
                trans.OldSublotExternalStatus = inventoryModel.StatusS;
                trans.NewLotExternalStatus = inventoryModel.StatusF;
                trans.NewSublotExternalStatus = inventoryModel.StatusS;
                //trans.PhysicalQuantity 物理数量
                //trans.TareQuantity 皮数量
                trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                inserTrans.Add(trans);


                #endregion

                //更新
                modelInven.Modify(splitID, _user.Name.ToString());
                //余量
                modelInven.Quantity = Math.Round(Convert.ToDecimal(bagsSurplus), 3);  //Convert.ToDecimal(bagsSurplus);
                #endregion

                await _MaterialTransferEntityDal.Add(inserTrans);
                await _dal.Add(inserInvens);
                await _materialSubLotServicesDal.Add(inserSubLots);

                await _dal.Update(modelInven);
                //这里打印需要考虑数据
                if (isPrint == true)
                {
                    //执行打印
                    //selectPrint 打印机

                    if (inserInvens.Count > 0)
                    {
                        //打印库存标签
                        for (int i = 0; i < inserInvens.Count; i++)
                        {
                            var inventViewData = await _IPrintSelectViewServices.GetInventLabel(inserInvens[i].ID, "");
                            List<object> objs = new List<object>();
                            objs.Add(inventViewData);

                            //这里拿打印机，默认拿第一个
                            var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_Bag();

                            if (printIDData != null && printIDData.Count >= 0)
                            {
                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.selectPrint, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                            }

                        }
                    }

                    if (modelInven != null)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(modelInven.ID, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //这里拿打印机，默认拿第一个
                        var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_Bag();

                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            //执行打印
                            await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.selectPrint, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        }

                    }


                }
                _unitOfWork.CommitTran();

                result.success = true;
                return result;//拆分成功
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "拆分失败";
                return result;
            }

        }


        #endregion

        #endregion

        #region 判断节点


        /// <summary>
        /// 对比节点并获取是否管理库存信息(返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存)
        /// </summary>
        /// <param name="equpIDFrom"></param>
        /// <param name="equpIDTo"></param>
        /// <returns></returns>
        public async Task<string> EquipmentIsIdentical(string equpIDFrom, string equpIDTo)
        {
            string equpF = await GetEquipment(equpIDFrom);
            string equpT = await GetEquipment(equpIDTo);

            if (equpF == string.Empty || equpT == string.Empty)
            {
                return "";
            }
            else
            {
                string[] fSplit = equpF.Split(';');
                string[] tSplit = equpT.Split(";");

                if (fSplit[0].Trim().ToString() == tSplit[0].Trim().ToString())
                {
                    return "OK" + ";" + tSplit[1].Trim();
                }
                else
                {
                    return "NO" + ";" + tSplit[1].Trim();
                }
            }

        }

        /// <summary>
        /// 根据节点插叙是否管理库存（返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存）
        /// </summary>
        /// <param name="equipmentID"></param>
        /// <returns></returns>
        public async Task<string> GetEquipment(string equipmentID)
        {

            string typeEquipmentID = equipmentID;
            var api_equipmentRequirement = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { EquipmentId = typeEquipmentID });
            DFM.Model.Models.EquipmentStorageEntity storage = api_equipmentRequirement.response.Find(p => p.EquipmentId == typeEquipmentID);
            if (storage == null)
            {
                return string.Empty;
            }

            var api_equirement = await HttpHelper.GetApiAsync<DFM.Model.Models.EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + storage.EquipmentRequirementId, _user.GetToken(), null);
            var equirement = api_equirement.response;
            if (equirement == null)
            {
                return string.Empty;
            }
            else
            {
                var sapStorageType = equirement.ManageInventory;

                if (string.IsNullOrEmpty(sapStorageType))
                {
                    return equirement.Code + ";" + "空";
                }
                return equirement.Code + ";" + sapStorageType;
            }
        }


        public async Task<string> GetEquipmentID(string type)
        {
            /// <param name="equipmentID">工厂节点的ID</param>
            string id = "02308281-5121-6559-163e-0370f6000000";
            //找工厂配置的退库和报废车间
            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + id, _user.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData == null)
            {
                return "";
            }
            //查询对应的属性
            List<EquFunctionPropertyModel> propertyList = equipmentAllData.EquipmentFunctionPropertyList;
            if (propertyList == null)
            {
                return "";
            }
            //获取属性
            List<EquipmentFunctionPropertyModel> funtionModel = propertyList[0].ActiveFunctionPropertyList;
            if (funtionModel == null)
            {
                return "";
            }
            //获取类型
            EquipmentFunctionPropertyModel model = funtionModel.Where(p => p.PropertyCode.Contains(type)).FirstOrDefault();
            if (model == null)
            {
                return "";
            }
            Dictionary<string, string> dic = new Dictionary<string, string>();
            //拿到设备
            string typeCode = model.ActualValue;
            if (string.IsNullOrEmpty(typeCode)) return "";

            var api_equipments = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentEntity>>("DFM", "api/Equipment/GetList", _user.GetToken(), new { EquipmentCode = typeCode });
            var equipments = api_equipments.response;
            if (equipments == null || equipments.Count == 0)
            {
                return "";
            }
            var eModel = equipments.Find(P => P.EquipmentCode == typeCode);
            string typeEquipmentID = eModel.ID;

            //var api_equipmentRequirement = await HttpHelper.PostAsync<DFM.Model.Models.EquipmentStorageEntity>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
            var api_equipmentRequirement = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { EquipmentId = typeEquipmentID });


            DFM.Model.Models.EquipmentStorageEntity storage = api_equipmentRequirement.response.Find(p => p.EquipmentId == typeEquipmentID);
            if (storage == null)
            {
                return "";
            }

            var api_equirement = await HttpHelper.GetApiAsync<DFM.Model.Models.EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + storage.EquipmentRequirementId, _user.GetToken(), null);
            var equirement = api_equirement.response;
            if (equirement == null)
            {
                return string.Empty;
            }
            else
            {
                var sapStorageType = equirement.ManageInventory;
                if (string.IsNullOrEmpty(sapStorageType))
                {
                    return "";
                }
                if (sapStorageType == "1")
                {
                    return typeEquipmentID;
                }
            }

            return "";
        }



        #endregion

        #region 打印类MKM使用（打印类）     

        /// <summary>
        /// 根据设备ID拿对应属性值(这里是拿teampID)
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="propertyCode"></param>
        /// <returns></returns>
        public async Task<DFM.Model.Models.LabelTempleteEntity> GetTeampID(string equipmentId, string propertyCode)
        {
            DFM.Model.Models.LabelTempleteEntity model = new DFM.Model.Models.LabelTempleteEntity();
            try
            {


                var value = string.Empty;
                //获取allData
                MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys = await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM", "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _user.GetToken(), new { EquipmentId = equipmentId });
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
                if (equFunctionPropertys?.Count > 0)
                {
                    foreach (var item in equFunctionPropertys)
                    {
                        var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
                        if (pr != null)
                        {
                            value = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
                        }
                        if (!string.IsNullOrEmpty(value))
                        {
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(value))
                {
                    //获取对应的模板ID
                    MessageModel<List<DFM.Model.Models.LabelTempleteEntity>> api_LabelTeamp = await HttpHelper.PostAsync<List<DFM.Model.Models.LabelTempleteEntity>>("DFM", "api/LabelTemplete/GetList", _user.GetToken(), new { Code = value });
                    var data_LabelTeamp = api_LabelTeamp.response;

                    if (data_LabelTeamp?.Count > 0)
                    {
                        model = data_LabelTeamp[0];
                        return model;
                    }
                }
                return model;
            }
            catch
            {
                return model;
            }

        }



        #region 打印机器类


        // http://*************:30017/api/LabelPrinter/GetList
        // POST 调用所有打印机
        // 传入参数 { 
        //  "pageIndex": 1,
        //  "pageSize": 200 
        //}

        //返回数据
        //    
        //  "Code": "Zebra_Printer",
        //  "EquipmentId": "02308281-5121-6559-163e-0370f6000000",
        //  "PrinterClassId": "02405221-1432-4115-0027-c52a7c000000",
        //  "Description": "测试打印机",
        //  "Status": "1",
        //  "AllowManualPrint": "1",
        //  "Type": "printer",
        //  "SequenceType": "string",
        //  "LabelSizeId": "02405221-1292-6322-0027-c52a7c000000",
        //  "ID": "02405221-3094-5063-0027-c52a7c000000",
        //  "CreateDate": "2024-05-22 13:09:45",
        //  "CreateUserId": "NonUser",
        //  "ModifyDate": "2024-05-22 13:09:45",
        //  "ModifyUserId": "NonUser",
        //  "UpdateTimeStamp": "AAAAAAABJbw="
        //

        #endregion

        #region 打印的类型

        //http://*************:30017/api/LabelPrinterClass/GetList
        //Post 根据code调用对应的编码规则
        //传入参数
        //        {
        //  "Code": "",    打印机器类（来自于此接口）
        //  "pageIndex": 1,
        //  "pageSize": 200

        //}
        //返回值:
        //  "Code": "Zebra", 
        //"Description": "斑马打印机",
        //"ID": "02405221-1432-4115-0027-c52a7c000000",
        //"CreateDate": "2024-05-22 11:43:24",
        //"CreateUserId": "NonUser",
        //"ModifyDate": "2024-05-22 11:43:24",
        //"ModifyUserId": "NonUser",
        //"UpdateTimeStamp": "AAAAAAABJa8="

        #endregion

        #region 打印模板ID

        //http://*************:30017/api/LabelTemplete/GetList
        //post 获取模板ID 这里需要用第一个函数的数据来进行定位模板ID 和Data属性
        //传参
        //        {
        //  "Code": "",   打印机器类 Code
        //  "PrinterClassId": "", 打印机器类 PrinterClassId
        //  "LabelSizeId": "", 打印机器类 LabelSizeId
        //  "pageIndex": 1, 
        //  "pageSize": 200

        //}
        //返回值：

        //  "Code": "Test_Temp_Zebra_A",
        //"Description": "Zebra_A模板",
        //"Data": "打印模板如下用户工号<UserCode>,用户姓名<UserName>",
        //"PrinterClassId": "02405221-1432-4115-0027-c52a7c000000",
        //"TemplateClassId": "02405221-1475-0490-0027-c52a7c000000",
        //"Variant": "printer",
        //"IsEditable": "0",
        //"PreviewConfig": null,
        //"IsDefault": "1",
        //"LabelSizeId": "02405221-1292-6322-0027-c52a7c000000",
        //"PreviewCommand": null,
        //"FileExtension": null,
        //"FileNamingType": null,
        //"ID": "02405221-1533-6397-0027-c52a7c000000",
        //"CreateDate": "2024-05-22 11:53:36",
        //"CreateUserId": "NonUser",
        //"ModifyDate": "2024-05-22 11:53:36",
        //"ModifyUserId": "NonUser",
        //"UpdateTimeStamp": "AAAAAAABJbs="
        #endregion


        /// <summary>
        /// 获取列名
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<string> GetColumnNames<T>(List<T> list)
        {
            if (list.Count == 0)
                return new List<string>();
            ;
            PropertyInfo[] properties = list[0].GetType().GetProperties();
            List<string> columnNames = new List<string>();

            foreach (PropertyInfo property in properties)
            {
                columnNames.Add(property.Name);
            }

            return columnNames;
        }


        /// <summary>
        /// 获取指定列数据源
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static object GetValueByReflection(object obj, string propertyName)
        {
            // 使用反射获取属性值
            PropertyInfo property = obj.GetType().GetProperty(propertyName);
            return property?.GetValue(obj, null);
        }


        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="prinitID">选中打印机id</param>
        /// <param name="quipmentId">房间ID</param>
        /// <param name="listobj">数据源头（用来获取表头）</param>
        /// <param name="dataList">标签数据</param>
        /// <param name="size">打印页数</param>       
        public async Task<MessageModel<string>> PrintCodeByEquipmentId(string prinitID, string quipmentId, List<object> listobj, object dataList, int size)
        {

            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {

                var models = await GetTeampID(quipmentId, "PrintTemplete");
                if (models == null)
                {
                    pResult.msg = "请配置打印机";
                    return pResult;
                }
                string templateID = models.ID;
                string templateclassID = models.TemplateClassId; ;

                Dictionary<string, object> dic = new Dictionary<string, object>();
                #region 获取列名 

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数
                #region 打印机id

                #region 打印实体

                string token = _user.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion
                //获取数据
                var printResult = await HttpHelper.PostAsync<List<DFM.Model.Models.LabelPrinterEntity>>("DFM", "api/LabelPrinter/GetList", token, model);
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].
                        #region 查询Temp数据源
                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, templateID, templateclassID, dic, size, pSizeId, equID);
                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = "打印成功";
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion



        }

        /// <summary>
        /// 打印数据
        /// </summary>
        /// <param name="prinitID">打印id</param>
        /// <param name="teampID">模板Code</param>
        /// <param name="teampClassID">类型code</param>
        /// <param name="listobj">数据源</param>
        /// <param name="dataList">数据列</param>
        /// <returns></returns>

        public async Task<MessageModel<string>> PrintByCode(string prinitID, string teampID, string teampClassID, List<object> listobj, object dataList)
        {

            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                #region 获取列名 

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数
                #region 打印机id

                #region 打印实体

                string token = _user.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion
                //获取数据
                var printResult = await HttpHelper.PostAsync<List<DFM.Model.Models.LabelPrinterEntity>>("DFM", "api/LabelPrinter/GetList", token, model);
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].
                        #region 查询Temp数据源

                        //#region 实体

                        ////构造实体
                        //LabelTempleteRequestModel templeteRequestModel = new LabelTempleteRequestModel();
                        //templeteRequestModel.Code = pritModel[0].Code;
                        //templeteRequestModel.LabelSizeId = pSizeId;
                        //templeteRequestModel.PrinterClassId = pClassID;
                        //templeteRequestModel.pageIndex = 1;
                        //templeteRequestModel.pageSize = 1000;

                        //#endregion


                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, teampID, teampClassID, dic, 1, pSizeId, equID);

                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = "创建库存成功，打印成功";
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = "创建库存成功" + printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion


        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="printerID">打印机ID</param>
        /// <param name="printerClass">打印机类ID</param>
        /// <param name="templateID">模板ID</param>
        /// <param name="printParam">打印参数</param>
        /// <param name="printNum">打印份数</param>
        /// <param name="labelSizeID">打印尺寸</param>
        /// <param name="eqmID">eqmID</param>
        /// <returns></returns>

        public async Task<string> CurrencyPrints(string printerID, string printerClass, string templateID, string templateClassID, Dictionary<string, object> printParam, int printNum, string labelSizeID, string eqmID)
        {

            #region 打印实体

            string token = _user.GetToken();
            LabelPrinterParamModel model = new LabelPrinterParamModel();
            model.EquipmentId = eqmID;

            model.PrinterId = printerID;
            model.PrinterClassId = printerClass;

            model.TemplateId = templateID;
            model.TemplateClassId = templateClassID;


            model.PrintNum = printNum;
            model.LabelSizeId = labelSizeID;
            model.PrintParameters = printParam;

            #endregion

            var printResult = await HttpHelper.PostAsync<string>("DFM", "api/LabelPrint/Print", token, model);
            if (printResult.success == true)
            {
                return "打印成功";
            }
            else
            {
                return "打印失败" + printResult.msg;
            }
        }

        #endregion


        #region 读取电子秤   

        #region 称下拉框


        public async Task<List<ScaleSelectViewEntity>> GetScaleList(string eqmID)
        {
            //查询数据
            var whereExpression = Expressionable.Create<ScaleSelectViewEntity>().And(P => P.EquipmentId == eqmID)
                             .ToExpression();
            var data = await _dalScaleSelectViewEntity.FindList(whereExpression);

            if (data.Count == 0)
            {
                return new List<ScaleSelectViewEntity>();
            }
            string scale = string.Empty;
            for (int i = 0; i < data.Count; i++)
            {
                //存在对应的数据进行绑定 第一个分号称名称，第二个分号IP，第三个分号端口
                string[] splitArray = data[i].PropertyValue.Split(';');
                splitArray = splitArray.Where(s => !string.IsNullOrWhiteSpace(s)).ToArray();
                if (splitArray.Length >= 3)
                {
                    data[i].ScaleName = splitArray[0];//称名称
                    data[i].ScaleIp = splitArray[1];//称IP
                    data[i].ScalePort = splitArray[2];//称端口
                    data[i].Remark = splitArray[3];//称端口
                }
            }

            return data;
        }


        #endregion


        public static decimal ConvertToDecimal(string value)
        {
            // 使用正则表达式移除非数字字符，并转换结果为double
            string numericValue = Regex.Replace(value, "[^.0-9]+", "");
            return decimal.Parse(numericValue);
        }

        public async Task<CallModel> ReadCallData(string printer, string ip, string port)
        {
            TcpClient client = new TcpClient();
            CallModel model = new CallModel();
            try
            {
                //printer = "23";
                //ip = "************";
                //port = "10006";
                if (string.IsNullOrEmpty(printer) || string.IsNullOrEmpty(ip) || string.IsNullOrEmpty(port))
                {
                    model.ResultData = "";
                    model.Data = "0";
                    model.ValueType = "+";
                    model.unit = "kg";
                    return model;
                }
                //找到打印机配置，动态获取对应的数据源 (打印机的ip和端口号)      
                string serverIp = ip;// "***********";
                int serverPort = Convert.ToInt32(port);
                client = new TcpClient(serverIp, serverPort); // 替换为电子称的实际IP地址和端口号
                                                              //连接失败休息一秒钟
                if (client.Connected == false)
                {
                    model.ResultData = "";
                    model.Data = "0";
                    model.ValueType = "+";
                    model.unit = "kg";
                    return model;
                }
                NetworkStream stream = client.GetStream();
                stream.ReadTimeout = 1000 * 60;
                if (stream.CanRead)
                {
                    byte[] bytes = new byte[client.ReceiveBufferSize];
                    int bytesRead = stream.Read(bytes, 0, client.ReceiveBufferSize);
                    string results = Encoding.ASCII.GetString(bytes, 0, bytesRead).Trim();
                    model = GetConvetDataLast(results);
                }

                // 关闭TCP连接
                client.Close();

                //if (string.IsNullOrEmpty(model.Data)) 
                //{
                //    model.ResultData = "";
                //    model.Data = "0";
                //    model.ValueType = "+";
                //    model.unit = "kg";
                //    return model;
                //}

                return model;
            }
            catch
            {
                model.ResultData = "";
                model.Data = "0";
                model.ValueType = "+";
                model.unit = "kg";
                client.Close();
                return model;
            }
        }


        private CallModel GetConvetDataLast(string reData)
        {
            try
            {
                CallModel model = new CallModel();
                model.ResultData = "0";
                model.Data = "0";
                model.ValueType = "&&&";
                model.unit = "kg";

                //对数据进行分组
                string[] countStr = reData.Split(new string[] { "\r\n" }, StringSplitOptions.None);
                if (countStr != null && countStr.Length > 0)
                {
                    int count = countStr.Length;
                    //获取最后一个取值
                    string results = countStr[countStr.Length - 1];

                    //拿单位（默认最后两位）
                    string unitCode = results.Substring(results.Length - 2, 2);
                    //拿正负值默认第一位置
                    string type = results.Substring(0, 1);

                    //如果包含单位，这里直接返回当前值(说明取值不完整)
                    if (!unitCode.Contains("kg") && !unitCode.Contains("g") && !unitCode.Contains("lb") && !unitCode.Contains("oz"))
                    {
                        #region 这里做一次数据防错,不完整的数据直接读取前一个值

                        if (count < 2)
                        {
                            return model;
                        }

                        results = countStr[countStr.Length - 2];

                        unitCode = results.Substring(results.Length - 2, 2);
                        //拿正负值默认第一位置
                        type = results.Substring(0, 1);
                        #endregion
                    }
                    //拿数据
                    string rData = results.Substring(1, results.Length - 3);

                    if (unitCode.Trim() == "g")
                    {
                        string convetData = Math.Round(Convert.ToDouble(rData) / Convert.ToDouble(1000), 4).ToString();//多保留一位小数
                        model.Data = convetData;
                    }
                    else
                    {
                        model.Data = rData;
                    }
                    model.ResultData = results;
                    model.OldData = rData + unitCode.Trim();
                    model.ValueType = type.Trim();
                    model.unit = unitCode.Trim();
                }
                return model;
            }
            catch (Exception)
            {
                return new CallModel();
            }
        }

        #endregion

        #region 读梅特勒托利多电子秤

        public async Task<CallModel> ReadCallDataMetiler(string printer, string ip, string port)
        {
            TcpClient client = new TcpClient();
            CallModel model = new CallModel();
            try
            {
                if (string.IsNullOrEmpty(printer) || string.IsNullOrEmpty(ip) || string.IsNullOrEmpty(port))
                {
                    model.ResultData = "";
                    model.Data = "0";
                    model.ValueType = "+";
                    model.unit = "kg";
                    return model;
                }
                //找到打印机配置，动态获取对应的数据源 (打印机的ip和端口号)      
                string serverIp = ip;// "***********";
                int serverPort = Convert.ToInt32(port);
                client = new TcpClient(serverIp, serverPort); // 替换为电子称的实际IP地址和端口号
                                                              //连接失败休息一秒钟
                if (client.Connected == false)
                {
                    model.ResultData = "";
                    model.Data = "0";
                    model.ValueType = "+";
                    model.unit = "kg";
                    return model;
                }
                NetworkStream stream = client.GetStream();
                stream.ReadTimeout = 1000 * 30;
                if (stream.CanRead)
                {
                    //发送
                    byte[] data = Encoding.UTF8.GetBytes("SI\r");
                    stream.Write(data, 0, data.Length);
                    //接收
                    byte[] bytes = new byte[client.ReceiveBufferSize];
                    int bytesRead = stream.Read(bytes, 0, client.ReceiveBufferSize);
                    string results = Encoding.ASCII.GetString(bytes, 0, bytesRead).Trim();
                    if (results.Length > 18)
                    {
                        //截单位和数据
                        string unit = results.Substring(19, results.Length - 19).Trim();
                        string sub18 = results.Substring(0, 18);
                        string numStr = sub18.Substring(3, sub18.Length - 3).Trim();

                        model.ResultData = results;
                        model.OldData = numStr + unit;
                        model.Data = numStr;
                        model.ValueType = "+";
                        model.unit = unit;
                    }
                    else if (results.Length > 16) //测试通过
                    {
                        string trimResult = results.Trim();
                        //截单位和数据
                        string unit = results.Substring(results.Length - 3, 3).Trim();
                        string sub18 = results.Substring(0, results.Length - 3).Trim().Replace(" ", "");
                        string numStr = sub18.Substring(2, sub18.Length - 2).Trim();

                        model.ResultData = results;
                        model.OldData = numStr + unit;
                        model.Data = numStr;
                        model.ValueType = "+";
                        model.unit = unit;
                    }
                    else
                    {
                        return model;
                    }
                }
                // 关闭TCP连接
                client.Close();
                return model;
            }
            catch (Exception ex)
            {
                model.ResultData = "";
                model.Data = "0";
                model.ValueType = "+";
                model.unit = "kg";
                client.Close();
                return model;
            }
        }

        #endregion


        #region 喉头PDA

        #region 查询

        /// <summary>
        /// 查询喉头（状态1是已出仓，0是未出仓） PAD扫码后判断状态 已出仓就有撤回按钮
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<ColdAWarehouseViewEntity>> GetThroat_PDAList(ColdAWarehouseViewRequestModel reqModel)
        {

            if (!string.IsNullOrEmpty(reqModel.PlanEnd))
            {
                reqModel.PlanEnd = reqModel.PlanEnd + " 23:59:59";
            }

            if (reqModel.TYPE == "三楼喉头仓")
            {
                reqModel.EquipmentCode = "ThirdFloorThroatRoom";
            }
            else if (reqModel.TYPE == "四楼喉头仓")
            {
                reqModel.EquipmentCode = "ForthFloorThroatRoom";
            }

            var whereExpression = Expressionable.Create<ColdAWarehouseViewEntity>()
                  .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.MatModifydate >= Convert.ToDateTime(reqModel.StartTime))
                  .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.MatModifydate <= Convert.ToDateTime(reqModel.EndTime))
                  .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode.Contains(reqModel.EquipmentCode) || a.MatEquipmentcode.Contains(reqModel.EquipmentCode))
                  .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.Sscc.Contains(reqModel.SSCC))
                   .AndIF(!string.IsNullOrEmpty(reqModel.PlanStar), a => a.PlanDate >= Convert.ToDateTime(reqModel.PlanStar))
                  .AndIF(!string.IsNullOrEmpty(reqModel.PlanEnd), a => a.PlanDate <= Convert.ToDateTime(reqModel.PlanEnd))
                             .ToExpression();
            var data = await _dalColdAWarehouseViewEntity.FindList(whereExpression);
            return data.OrderBy(p => p.MatStatus).ThenByDescending(p => p.ModifyDate).ToList();
        }


        public async Task<MessageModel<string>> GetDataStatus(string SSCC, string type, DateTime star, DateTime end)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (type == "三楼喉头仓")
            {
                type = "ThirdFloorThroatRoom";


            }
            else if (type == "四楼喉头仓")
            {
                type = "ForthFloorThroatRoom";
            }
            var whereExpression = Expressionable.Create<ColdAWarehouseViewEntity>().And(a => a.Sscc.Contains(SSCC))
                  .AndIF(!string.IsNullOrEmpty(type), a => a.EquipmentCode.Contains(type) || a.MatEquipmentcode.Contains(type))
             .ToExpression();

            var data = await _dalColdAWarehouseViewEntity.FindList(whereExpression);
            if (data != null && data.Count > 0)
            {
                int state = data[0].MatStatus;
                if (state == 1)
                {
                    result.msg = "撤回";
                    result.success = true;
                }
                else
                {
                    if (data[0].PlanDate == null)
                    {
                        result.msg = "计划时间为空";
                    }
                    else
                    {
                        if (data[0].PlanDate >= Convert.ToDateTime(star) && data[0].PlanDate <= Convert.ToDateTime(end))
                        {
                            result.msg = "出仓";
                            result.success = true;
                        }
                        else
                        {
                            result.msg = "该时间段未找到追溯码";
                        }
                    }
                }
            }
            else
            {
                result.msg = "未找到追溯码信息";
            }

            return result;
        }


        #endregion

        #region 出仓

        /// <summary>
        /// 喉头出仓
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> OutWarehouse(ColdAWarehouseViewRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.TYPE == "三楼喉头仓")
                {
                    reqModel.EquipmentCode = "ThirdFloorThroatRoom";
                }
                else if (reqModel.TYPE == "四楼喉头仓")
                {
                    reqModel.EquipmentCode = "ForthFloorThroatRoom";
                }

                //默认查询的是当前的数据(查询两天之内的数据，防止夜班找不到数据)
                DateTime now = DateTime.Now;
                reqModel.StartTime = now.AddDays(-1).ToString("yyyy-MM-dd") + " 00:00:00";
                reqModel.EndTime = now.ToString("yyyy-MM-dd HH:mm:ss");

                var whereExpression = Expressionable.Create<ColdAWarehouseViewEntity>()
                      .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.MatModifydate >= Convert.ToDateTime(reqModel.StartTime))
                      .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.MatModifydate <= Convert.ToDateTime(reqModel.EndTime))
                      .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode.Contains(reqModel.EquipmentCode))
                      .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.Sscc.Contains(reqModel.SSCC))
                      .And(p => p.MatStatus == 0)//为出仓数据
                                 .ToExpression();
                var data = await _dalColdAWarehouseViewEntity.FindList(whereExpression);
                if (data == null || data.Count <= 0)
                {
                    result.msg = "该追溯码不存在出仓数据" + reqModel.SSCC;
                    return result;
                }

                //获取数据
                var modelHT = data[0];
                if (!string.IsNullOrEmpty(modelHT.BatchId) || !string.IsNullOrEmpty(modelHT.ProductionRequestId))
                {
                    result.msg = "该追溯码已经绑定工单" + reqModel.SSCC;
                    return result;
                }
                string inventId = modelHT.ID;
                //喉头ID
                string htId = modelHT.Workorderthroatid;
                decimal qty = modelHT.Inweight;
                //  int htState = modelHT.MatStatus; //（状态1是已出仓，0是未出仓）

                var inventData = await _InventorylistingViewEntityDal.FindEntity(p => p.ID == inventId);

                if (inventData == null)
                {
                    result.msg = "该追溯码不存在库存信息" + reqModel.SSCC;
                    return result;
                }

                var equipmentPG = await _equipmentEntity.FindEntity(p => p.EquipmentCode.Contains("MergePalletArea") || p.EquipmentCode.Contains("拼锅区域"));
                if (equipmentPG == null)
                {
                    result.msg = "该追溯码不存在拼锅区域" + reqModel.SSCC;
                    return result;
                }

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                //trans.OldStorageLocation = inventData.OldStorageLocation;
                //trans.NewStorageLocation = inventData.OldStorageLocation;
                trans.OldLotId = inventData.LotId;
                trans.NewLotId = inventData.LotId;
                trans.OldSublotId = inventData.SlotId;
                trans.NewSublotId = inventData.SlotId;
                trans.OldExpirationDate = inventData.ExpirationDate;
                trans.NewExpirationDate = inventData.ExpirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(qty), 3); //Convert.ToInt32(transfer.Quantity);
                trans.QuantityUomId = inventData.QuantityUomId;
                trans.ProductionExecutionId = inventData.ProductionRequestId;
                trans.Type = "Out Warehouse";
                trans.Comment = "喉头产出";
                trans.NewEquipmentRequirementId = inventData.ProductionRequestId;
                trans.OldEquipmentRequirementId = inventData.ProductionRequestId;
                //trans.TransferGroupId
                trans.OldEquipmentId = inventData.EquipmentId;
                trans.NewEquipmentId = equipmentPG.ID; //这里改成拼锅区
                //status
                trans.OldMaterialId = inventData.MaterialId;
                trans.NewMaterialId = inventData.MaterialId;
                trans.OldLotExternalStatus = inventData.StatusF;
                trans.OldSublotExternalStatus = inventData.StatusF;
                trans.NewLotExternalStatus = inventData.StatusFl;
                trans.NewSublotExternalStatus = inventData.StatusFl;
                //trans.PhysicalQuantity = transfer.PhysicalQuantity; //物理数量
                //trans.TareQuantity = transfer.TareQuantity;  //皮数量

                #endregion

                #region 修改库存信息
                var invent = await _MaterialInventoryEntityDal.FindEntity(p => p.ID == inventId);
                MaterialInventoryEntity mInvent = invent;
                mInvent.Modify(inventId, _user.Name);
                mInvent.EquipmentId = equipmentPG.ID;
                #endregion

                #region 修改喉头状态

                var htModel = await _dalWorkorderthroatEntity.FindEntity(p => p.ID == htId);
                WorkorderthroatEntity ht = htModel;
                ht.Modify(htModel.ID, _user.Name);
                ht.MatEquipmentcode = reqModel.EquipmentCode;
                ht.MatStatus = 1;

                #endregion

                _unitOfWork.BeginTran();

                //更新库存
                bool saveIninventory = await _dal.Update(mInvent);
                //新增记录
                bool tranResult = await _MaterialTransferEntityDal.Add(trans) > 0;
                bool upHt = await _dalWorkorderthroatEntity.Update(ht);

                if (!saveIninventory || !tranResult || !upHt)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "喉头出仓失败，追溯码:" + reqModel.SSCC;
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "喉头出仓成功";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "喉头出仓失败，原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }

        #endregion

        #region 撤回

        /// <summary>
        /// 喉头出仓
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ReturnWarehouse(ColdAWarehouseViewRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.TYPE == "三楼喉头仓")
                {
                    reqModel.EquipmentCode = "ThirdFloorThroatRoom";
                }
                else if (reqModel.TYPE == "四楼喉头仓")
                {
                    reqModel.EquipmentCode = "ForthFloorThroatRoom";
                }

                //默认查询的是当前的数据(查询两天之内的数据，防止夜班找不到数据)
                DateTime now = DateTime.Now;
                reqModel.StartTime = now.AddDays(-1).ToString("yyyy-MM-dd") + " 00:00:00";
                reqModel.EndTime = now.ToString("yyyy-MM-dd HH:mm:ss");

                string equpmentID = string.Empty;
                var equipmentPG = await _equipmentEntity.FindEntity(p => p.EquipmentCode.Contains("MergePalletArea") || p.EquipmentCode.Contains("拼锅区域"));
                if (equipmentPG != null)
                {
                    equpmentID = equipmentPG.ID;
                }

                var whereExpression = Expressionable.Create<ColdAWarehouseViewEntity>()
                      .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.MatModifydate >= Convert.ToDateTime(reqModel.StartTime))
                      .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.MatModifydate <= Convert.ToDateTime(reqModel.EndTime))
                      //.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode.Contains(reqModel.EquipmentCode))
                      .And(p => p.EquipmentCode.Contains("MergePalletArea"))
                      .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.Sscc.Contains(reqModel.SSCC))
                      .AndIF(!string.IsNullOrEmpty(equpmentID), a => a.EquipmentId.Contains(equpmentID))
                      .And(p => p.MatStatus == 1)//为出仓数据
                                 .ToExpression();
                var data = await _dalColdAWarehouseViewEntity.FindList(whereExpression);
                if (data == null || data.Count <= 0)
                {
                    result.msg = "该追溯码不存在撤回数据" + reqModel.SSCC;
                    return result;
                }

                //获取数据
                var modelHT = data[0];
                if (!string.IsNullOrEmpty(modelHT.BatchId) || !string.IsNullOrEmpty(modelHT.ProductionRequestId))
                {
                    result.msg = "该追溯码已经绑定工单" + reqModel.SSCC;
                    return result;
                }
                string inventId = modelHT.ID;
                //喉头ID
                string htId = modelHT.Workorderthroatid;
                decimal qty = modelHT.Inweight;
                //  int htState = modelHT.MatStatus; //（状态1是已出仓，0是未出仓）

                var inventData = await _InventorylistingViewEntityDal.FindEntity(p => p.ID == inventId);
                if (inventData == null)
                {
                    result.msg = "该追溯码不存在库存信息" + reqModel.SSCC;
                    return result;
                }

                //撤回需要查询对应的出仓记录
                var hisList = await _MaterialTransferEntityDal.FindList(p => p.OldSublotId == inventData.SlotId && p.Comment == "喉头产出");

                if (hisList == null || hisList.Count <= 0)
                {
                    result.msg = "该追溯码不存在喉头产出记录" + reqModel.SSCC;
                    return result;
                }

                var oldEqpID = hisList.FirstOrDefault().OldEquipmentId;

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(_user.Name.ToString());
                //trans.OldStorageLocation = inventData.OldStorageLocation;
                //trans.NewStorageLocation = inventData.OldStorageLocation;
                trans.OldLotId = inventData.LotId;
                trans.NewLotId = inventData.LotId;
                trans.OldSublotId = inventData.SlotId;
                trans.NewSublotId = inventData.SlotId;
                trans.OldExpirationDate = inventData.ExpirationDate;
                trans.NewExpirationDate = inventData.ExpirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(qty), 3); //Convert.ToInt32(transfer.Quantity);
                trans.QuantityUomId = inventData.QuantityUomId;
                trans.ProductionExecutionId = inventData.ProductionRequestId;
                trans.Type = "Return Warehouse";
                trans.Comment = "喉头撤回";
                trans.NewEquipmentRequirementId = inventData.ProductionRequestId;
                trans.OldEquipmentRequirementId = inventData.ProductionRequestId;
                //trans.TransferGroupId
                trans.OldEquipmentId = inventData.EquipmentId;
                trans.NewEquipmentId = oldEqpID; //这里改成拼锅区
                //status
                trans.OldMaterialId = inventData.MaterialId;
                trans.NewMaterialId = inventData.MaterialId;
                trans.OldLotExternalStatus = inventData.StatusF;
                trans.OldSublotExternalStatus = inventData.StatusF;
                trans.NewLotExternalStatus = inventData.StatusFl;
                trans.NewSublotExternalStatus = inventData.StatusFl;
                //trans.PhysicalQuantity = transfer.PhysicalQuantity; //物理数量
                //trans.TareQuantity = transfer.TareQuantity;  //皮数量

                #endregion

                #region 修改库存信息
                var invent = await _MaterialInventoryEntityDal.FindEntity(p => p.ID == inventId);
                MaterialInventoryEntity mInvent = invent;
                mInvent.Modify(inventId, _user.Name);
                mInvent.EquipmentId = oldEqpID;
                #endregion

                #region 修改喉头状态
                var htModel = await _dalWorkorderthroatEntity.FindEntity(p => p.ID == htId);
                WorkorderthroatEntity ht = htModel;
                ht.Modify(htModel.ID, _user.Name);
                ht.MatEquipmentcode = "";
                ht.MatStatus = 0;
                #endregion

                _unitOfWork.BeginTran();
                //更新库存
                bool saveIninventory = await _dal.Update(mInvent);
                //新增记录
                bool tranResult = await _MaterialTransferEntityDal.Add(trans) > 0;
                bool upHt = await _dalWorkorderthroatEntity.Update(ht);

                if (!saveIninventory || !tranResult || !upHt)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "喉头撤回失败，追溯码:" + reqModel.SSCC;
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "喉头撤回成功";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "喉头撤回失败，原因" + ex.Message + ex.StackTrace;
                return result;
            }
        }

        #endregion

        #endregion

    }
}