
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace SEFA.MKM.Services
{
    public class EquipmentServices : BaseServices<EquipmentEntity>, IEquipmentServices
    {
        private readonly IBaseRepository<EquipmentEntity> _dal;
        public EquipmentServices(IBaseRepository<EquipmentEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<EquipmentEntity>> GetList(EquipmentRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<EquipmentEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }
        /// <summary>
        /// 获取LEVEL='machine'的设备
        /// </summary>
        /// <returns></returns>
        public async Task<List<EquipmentEntity>> GetMachine()
        {
            var data = await _dal.FindList(x => x.Level == "machine");//machine
            return data;
        }
        public async Task<PageModel<EquipmentEntity>> GetPageList(EquipmentRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<EquipmentEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(EquipmentEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}