using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Model.Models;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.ViewModels;
using System.Reflection;
using System;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Services;
using SEFA.PPM.Model.ViewModels.MKM.PrintView;
using System.Drawing.Printing;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using static SEFA.PPM.Services.TippingMlistViewServices;
using System.Linq;
using System.Drawing;
using SEFA.Base.Common.LogHelper;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SEFA.Base.Common.Extensions;
using EquipmentEntity = SEFA.MKM.Model.Models.EquipmentEntity;

namespace SEFA.PPM.Services
{
    public class PrintSelectViewServices : BaseServices<PrintSelectViewEntity>, IPrintSelectViewServices
    {
        private readonly IBaseRepository<BatchConsumeRequirementEntity> _BatchConsumeRequirementEntitydal;

        private readonly IBaseRepository<PrintSelectViewEntity> _dal;

        //备料标签
        public IUser _uIser;
        private readonly IBaseRepository<InventorylistingViewEntity> _InventorylistingViewEntitydal;
        private readonly IBaseRepository<MaterialPreparationViewEntity> _MaterialPreparationViewEntitydal;
        private readonly IBaseRepository<PoProducedRequirementEntity> _PoProducedRequirementEntitydal;
        private readonly IBaseRepository<SEFA.PPM.Model.Models.UnitmanageEntity> _UnitmanageEntitydal;
        private readonly IBaseRepository<SEFA.PPM.Model.Models.BatchEntity> _BatchEntity;
        private readonly IBaseRepository<BatchProducedRequirementEntity> _BatchProducedRequirementEntitydal;
        private readonly IBaseRepository<MKM.Model.Models.EquipmentEntity> _dalEquipmentEntity;
        private readonly IBaseRepository<PoConsumeRequirementEntity> _dalPoConsumeRequirementEntity;

        private readonly IBaseRepository<DFM.Model.Models.UserinfoEntity> _dalUserinfoEntity;
        private readonly IBaseRepository<MaterialInventoryEntity> _dalMaterialInventoryEntity;

        private readonly IBaseRepository<ContainerEntity> _dalContainerEntity;

        public PrintSelectViewServices(IBaseRepository<PrintSelectViewEntity> dal, IUser user,
            IBaseRepository<InventorylistingViewEntity> inventorylistingViewEntitydal,
            IBaseRepository<MaterialPreparationViewEntity> materialPreparationViewEntitydal,
            IBaseRepository<PoProducedRequirementEntity> poProducedRequirementEntitydal,
            IBaseRepository<Model.Models.UnitmanageEntity> unitmanageEntitydal,
            IBaseRepository<Model.Models.BatchEntity> batchEntity,
            IBaseRepository<BatchProducedRequirementEntity> batchProducedRequirementEntitydal,
            IBaseRepository<MKM.Model.Models.EquipmentEntity> dalEquipmentEntity,
            IBaseRepository<PoConsumeRequirementEntity> dalPoConsumeRequirementEntity,
            IBaseRepository<MaterialInventoryEntity> dalMaterialInventoryEntity,
            IBaseRepository<UserinfoEntity> dalUserinfoEntity,
            IBaseRepository<BatchConsumeRequirementEntity> batchConsumeRequirementEntitydal,
            IBaseRepository<ContainerEntity> dalContainerEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _uIser = user;
            _InventorylistingViewEntitydal = inventorylistingViewEntitydal;
            _MaterialPreparationViewEntitydal = materialPreparationViewEntitydal;
            _PoProducedRequirementEntitydal = poProducedRequirementEntitydal;
            _UnitmanageEntitydal = unitmanageEntitydal;
            _BatchEntity = batchEntity;
            _BatchProducedRequirementEntitydal = batchProducedRequirementEntitydal;
            _dalEquipmentEntity = dalEquipmentEntity;
            _dalPoConsumeRequirementEntity = dalPoConsumeRequirementEntity;
            _dalMaterialInventoryEntity = dalMaterialInventoryEntity;
            _dalUserinfoEntity = dalUserinfoEntity;
            _BatchConsumeRequirementEntitydal = batchConsumeRequirementEntitydal;
            _dalContainerEntity = dalContainerEntity;
        }

        public async Task<List<PrintSelectViewEntity>> GetList(PrintSelectViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PrintSelectViewEntity>()
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<PrintSelectViewEntity>> GetPageList(PrintSelectViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PrintSelectViewEntity>()
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(PrintSelectViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        #region 公共类-打印

        #region 打印类

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="prinitID">模板ID</param>
        /// <param name="quipmentId">room</param>
        /// <param name="listobj">数据集合</param>
        /// <param name="dataList">数据表头用[0]</param>
        /// <param name="size">几张</param>
        /// <param name="teampType">PrintTemplete  库存标签打印 PrintBagTemplete  备料包库存标签模板   PrintPalletTemplete     备料托盘库存标签模板        
        /// <returns></returns>
        public async Task<MessageModel<string>> PrintCodeByEquipmentIdFactory(string prinitID, string templateID,
            string templateclassID, List<object> listobj, object dataList, int size)
        {
            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                //////PrintTemplete                   库存标签打印
                //////PrintBagTemplete                备料包库存标签模板
                //////PrintPalletTemplete             备料托盘库存标签模板
                //var models = await GetTeampID(quipmentId, teampType);
                //if (models == null)
                //{
                //    pResult.msg = "请配置打印机";
                //    return pResult;
                //}
                //string templateID = models.ID;
                //string templateclassID = models.TemplateClassId; ;

                Dictionary<string, object> dic = new Dictionary<string, object>();

                #region 获取列名

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数

                #region 打印机id

                #region 打印实体

                string token = _uIser.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion

                //获取数据
                DateTime nowTime = DateTime.Now;
                var printResult =
                    await HttpHelper.PostAsync<List<DFM.Model.Models.LabelPrinterEntity>>("DFM",
                        "api/LabelPrinter/GetList", token, model);
                SerilogServer.LogDebug($"获取打印机属性耗时:[{(DateTime.Now - nowTime).TotalSeconds}]", "获取打印机属性log");
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;

                    pritModel = pritModel.Where(p => p.ID == prinitID).ToList();
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].

                        #region 查询Temp数据源

                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, templateID, templateclassID, dic,
                            size, pSizeId, equID);
                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = printMsg;
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="prinitID">模板ID</param>
        /// <param name="quipmentId">room</param>
        /// <param name="listobj">数据集合</param>
        /// <param name="dataList">数据表头用[0]</param>
        /// <param name="size">几张</param>
        /// <param name="teampType">PrintTemplete  库存标签打印 PrintBagTemplete  备料包库存标签模板   PrintPalletTemplete     备料托盘库存标签模板        
        /// <returns></returns>
        public async Task<MessageModel<string>> PrintCodeByEquipmentId(string prinitID, string quipmentId,
            List<object> listobj, object dataList, int size, string teampType)
        {
            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                ////PrintTemplete                   库存标签打印
                ////PrintBagTemplete                备料包库存标签模板
                ////PrintPalletTemplete             备料托盘库存标签模板
                var models = await GetTeampID(quipmentId, teampType);
                // var models = await GetTeampID("02308281-5121-6559-163e-0370f6000000", teampType);
                if (models == null)
                {
                    pResult.msg = "请配置打印机";
                    return pResult;
                }

                string templateID = models.ID;
                string templateclassID = models.TemplateClassId;
                ;

                Dictionary<string, object> dic = new Dictionary<string, object>();

                #region 获取列名

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数

                #region 打印机id

                #region 打印实体

                string token = _uIser.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion

                DateTime nowTime = DateTime.Now;
                //获取数据
                var printResult =
                    await HttpHelper.PostAsync<List<DFM.Model.Models.LabelPrinterEntity>>("DFM",
                        "api/LabelPrinter/GetList", token, model);
                SerilogServer.LogDebug($"打印标签耗时:[{(DateTime.Now - nowTime).TotalSeconds}]", "打印标签耗时LOG");
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;

                    pritModel = pritModel.Where(p => p.ID == prinitID).ToList();
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].

                        #region 查询Temp数据源

                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, templateID, templateclassID, dic,
                            size, pSizeId, equID);
                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = printMsg;
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion
        }


        /// <summary>
        /// 获取指定列数据源
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static object GetValueByReflection(object obj, string propertyName)
        {
            // 使用反射获取属性值
            PropertyInfo property = obj.GetType().GetProperty(propertyName);
            return property?.GetValue(obj, null);
        }

        /// <summary>
        /// 获取列名
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<string> GetColumnNames<T>(List<T> list)
        {
            if (list.Count == 0)
                return new List<string>();
            ;
            PropertyInfo[] properties = list[0].GetType().GetProperties();
            List<string> columnNames = new List<string>();

            foreach (PropertyInfo property in properties)
            {
                columnNames.Add(property.Name);
            }

            return columnNames;
        }

        /// <summary>
        /// 根据设备ID拿对应属性值
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="propertyCode"></param>
        /// <returns></returns>
        public async Task<DFM.Model.Models.LabelTempleteEntity> GetTeampID(string equipmentId, string propertyCode)
        {
            DFM.Model.Models.LabelTempleteEntity model = new DFM.Model.Models.LabelTempleteEntity();
            try
            {
                var value = string.Empty;
                //获取allData
                MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys =
                    await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM",
                        "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _uIser.GetToken(),
                        new { EquipmentId = equipmentId });
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
                if (equFunctionPropertys?.Count > 0)
                {
                    foreach (var item in equFunctionPropertys)
                    {
                        var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
                        if (pr != null)
                        {
                            value = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
                        }

                        if (!string.IsNullOrEmpty(value))
                        {
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(value))
                {
                    //获取对应的模板ID
                    MessageModel<List<DFM.Model.Models.LabelTempleteEntity>> api_LabelTeamp =
                        await HttpHelper.PostAsync<List<DFM.Model.Models.LabelTempleteEntity>>("DFM",
                            "api/LabelTemplete/GetList", _uIser.GetToken(), new { Code = value });
                    var data_LabelTeamp = api_LabelTeamp.response;

                    if (data_LabelTeamp?.Count > 0)
                    {
                        string values = string.Empty;
                        for (int i = 0; i < data_LabelTeamp.Count; i++)
                        {
                            values = data_LabelTeamp[i].Code;
                            if (values == value)
                            {
                                model = data_LabelTeamp[i];
                                break;
                            }
                        }

                        // model = data_LabelTeamp[0];
                        return model;
                    }
                }

                return model;
            }
            catch
            {
                return model;
            }
        }

        /// <summary>
        /// 打印数据
        /// </summary>
        /// <param name="prinitID">打印id</param>
        /// <param name="teampID">模板Code</param>
        /// <param name="teampClassID">类型code</param>
        /// <param name="listobj">数据源</param>
        /// <param name="dataList">数据列</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> PrintByCode(string prinitID, string teampID, string teampClassID,
            List<object> listobj, object dataList)
        {
            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();

                #region 获取列名

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数

                #region 打印机id

                #region 打印实体

                string token = _uIser.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion

                //获取数据
                var printResult =
                    await HttpHelper.PostAsync<List<DFM.Model.Models.LabelPrinterEntity>>("DFM",
                        "api/LabelPrinter/GetList", token, model);
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].

                        #region 查询Temp数据源

                        //#region 实体

                        ////构造实体
                        //LabelTempleteRequestModel templeteRequestModel = new LabelTempleteRequestModel();
                        //templeteRequestModel.Code = pritModel[0].Code;
                        //templeteRequestModel.LabelSizeId = pSizeId;
                        //templeteRequestModel.PrinterClassId = pClassID;
                        //templeteRequestModel.pageIndex = 1;
                        //templeteRequestModel.pageSize = 1000;

                        //#endregion


                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, teampID, teampClassID, dic, 1,
                            pSizeId, equID);

                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = "创建库存成功，打印成功";
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = "创建库存成功" + printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="printerID">打印机ID</param>
        /// <param name="printerClass">打印机类ID</param>
        /// <param name="templateID">模板ID</param>
        /// <param name="printParam">打印参数</param>
        /// <param name="printNum">打印份数</param>
        /// <param name="labelSizeID">打印尺寸</param>
        /// <param name="eqmID">eqmID</param>
        /// <returns></returns>
        public async Task<string> CurrencyPrints(string printerID, string printerClass, string templateID,
            string templateClassID, Dictionary<string, object> printParam, int printNum, string labelSizeID,
            string eqmID)
        {
            #region 打印实体

            string token = _uIser.GetToken();
            LabelPrinterParamModel model = new LabelPrinterParamModel();
            model.EquipmentId = eqmID;

            model.PrinterId = printerID;
            model.PrinterClassId = printerClass;

            model.TemplateId = templateID;
            model.TemplateClassId = templateClassID;


            model.PrintNum = printNum;
            model.LabelSizeId = labelSizeID;
            model.PrintParameters = printParam;

            #endregion

            DateTime nowTime = DateTime.Now;
            var printResult = await HttpHelper.PostAsync<string>("DFM", "api/LabelPrint/Print", token, model);
            SerilogServer.LogDebug($"打印耗时:[{(DateTime.Now - nowTime).TotalSeconds}]", "打印log");
            if (printResult.success == true)
            {
                return "打印成功";
            }
            else
            {
                return "打印失败" + printResult.msg;
            }
        }

        #endregion

        #region 补打

        #region 备料小标签补打

        /// <summary>
        /// 根据库存ID获取对应的备料小标签（获取小标签数据源）
        /// </summary>
        /// <param name="inventID"></param>
        /// <returns></returns>
        public async Task<List<Object>> GetMinLabelData(string inventID)
        {
            List<Object> objList = new List<Object>();

            //拿库存视图
            var inventEntity = await _InventorylistingViewEntitydal.FindEntity(p => p.ID == inventID);
            if (inventEntity == null)
            {
                return objList;
            }

            //获取工单信息()
            string proID = inventEntity.ProductionRequestId;
            if (string.IsNullOrEmpty(proID))
            {
                #region 多查询一次容器工单防错

                //多查询一次当前容器对应的工单信息
                string conID = inventEntity.ContainerId;
                if (!string.IsNullOrEmpty(conID))
                {
                    var conEntity = await _dalContainerEntity.FindEntity(p => p.ID == conID);
                    if (conEntity != null)
                    {
                        proID = conEntity.ProductionRequestId;
                    }
                }

                #endregion
            }

            var proData = await _MaterialPreparationViewEntitydal.FindEntity(p => p.ID == proID);
            if (proData == null)
            {
                return objList;
            }

            string unitConvent = string.Empty;

            TeamplateMinLabel obj = new TeamplateMinLabel();
            obj.Material_Number = inventEntity.MaterialCode.ToString();
            obj.Material_Name = inventEntity.MaterialName; //.Replace('_', ' '); 
            obj.Weight = inventEntity.Quantity == null
                ? "0"
                : Math.Round(Convert.ToDecimal(inventEntity.Quantity.Value), 3)
                    .ToString(); //inventEntity.Quantity.Value.ToString();
            obj.Unit = inventEntity.MinUnit;

            //这里来查询下当前物料的单位信息来执行打印
            if (!string.IsNullOrEmpty(proID))
            {
                string mID = inventEntity.MaterialId;
                var bcList = await _dalPoConsumeRequirementEntity.FindList(p =>
                    p.ProductionOrderId == proID && p.MaterialId == mID && p.ChangeUnit == "g");
                if (bcList != null && bcList.Count > 0)
                {
                    string qty = inventEntity.Quantity == null
                        ? "0"
                        : Math.Round(Convert.ToDecimal(inventEntity.Quantity.Value), 6).ToString();
                    obj.Weight = Math.Round(Convert.ToDecimal(qty) * Convert.ToDecimal(1000), 3).ToString();
                    unitConvent = "g";
                    //     obj.Unit = "g";
                }
            }


            obj.Formula_Code = proData.FormulaNo;
            obj.Product_Name = proData.MaterinalName;
            obj.PO_Number = proData.ProOrder;
            obj.LineCode = proData.LineCode;
            obj.Order = proData.Sequence + "/" + proData.Sequencetotal;
            obj.OperatorName = await GetUserName(inventEntity.CreateUserId);
            //获取batchID
            var batchEntity = await _BatchEntity.FindEntity(P => P.ID == inventEntity.BatchId2);
            if (batchEntity != null)
            {
                obj.Tank_Order = batchEntity.Number;
            }
            else
            {
                obj.Tank_Order = "";
            }

            string pc = "(" + batchEntity.Number + "/";

            var countB = await _BatchEntity.FindList(p => p.ProductionOrderId == inventEntity.ProductionRequestId);
            pc += countB.Count + ")";
            //缸重
            decimal targetQuantity = batchEntity.TargetQuantity.Value;
            //obj.Weight = targetQuantity.ToString();
            obj.PO_Number += pc;
            obj.Total_Order = proData.PrepStatustotalcount.ToString();
            obj.Plan_Date = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
            // obj.OperatorName = _uIser.UserName;
            obj.SSCC = inventEntity.Sscc;

            //获取酱料数量和单位
            var poReData = await _PoProducedRequirementEntitydal.FindEntity(p => p.ProductionOrderId == proID);
            if (poReData == null)
            {
                objList.Add(obj);
                return objList;
            }

            //获取单位
            var unitData = await _UnitmanageEntitydal.FindEntity(p => p.ID == poReData.UnitId);
            if (unitData == null)
            {
                objList.Add(obj);
                return objList;
            }

            var batchReData = await _BatchProducedRequirementEntitydal.FindList(p =>
                p.PoProducedRequirementId == poReData.ID && p.BatchId == inventEntity.BatchId2);
            obj.Unit = unitData.Name;
            string tatalSum = batchReData.Sum(p => p.Quantity).ToString();
            tatalSum = targetQuantity.ToString();
            obj.SauceWeight = tatalSum + " " + unitData.Name; //酱料重量
            if (string.IsNullOrEmpty(obj.SauceWeight))
            {
                obj.SauceWeight = "";
            }

            if (unitConvent == "g")
            {
                obj.Unit = "g";
            }

            //缸重
            // obj.SauceWeight = targetQuantity.ToString();
            objList.Add(obj);

            return objList;
        }

        #endregion

        #region 备料标签补打

        /// <summary>
        /// 根据库存ID获取备料标签（获取小标签数据源）这里可能会有部分数据缺失
        /// </summary>
        /// <param name="inventID">库存ID</param>
        /// <param name="factoryName">表头使用功能组织(默认三厂)</param>
        /// <returns></returns>
        public async Task<List<object>> GetPrepareLableData(string inventID, string factoryName)
        {
            List<object> objList = new List<object>();

            //拿库存视图
            var inventEntity = await _InventorylistingViewEntitydal.FindEntity(p => p.ID == inventID);
            if (inventEntity == null)
            {
                return objList;
            }

            string uName = await GetUserName(inventEntity.CreateUserId);

            TeamplatePrepareLabel obj = new TeamplatePrepareLabel();

            obj.Plant = factoryName;
            obj.MaterialCode = inventEntity.MaterialCode;
            obj.Batch_Code = inventEntity.BatchId;
            obj.Material_Name = inventEntity.MaterialName; //.Replace('_', ' ');
            obj.Weight = inventEntity.Quantity == null
                ? "0"
                : Math.Round(Convert.ToDecimal(inventEntity.Quantity.Value), 3)
                    .ToString(); // inventEntity.Quantity.ToString();
            obj.Unit = inventEntity.MaxUnit;
            obj.Bucket_Number = inventEntity.Bucketnum; //桶号暂未给出
            obj.Remark = inventEntity.Remark;
            obj.Pallet_Number = inventEntity.SubLotId; //二维码
            obj.Operator = uName; //_uIser.UserName.ToString();
            obj.PONumber = inventEntity.ProductionOrderNo;
            //默认给当前
            //if (inventEntity.LocationFcode == "ProcessPlant")
            //{
            //    obj.PreparationDate = DateTime.Now.ToString("yyyy-MM-dd");
            //}
            obj.PlanDate = DateTime.Now.ToString("yyyy-MM-dd");

            //获取工单信息()
            string proID = inventEntity.ProductionRequestId;

            #region PO_COM

            var proConsume =
                await _dalPoConsumeRequirementEntity.FindList(p =>
                    p.ID == proID && p.MaterialId == inventEntity.MaterialId);

            if (proConsume != null && proConsume.Count > 0)
            {
                obj.Total_Weight = proConsume.Sum(p => p.Quantity.Value).ToString();
            }
            else
            {
                obj.Total_Weight = "0";
            }

            #endregion


            var proData = await _MaterialPreparationViewEntitydal.FindEntity(p => p.ID == proID);
            if (proData == null)
            {
                objList.Add(obj);
                return objList;
            }

            obj.Formula_Name = proData.MaterinalName;


            obj.Line_Code = proData.LineCode;
            obj.Formula_Code = proData.FormulaNo;

            //这里判断是否要显示对应的计划号
            if (inventEntity.LocationFcode == "ProcessPlant" || inventEntity.LocationFcode == "Plant4")
            {
                // obj.PreparationDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
                obj.PreparationDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
            }


            //获取酱料数量和单位
            var poReData = await _PoProducedRequirementEntitydal.FindEntity(p => p.ProductionOrderId == proID);
            if (poReData == null)
            {
                objList.Add(obj);
                return objList;
            }

            //获取单位
            var unitData = await _UnitmanageEntitydal.FindEntity(p => p.ID == poReData.UnitId);
            if (unitData == null)
            {
                objList.Add(obj);
                return objList;
            }

            var batchReData =
                await _BatchProducedRequirementEntitydal.FindEntity(p => p.PoProducedRequirementId == poReData.ID);
            //obj.Unit = unitData.Name;
            obj.Sauce_Weight = batchReData.Quantity.ToString(); //酱料重量
            objList.Add(obj);

            return objList;
        }


        /// <summary>
        /// 根据库存ID获取备料标签（获取小标签数据源）这里可能会有部分数据缺失
        /// </summary>
        /// <param name="inventID">库存ID</param>
        /// <param name="factoryName">表头使用功能组织(默认三厂)</param>
        /// <returns></returns>
        public async Task<List<object>> GetPrepareLableDataByTime(string inventID, string factoryName)
        {
            List<object> objList = new List<object>();

            //拿库存视图
            var inventEntity = await _InventorylistingViewEntitydal.FindEntity(p => p.ID == inventID);
            if (inventEntity == null)
            {
                return objList;
            }

            string uName = await GetUserName(inventEntity.CreateUserId);
            TeamplatePrepareLabel obj = new TeamplatePrepareLabel();
            obj.Plant = factoryName;
            obj.MaterialCode = inventEntity.MaterialCode;
            obj.Batch_Code = inventEntity.BatchId;
            obj.Material_Name = inventEntity.MaterialName;
            obj.Weight = inventEntity.Quantity == null
                ? "0"
                : Math.Round(Convert.ToDecimal(inventEntity.Quantity.Value), 3).ToString();
            obj.Unit = inventEntity.MaxUnit;
            obj.Bucket_Number = inventEntity.Bucketnum; //桶号暂未给出
            obj.Remark = inventEntity.Remark;
            obj.Pallet_Number = inventEntity.SubLotId; //二维码
            obj.Operator = uName; // _uIser.UserName.ToString();
            obj.PONumber = inventEntity.ProductionOrderNo;


            try
            {
                var invenResult = await _dalMaterialInventoryEntity.FindEntity(p => p.ID == inventID);
                if (invenResult != null)
                {
                    obj.PlanDate = invenResult.Createtime == null
                        ? DateTime.Now.ToString("yyyy-MM-dd")
                        : invenResult.Createtime.Value.ToString("yyyy-MM-dd");
                }

                //默认给当前
                // obj.PreparationDate = Convert.ToDateTime(CreateTime).ToString("yyyy-MM-dd");
            }
            catch
            {
                obj.PlanDate = DateTime.Now.ToString("yyyy-MM-dd");
            }


            //获取工单信息()
            string proID = inventEntity.ProductionRequestId;

            #region PO_COM

            var proConsume = await _dalPoConsumeRequirementEntity.FindList(p =>
                p.ProductionOrderId == proID && p.MaterialId == inventEntity.MaterialId);

            if (proConsume != null && proConsume.Count > 0)
            {
                obj.Total_Weight = proConsume.Sum(p => p.Quantity.Value).ToString();
            }
            else
            {
                obj.Total_Weight = "0";
            }

            #endregion


            var proData = await _MaterialPreparationViewEntitydal.FindEntity(p => p.ID == proID);
            if (proData == null)
            {
                objList.Add(obj);
                return objList;
            }
            else
            {
                //这里拿缸序
                string gx = proData.Sequence.ToString() + "/" + proData.Sequencetotal.ToString();
                obj.TankOrder = gx;
            }

            obj.Formula_Name = proData.MaterinalName;


            obj.Line_Code = proData.LineCode;
            obj.Formula_Code = proData.FormulaNo;

            //这里判断是否要显示对应的计划号
            if (inventEntity.LocationFcode == "ProcessPlant" || inventEntity.LocationFcode == "Plant4")
            {
                // obj.PreparationDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
                obj.PreparationDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
            }


            //if (inventEntity.LocationFcode == "ProcessPlant"||)
            //{
            //    obj.PreparationDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
            //}

            //获取酱料数量和单位
            var poReData = await _PoProducedRequirementEntitydal.FindEntity(p => p.ProductionOrderId == proID);
            if (poReData == null)
            {
                objList.Add(obj);
                return objList;
            }

            //获取单位
            var unitData = await _UnitmanageEntitydal.FindEntity(p => p.ID == poReData.UnitId);
            if (unitData == null)
            {
                objList.Add(obj);
                return objList;
            }

            var batchReData =
                await _BatchProducedRequirementEntitydal.FindEntity(p => p.PoProducedRequirementId == poReData.ID);
            //obj.Unit = unitData.Name;
            obj.Sauce_Weight = batchReData.Quantity.ToString(); //酱料重量
            objList.Add(obj);

            return objList;
        }

        #endregion

        #region 库存标签

        /// <summary>
        /// 获取登录名
        /// </summary>
        /// <param name="loginName"></param>
        /// <returns></returns>
        public async Task<string> GetUserName(string loginName)
        {
            var userInfo = await _dalUserinfoEntity.FindEntity(p => p.LoginName == loginName);
            if (userInfo == null)
            {
                return loginName;
            }
            else
            {
                return userInfo.UserName;
            }
        }


        public async Task<VerifiyDetailTotalData> GetInventLabel(string inventID, string factoryNam)
        {
            //查询视图
            var result = await _InventorylistingViewEntitydal.FindEntity(p => p.ID == inventID);
            if (result != null)
            {
                string supplier_Name = string.Empty;
                string material_Name = string.Empty;
                string material_Code = string.Empty;
                string lot_Code = string.Empty;
                string material_Inventory_Qty = string.Empty;
                string unit_Code = string.Empty;
                string sublot_Code = string.Empty;
                string fNumber = string.IsNullOrEmpty(result.Sapformula) ? "" : result.Sapformula;

                string uName = await GetUserName(result.CreateUserId);
                VerifiyDetailTotalData objs = new VerifiyDetailTotalData
                {
                    Plant = factoryNam,
                    Material_Name = result.MaterialName, //.Replace('_', ' '),
                    Material_Code = result.MaterialCode + " " + fNumber,
                    Batch_Code = result.BatchId,
                    NUM = result.Quantity == null
                        ? "0"
                        : Math.Round(Convert.ToDecimal(result.Quantity), 3).ToString(), // result.Quantity.ToString(),
                    PO_NUM = result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                    Unit = result.MinUnit,
                    Receive_Date = result.CreateDate.ToString("yyyy-MM-dd"),
                    SSCC = result.Sscc,
                    Remark = result.Remark,
                    Receiver = uName
                };
                return objs;
            }

            return new VerifiyDetailTotalData();
        }

        #endregion

        #endregion


        #region 这里拿打印机

        /// <summary>
        /// 根据设备和模板定位可选打印机器
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="propertyCode"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetPrintSelectList(string equipmentId, string propertyCode)
        {
            List<PrintSelectViewEntity> model = new List<PrintSelectViewEntity>();
            try
            {
                var result = await _dal.FindList(p =>
                    p.TCode.Contains(propertyCode) && p.EquipmentId.Contains(equipmentId));
                return result;
            }
            catch
            {
                return model;
            }
        }

        /// <summary>
        /// 根据设备ID拿对应属性值(这里是拿teampID)
        /// </summary>
        /// <param name="equipmentId">默认传入工厂的ID</param>
        /// <param name="propertyCode">工厂配置的属性（PrintTemplete/PrintBagTemplete/PrintPalletTemplete）</param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectDataList(string equipmentId, string propertyCode,
            string searcheID)
        {
            List<PrintSelectViewEntity> model = new List<PrintSelectViewEntity>();
            try
            {
                var value = string.Empty;

                SerilogServer.LogDebug($"调用DFM接口", "获取配置信息");
                //获取allData
                MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys =
                    await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM",
                        "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _uIser.GetToken(),
                        new { EquipmentId = equipmentId });
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;

                SerilogServer.LogDebug($"获取工厂配置[{equipmentId}][{equFunctionPropertys?.Count}]", "获取配置信息");
                if (equFunctionPropertys?.Count > 0)
                {
                    //LKK_PROCESSPLANT_LABEL
                    //LKK_PROCESSPLANT_LABEL
                    foreach (var item in equFunctionPropertys)
                    {
                        var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
                        if (pr != null)
                        {
                            value = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
                            SerilogServer.LogDebug($"获取工厂配置[{equipmentId}][{propertyCode}]值[{value}]", "获取配置信息");
                        }

                        if (!string.IsNullOrEmpty(value))
                        {
                            break;
                        }
                    }
                }


                //这里拿到的是视图的code
                if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(searcheID))
                {
                    var result = await _dal.FindList(p => p.TCode.Contains(value) && p.EquipmentId.Contains(searcheID));
                    SerilogServer.LogDebug($"查询设备ID[{searcheID}],返回数据[{result.Count}]条", "获取配置信息");
                    return result;
                }
                //这里拿到的是视图的code
                else if (!string.IsNullOrEmpty(value))
                {
                    var result = await _dal.FindList(p => p.TCode.Contains(value));
                    SerilogServer.LogDebug($"未查询设备,返回[{value}]数据[{result.Count}]条", "获取配置信息");
                    return result;
                }

                SerilogServer.LogDebug($"直接返回[{value}]数据[{model.Count}]条", "获取配置信息");
                return model.OrderBy(p => p.Code).ToList();
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug(ex.ToString(), "获取配置信息");
                return model;
            }
        }

        /// <summary>
        /// 替换原来获取的打印机的方法
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="propertyCode"></param>
        /// <param name="searcheID"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetPrinterList(string equipmentId, string propertyCode,
            string searcheID)
        {
            List<PrintSelectViewEntity> result = new List<PrintSelectViewEntity>();

            #region 获取当前用户所在的工厂ID

            var userEquipmentId = "";
            UserinfoEntity userInfo = await _dalUserinfoEntity.FindEntity(a => a.LoginName == _uIser.Name);
            if (userInfo == null || string.IsNullOrWhiteSpace(userInfo.Companyid))
            {
                userEquipmentId = "02308281-5121-6559-163e-0370f6000000";
            }

            var equipmentCode = userInfo?.Companyid;
            EquipmentEntity equipment = await _dalEquipmentEntity.FindEntity(a => a.EquipmentCode == equipmentCode);
            if (equipment == null || string.IsNullOrWhiteSpace(equipment.ID))
            {
                userEquipmentId = "02308281-5121-6559-163e-0370f6000000";
            }

            userEquipmentId = equipment?.ID;

            #endregion

            #region 根据用户所在工厂ID获取对应模板编码的标签模板编码

            var tempCodeList = new List<string>();
            tempCodeList = await _dalUserinfoEntity.Db
                .Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionPropertyEntity>((eq, ef, fp) =>
                    new object[]
                    {
                        JoinType.Inner, eq.ID == ef.EquipmentId,
                        JoinType.Inner, ef.FunctionId == fp.FunctionId
                    })
                .Where((eq, ef, fp) => eq.ID == userEquipmentId)
                .WhereIF(!string.IsNullOrWhiteSpace(propertyCode), (eq, ef, fp) => fp.PropertyCode == propertyCode)
                .Select((eq, ef, fp) => fp.DefaultValue).ToListAsync();

            #endregion

            if (tempCodeList.Count == 0)
            {
                return result;
            }

            #region 获取打印机信息

            result = await _dalEquipmentEntity.Db
                .Queryable<LabelEquipmentPrinterEntity, LabelTempleteEntity, LabelPrinterEntity>((lep, lt, lp) =>
                    new object[]
                    {
                        JoinType.Inner, lep.TemplateId == lt.ID,
                        JoinType.Inner, lep.PrinterId == lp.ID
                    })
                .WhereIF(!string.IsNullOrWhiteSpace(searcheID), (lep, lt, lp) => lep.EquipmentId == searcheID)
                .WhereIF(tempCodeList.Count > 0, (lep, lt, lp) => tempCodeList.Contains(lt.Code))
                .Select((lep, lt, lp) => new PrintSelectViewEntity()
                {
                    ID = lp.ID,
                    Code = lp.Code,
                    TemplateId = lt.ID,
                    TemplateClassId = lt.TemplateClassId,
                    TCode = lt.Code,
                    TDesc = lt.Description,
                    EquipmentId = lep.EquipmentId,
                    CreateDate = lp.CreateDate,
                    CreateUserId = lp.CreateUserId,
                    ModifyDate = lp.ModifyDate,
                    ModifyUserId = lp.ModifyUserId,
                    UpdateTimeStamp = lp.UpdateTimeStamp
                }).ToListAsync();

            #endregion

            return result;
        }


        /// <summary>
        /// 根据设备ID拿对应属性值(这里是拿teampID)
        /// </summary>
        /// <param name="equipmentId">默认传入工厂的ID</param>
        /// <param name="propertyCode">工厂配置的属性（PrintTemplete/PrintBagTemplete/PrintPalletTemplete）</param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectDataListByTwo(string equipmentId, string propertyCode,
            string searcheID)
        {
            List<PrintSelectViewEntity> model = new List<PrintSelectViewEntity>();
            try
            {
                var value = string.Empty;

                SerilogServer.LogDebug($"调用DFM接口", "获取配置信息");
                //获取allData
                MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys =
                    await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM",
                        "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _uIser.GetToken(),
                        new { EquipmentId = equipmentId });
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;

                SerilogServer.LogDebug($"获取工厂配置[{equipmentId}][{equFunctionPropertys?.Count}]", "获取配置信息");
                if (equFunctionPropertys?.Count > 0)
                {
                    //LKK_PROCESSPLANT_LABEL
                    //LKK_PROCESSPLANT_LABEL
                    foreach (var item in equFunctionPropertys)
                    {
                        var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
                        if (pr != null)
                        {
                            value = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
                            SerilogServer.LogDebug($"获取工厂配置[{equipmentId}][{propertyCode}]值[{value}]", "获取配置信息");
                        }

                        if (!string.IsNullOrEmpty(value))
                        {
                            break;
                        }
                    }
                }

                ////这里根据打印机名字来拆分数据

                //if (printName.Contains("移动"))
                //{
                //    if (value.Split(';').Length > 1)
                //    {
                //        value = value.Split(';')[0];
                //    }

                //}
                //else
                //{
                //    if (value.Split(';').Length > 1)
                //    {
                //        value = value.Split(';')[1];
                //    }
                //}


                //这里拿到的是视图的code
                if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(searcheID))
                {
                    var result = await _dal.FindList(p => p.TCode.Contains(value) && p.EquipmentId.Contains(searcheID));
                    SerilogServer.LogDebug($"查询设备ID[{searcheID}],返回数据[{result.Count}]条", "获取配置信息");
                    return result;
                }
                //这里拿到的是视图的code
                else if (!string.IsNullOrEmpty(value))
                {
                    var result = await _dal.FindList(p => p.TCode.Contains(value));
                    SerilogServer.LogDebug($"未查询设备,返回[{value}]数据[{result.Count}]条", "获取配置信息");
                    return result;
                }

                SerilogServer.LogDebug($"直接返回[{value}]数据[{model.Count}]条", "获取配置信息");
                return model.OrderBy(p => p.Code).ToList();
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug(ex.ToString(), "获取配置信息");
                return model;
            }
        }

        #endregion

        #region 拿打印机

        /// <summary>
        /// 库存打印机
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_Bag()
        {
            // string equipmentId = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintTemplete";

            var data = await GetPrinterList("", propertyCode, "");

            return data;
        }

        /// <summary>
        /// 获取下拉打印机-备料包库存标签模板/复称(小标签)
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_Move()
        {
            // string equipmentId = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintBagTemplete";

            var data = await GetPrinterList("", propertyCode, "");
            return data;
        }

        /// <summary>
        /// 获取下拉打印机-备料包库存标签模板/复称(小标签)
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetPrinit_MoveByEqumentID(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintBagTemplete";
            //var data = await GetPrintSelectList(equipmentId, propertyCode);
            var data = await GetPrinterList("", propertyCode, equipmentId);
            return data;
        }


        /// <summary>
        /// 获取下拉打印机-备料包库存标签模板/复称(小标签)
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetPrinit_MoveBagByEqumentID(string equipmentId)
        {
            string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintMoveBagTemplete";
            //var data = await GetPrintSelectList(equipmentId, propertyCode);
            var data = await GetSelectDataListByTwo(equipmentIds, propertyCode, equipmentId);
            return data;
        }


        /// <summary>
        /// 获取下拉打印机-托盘和拼锅最后一个
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_Pallet()
        {
            // string equipmentId = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintPalletTemplete";

            var data = await GetPrinterList("", propertyCode, "");

            return data;
        }


        /// <summary>
        /// 获取下拉打印机-托盘和拼锅最后一个
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_PG(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintPalletTemplete";

            var data = await GetPrinterList("", propertyCode,
                equipmentId); //GetPrintSelectList(equipmentId, propertyCode);   // 

            return data;
        }

        /// <summary>
        /// 库存打印机（）
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_KC(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintTemplete";

            var data = await GetPrinterList("", propertyCode,
                equipmentId); // GetPrintSelectList(equipmentId, propertyCode); //GetSelectDataList(equipmentId, propertyCode, equipmentId);

            return data;
        }

        /// <summary>
        /// 库存打印机（）
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_CY(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintProcessPlantTemplete";

            var data = await GetPrinterList("", propertyCode,
                equipmentId); // GetPrintSelectList(equipmentId, propertyCode); //GetSelectDataList(equipmentId, propertyCode, equipmentId);

            return data;
        }

        /// <summary>
        /// 库存打印机（）
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_SC(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintProcessPlantTemplete";

            var data = await GetPrinterList("", propertyCode,
                equipmentId); // GetPrintSelectList(equipmentId, propertyCode); //GetSelectDataList(equipmentId, propertyCode, equipmentId);

            return data;
        }

        /// <summary>
        /// 称量备料打印机
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_CLBL(string equipmentId)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "RemainingBagTemplate";

            var data = await GetPrinterList("", propertyCode,
                equipmentId); // GetPrintSelectList(equipmentId, propertyCode);// GetSelectDataList(equipmentId, propertyCode, equipmentId);

            return data;
        }


        /// <summary>
        /// 拼锅区域打印机
        /// </summary>
        /// <param name="equipmentId">拼锅区域ID</param>
        /// <param code="">扫描的打印机</param>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_CLBLPDA(string equipmentId, string code)
        {
            // string equipmentIds = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "RemainingBagTemplate";
            //    equipmentId = "02406281-0095-9185-163e-0370f6000000";
            var data = await GetSelectDataList("", propertyCode,
                equipmentId); // GetPrintSelectList(equipmentId, propertyCode);// GetSelectDataList(equipmentId, propertyCode, equipmentId);
            data = data.Where(p => p.Code.Contains(code) || p.ID.Contains(code)).ToList();
            if (data == null)
            {
                return new List<PrintSelectViewEntity>();
            }

            return data;
        }


        /// <summary>
        /// 获取下拉打印机-原料加工厂备料标签模板
        /// </summary>
        /// <returns></returns>
        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_Process()
        {
            // string equipmentId = "02308281-5121-6559-163e-0370f6000000";
            string propertyCode = "PrintProcessPlantTemplete";

            var data = await GetPrinterList("", propertyCode, "");

            return data;
        }

        public async Task<List<PrintSelectViewEntity>> GetSelectPrinit_ProcessFour(string equID)
        {
            string equipmentId = equID;
            //var entity = await _dalEquipmentEntity.FindEntity(p => p.EquipmentCode.Contains("Plant4"));
            //if (entity != null)
            //{
            //    equipmentId = entity.ID;
            //}


            string propertyCode = "PrintProcessPlantTemplete";
            var data = await GetPrinterList("", propertyCode, "");
            return data;
        }

        #endregion

        #endregion
    }
}