
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;

namespace SEFA.MKM.Services
{
    public class MaterialTransferListServices : BaseServices<MaterialTransferListEntity>, IMaterialTransferListServices
    {
        private readonly IBaseRepository<MaterialTransferListEntity> _dal;
        public MaterialTransferListServices(IBaseRepository<MaterialTransferListEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MaterialTransferListEntity>> GetList(MaterialTransferListRequestModel reqModel)
        {
            List<MaterialTransferListEntity> result = new List<MaterialTransferListEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialTransferListEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<MaterialTransferListEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<MaterialTransferListEntity>> GetPageList(MaterialTransferListRequestModel reqModel)
        {
            PageModel<MaterialTransferListEntity> result = new PageModel<MaterialTransferListEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialTransferListEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.EndTime))
                //原物料数据
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceMaterial), a => a.OldMaterialName.Contains(reqModel.SourceMaterial)
                                                   || a.OldMaterialCode.Contains(reqModel.SourceMaterial))
                 //新物料数据
                .AndIF(!string.IsNullOrEmpty(reqModel.DestinationMaterial), a => a.NewMaterialName.Contains(reqModel.DestinationMaterial)
                                                   || a.NewMaterialCode.Contains(reqModel.DestinationMaterial))
                 //老批次信息
                .AndIF(!string.IsNullOrEmpty(reqModel.OldBatch), a => a.OldLotId.Contains(reqModel.OldBatch))
                 //新批次信息
                .AndIF(!string.IsNullOrEmpty(reqModel.NewBatch), a => a.NewLotId.Contains(reqModel.NewBatch))
                 //老子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.OLD_SUB_LOT_ID), a => a.OldSubLotId.Contains(reqModel.OLD_SUB_LOT_ID))
                 //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.NEW_SUB_LOT_ID), a => a.NewSubLotId.Contains(reqModel.NEW_SUB_LOT_ID))
                           
                             .ToExpression();


            var data = await _dal.Db.Queryable<MaterialTransferListEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(MaterialTransferListEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}