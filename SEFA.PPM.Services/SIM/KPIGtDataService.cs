using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.Models.SIM;
using SEFA.PPM.Model.ViewModels.SIM;
using SEFA.PPM.Services.Helper;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{
    public partial class KpitgtServices
    {
        #region SIM通用方法
        /// <summary>
        /// 获取旭日图数据
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        public async Task<KpiSunburstModelValue> GetRisingSunPictureSeries(KpiValueRequestSingleModel reqmodel)
        {
            KpiSunburstModelValue result = new KpiSunburstModelValue()
            {
                LineCode = reqmodel.LineCode,
                KpiName = reqmodel.KPIName,
                KpiCode = reqmodel.KPIName,
                StartTime = reqmodel.StartTime,
                EndTime = reqmodel.EndTime,
                Data = new List<SunburstModel>()
            };
            try
            {
                switch (reqmodel.KPIName)
                {
                    case "订单产量状态":
                        result.Data = await GetOrderProductionStatus(reqmodel);
                        break;
                    default:
                        break;
                }
            }
            catch
            {

            }
            return result;
        }
        /// <summary>
        /// 获取KPI值
        /// </summary>
        /// <param name="reqModel">请求参数</param>
        /// <returns></returns>
        public async Task<MessageModel<List<KpiValueModel>>> GetKPIDataList(KpiValueRequestModel reqModel)
        {
            MessageModel<List<KpiValueModel>> result = new MessageModel<List<KpiValueModel>>();

            DateTime nowDate = DateTime.Now.Date;
            if (reqModel.StartTime.HasValue == false)
            {
                reqModel.StartTime = nowDate.AddDays(0 - nowDate.Day);
            }
            if (reqModel.EndTime.HasValue == false)
            {
                reqModel.EndTime = reqModel.StartTime.Value.AddMonths(1);
            }
            if (reqModel.KPIName.Count == 0)
            {
                result.success = false;
                result.msg = "请输入查询的KPI";
                return result;
            }
            else
            {
                List<KpiValueModel> list = new List<KpiValueModel>();
                foreach (var item in reqModel.KPIName)
                {
                    KpiValueModel kpi = await GetKpiData(new KpiValueRequestSingleModel { LineCode = reqModel.LineCode, StartTime = reqModel.StartTime, EndTime = reqModel.EndTime, KPIName = item });
                    list.Add(kpi);
                }
                result.response = list;
                result.success = true;
            }
            return result;
        }
        /// <summary>
        /// 获取单个KPI值
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns>返回结果</returns>
        public async Task<KpiValueModel> GetKpiData(KpiValueRequestSingleModel reqmodel)
        {
            KpiValueModel result = new KpiValueModel
            {
                LineCode = reqmodel.LineCode,
                KpiName = reqmodel.KPIName,
                KpiCode = reqmodel.KPIName,
                KpiValue = "0",
                KpiTarget = "0",
                KpiVS = "0",
                IsBigBetter = true,
                StartTime = reqmodel.StartTime,
                EndTime = reqmodel.EndTime
            };
            try
            {
                result.KpiTarget = await GetKPITarget(reqmodel);
                result.KpiValue = await GetKPIValue(reqmodel);
                var kpiValue = Convert.ToDecimal(result.KpiValue);
                var KpiTarget = Convert.ToDecimal(result.KpiTarget);
                if (kpiValue != 0)
                {
                    //实际-目标和除以目标
                    result.KpiVS = Math.Round(((kpiValue - KpiTarget) * 100 / KpiTarget), 1).ToString();

                    //目标-实际和除以目标
                    //   result.KpiVS = Math.Round(((KpiTarget - kpiValue) * 100 / KpiTarget), 1).ToString();
                }
                result.IsBigBetter = await GetKPIBetter(reqmodel.KPIName);
            }
            catch (Exception ex)
            {
                //result.KpiTarget = ex.Message;
            }
            return result;
        }


        /// <summary>
        /// 获取KPI数据序列
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        public async Task<KpiListValueModel> GetKPISeries(KpiValueRequestSingleModel reqmodel)
        {
            if (reqmodel.KPIName.Contains("各规格群组占比"))
            {

                try
                {
                    return await GetGroupSize(reqmodel);
                }
                catch (Exception)
                {

                    KpiListValueModel result1 = new KpiListValueModel()
                    {
                        LineCode = reqmodel.LineCode,
                        KpiName = reqmodel.KPIName,
                        KpiCode = reqmodel.KPIName,
                        IsBigBetter = true,
                        StartTime = reqmodel.StartTime,
                        EndTime = reqmodel.EndTime,
                        TimeSeries = new List<string>(),
                        DataSeries = new List<DataSeriesModel>()
                    };

                    return result1;
                }
            }


            KpiListValueModel result = new KpiListValueModel()
            {
                LineCode = reqmodel.LineCode,
                KpiName = reqmodel.KPIName,
                KpiCode = reqmodel.KPIName,
                IsBigBetter = true,
                StartTime = reqmodel.StartTime,
                EndTime = reqmodel.EndTime,
                TimeSeries = new List<string>(),
                DataSeries = new List<DataSeriesModel>()
            };
            try
            {
                result.TimeSeries = await GetKPITimeSeries(reqmodel);
                if (reqmodel.KPIName == "周度产线使用率" && result.TimeSeries.Count < 8)
                {
                    reqmodel.StartTime = reqmodel.EndTime.Value.Date.AddDays(-50);
                    result.TimeSeries = await GetKPITimeSeries(reqmodel);
                }
                result.DataSeries = await GetKPIDataSeries(reqmodel);
                var series = result.DataSeries.Where(p => p.XSeries != null && p.XSeries.Count > 0).FirstOrDefault();
                if (series != null)
                {
                    result.TimeSeries = series.XSeries;
                }
                result.IsBigBetter = await GetKPIBetter(reqmodel.KPIName);
            }
            catch
            {

            }
            return result;
        }
        /// <summary>
        /// 获取数据序列
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<List<DataSeriesModel>> GetKPIDataSeries(KpiValueRequestSingleModel reqmodel)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            switch (reqmodel.KPIName)
            {
                case "生产力(产量/人时)":
                    list = await GetProductLabourSeries(reqmodel);
                    break;
                case "生产力":
                    list = await GetProductLabourSeries(reqmodel);
                    break;
                case "生产力（产量/人时）":
                    list = await GetProductLabourSeries(reqmodel);
                    break;
                case "产量同期对比（吨）":
                case "产量同期对比(吨)":
                case "产量同期对比":
                    list = await GetProductCompareSeries(reqmodel);
                    break;
                case "top6包装车间故障累计时间生产线/小时":
                case "TOP6包装车间故障累计时间生产线/小时":
                case "TOP6包装车间故障累计时间":
                case "TOP6设备故障":
                case "top6设备故障":
                    list = await GetEqFaultSeries(reqmodel);
                    break;
                case "产品类别(TOP6%)":
                case "TOP6产品产量排名":
                    list = await GetProductTop6Series(reqmodel);
                    break;
                case "产品类别":
                    list = await GetProductTop6Series(reqmodel, 3);
                    break;
                case "产品类别(TOP3%)":
                    list = await GetProductTop6Series(reqmodel, 3);
                    break;

                case "生产成本信息":
                    list = await GetProductionCostsMsg(reqmodel);
                    break;
                case "周度产线使用率":
                    list = await GetLineUsageRate(reqmodel);
                    break;
                default:
                    list.Add(new DataSeriesModel
                    {
                        Name = "目标",
                        Data = new List<decimal> { 1, 2, 3, 1, 4 }
                    });
                    list.Add(new DataSeriesModel
                    {
                        Name = "实际",
                        Data = new List<decimal> { 2, 2, 3, 1, 4 }
                    });
                    list.Add(new DataSeriesModel
                    {
                        Name = "预算",
                        Data = new List<decimal> { 3, 2, 3, 1, 4 }
                    });
                    list.Add(new DataSeriesModel
                    {
                        Name = "VS",
                        Data = new List<decimal> { 4, 2, 3, 1, 4 }
                    });
                    break;
            }
            return list;
        }

        /// <summary>
        /// 获取时间序列
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<List<string>> GetKPITimeSeries(KpiValueRequestSingleModel reqmodel)
        {
            List<string> ss = new List<string>();
            switch (reqmodel.KPIName)
            {
                case "产品类别":
                case "产品类别(TOP3%)":
                case "TOP6产品产量排名":
                    ss = new List<string>() { "酱料", "蚝油", "茄汁" };
                    break;
                case "生产成本信息":
                    ss = new List<string>() { "自然月人工费用", "自然月消耗品", "自然月维修费用", "自然月动力耗用费用", "自然月折旧费用", "自然月其它费用" };
                    break;
                case "设备管理信息":
                case "各规格群组占比":
                    break;
                case "top6包装车间故障累计时间生产线/小时":
                case "TOP6包装车间故障累计时间生产线/小时":
                case "TOP6包装车间故障累计时间":
                case "TOP6设备故障":
                case "top6设备故障":
                    ss = new List<string> { "L1", "L2", "L3", "L4", "L5", "L6" };
                    break;
                default:
                    var weekList = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
                    foreach (var item in weekList)
                    {
                        ss.Add($"第{item.WeekIndex}周");
                    }
                    break;
            }
            return ss;
        }

        /// <summary>
        /// 获取KPI是否是越大越好
        /// </summary>
        /// <param name="kPIName">KPI名称</param>
        /// <returns></returns>
        private async Task<bool> GetKPIBetter(string kPIName)
        {
            var isBetter = true;
            switch (kPIName)
            {
                case "用水单耗":
                case "用电单耗":
                case "蒸汽单耗":
                case "原料损耗率":
                case "包材损耗率":
                case "确认投诉数":
                case "累计工伤数":
                case "安全事故数量":
                case "TOP6设备故障":
                    isBetter = false;
                    break;
                default:
                    isBetter = true;
                    break;
            }
            return isBetter;
        }
        /// <summary>
        /// 获取KPI值是目标值
        /// </summary>
        /// <param name="lineCode"></param>
        /// <param name="kPIName"></param>
        /// <returns></returns>
        private async Task<string> GetKPIValue(KpiValueRequestSingleModel reqmodel)
        {
            var ActualCount = "0";
            switch (reqmodel.KPIName)
            {
                case "确认投诉数"://待确定KPI名后再更换20241223
                    ActualCount = Convert.ToString(await SafeMessage(reqmodel, "TS"));
                    break;
                case "累计工伤数"://待确定KPI名后再更换20241223
                    ActualCount = Convert.ToString(await SafeMessage(reqmodel, "GS"));
                    break;
                case "煮缸调节率":
                    ActualCount = Convert.ToString(await CookTanlAdjust(reqmodel));
                    break;
                case "产出率":
                    ActualCount = Convert.ToString(await GetYield(reqmodel));
                    break;
                case "用水单耗":
                    ActualCount = Convert.ToString(await WaterConsumption(reqmodel));
                    break;
                case "用电单耗":
                    ActualCount = Convert.ToString(await ElectricityConsumption(reqmodel));
                    break;
                case "蒸汽单耗":
                    ActualCount = Convert.ToString(await SteamConsumption(reqmodel));
                    break;
                case "OEE":
                    ActualCount = Convert.ToString(await GetOEEDate(reqmodel));
                    break;
                case "表现性":
                    ActualCount = Convert.ToString(await GetOEEDate(reqmodel));
                    break;
                case "有效性":
                    ActualCount = Convert.ToString(await GetOEEDate(reqmodel));
                    break;
                case "品质性":
                    ActualCount = Convert.ToString(await GetOEEDate(reqmodel));
                    break;
                case "待料时间":
                    ActualCount = Convert.ToString(await WaitTime(reqmodel));
                    break;
                case "当前全厂使用率":
                case "实际产能利用率":
                    ActualCount = Convert.ToString(await UsageRate(reqmodel));
                    break;
                case "整线直通率":
                    ActualCount = Convert.ToString(await LineDirectRate(reqmodel));
                    break;
                case "工单一次性完成率":
                    ActualCount = Convert.ToString(await PoCompletioRate(reqmodel));
                    break;
                case "本月产量计划达成率":
                case "产量计划达成率":
                case "生产计划达成率":
                    ActualCount = await GetPlanFinishRate(reqmodel);
                    break;
                case "当月订单转换率":
                    ActualCount = await GetPlanSlewRate(reqmodel);
                    break;
                case "订单转换率":
                    ActualCount = await GetPlanSlewRate(reqmodel);
                    break;
                case "订单转化率":
                    ActualCount = await GetPlanSlewRate(reqmodel);
                    break;
                case "计划转化率":
                    ActualCount = await GetPlanSlewRate(reqmodel);
                    break;
                case "计划转换率":
                    ActualCount = await GetPlanSlewRate(reqmodel);
                    break;
                case "厂房综合表现性":
                    ActualCount = await GetPlatePerformance(reqmodel);
                    break;
                case "订单满足率":
                    ActualCount = await GetPlanFullFillRate(reqmodel);
                    break;
                case "平均维修时间":
                    ActualCount = await GetRepairAvgTime(reqmodel);
                    break;
                case "包材损耗率":
                    ActualCount = await GetPackLossRate(reqmodel);
                    break;
                case "全厂产能":
                    ActualCount = await GetPlantCapacity(reqmodel);
                    break;
                case "本日产量计划达成率":
                    ActualCount = await GetDayPlanFinishRate();
                    break;
                case "年累计产量":
                    ActualCount = await GetYearProduction(reqmodel);
                    break;
                case "原料综合损耗率":
                case "原料损耗率":
                    ActualCount = await GetMtrLossRate(reqmodel);
                    break;
                case "当日生产工单进度":
                    ActualCount = await ProductionSchedule(reqmodel);
                    break;
                case "喉头产出率":
                    ActualCount = Convert.ToString(await GetThroatRate(reqmodel));
                    break;
                case "维修及时率":
                    ActualCount = await GetRepairTimeRate(reqmodel);
                    break;
                default:
                    break;
            }

            return ActualCount;
        }
        /// <summary>
        /// 计算原料损耗率
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<string> GetMtrLossRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            try
            {
                var endTime = reqmodel.EndTime.Value.Date.AddDays(1);
                var orderList = await _dal.Db.Queryable<CookConfirmationEntity, ProductionOrderEntity>(
                    (c, p) => new object[]
                    {
                       JoinType.Inner , c.OrderId == p.ID
                    })
                    .Where((c, p) => /*p.PoStatus == "3" &&*/ p.SapOrderType == "ZXH2"
                                     && p.PlanDate >= reqmodel.StartTime.Value.Date
                                     && p.PlanDate < endTime)
                    .Select((c, p) => new
                    {
                        p.ID
                    })
                    .Distinct()
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var ids = orderList.Select(x => x.ID).Distinct().ToList();

                    var mtrId = await _dal.Db.Queryable<MaterialEntity>()
                        .Where(x => x.Type == "ZRAW" && x.Code != "7300030001")
                        .Select(x => x.ID)
                        .ToListAsync();
                    var actualBom = await _dal.Db.Queryable<PoConsumeActualEntity>()
                         .Where(p => ids.Contains(p.ProductionOrderId) && p.Quantity != null)
                         .Select(p => new
                         {
                             p.ID,
                             p.PoConsumeRequirementId,
                             p.Quantity
                         })
                         .ToListAsync();
                    if (actualBom.Count > 0)
                    {
                        var standradBom = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                            .Where(p => ids.Contains(p.ProductionOrderId) && p.Quantity != null && mtrId.Contains(p.MaterialId))
                             .Select(p => new
                             {
                                 p.ID,
                                 p.Quantity
                             })
                           .ToListAsync();
                        var sQty = standradBom.Sum(p => p.Quantity);
                        if (sQty > 0)
                        {
                            var standardids = standradBom.Select(p => p.ID).ToList();
                            var aQty = actualBom.Where(p => standardids.Contains(p.PoConsumeRequirementId)).Sum(p => p.Quantity);
                            var data = Math.Round((aQty.Value - sQty.Value) * 100.0m / sQty.Value, 1);
                            value = data.ToString();
                        }
                    }
                }
            }
            catch
            {
                value = "-1";
            }
            return value;
        }

        /// <summary>
        /// 获取KPI目标值
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<string> GetKPITarget(KpiValueRequestSingleModel reqmodel, string type = "预算")
        {
            var yearS = reqmodel.StartTime.Value.Year;
            var months = reqmodel.StartTime.Value.Month;
            var ss = yearS * 100 + months;
            var yearE = reqmodel.EndTime.Value.Year;
            var monthE = reqmodel.EndTime.Value.Month;
            var ee = yearE * 100 + monthE;
            var area = (await _eq.FindList(p => p.ID == reqmodel.LineCode || p.EquipmentCode == reqmodel.LineCode)).FirstOrDefault();
            var value = "0";
            if (area != null)
            {
                var tagList = await _dal.FindList(p => p.ModelRef == area.ID && p.DataName == reqmodel.KPIName
                 && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                if (tagList.Count == 0 )
                {
                    if (reqmodel.KPIName.Contains("年累计产量"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == reqmodel.KPIName
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    else if (reqmodel.KPIName.Contains("计划达成率"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == "生产计划达成率"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    else if( reqmodel.KPIName.Contains("转化率") || reqmodel.KPIName.Contains("转换率"))  
                    {
                        tagList = await _dal.FindList(p => p.DataName == "订单转化率"
                       && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    else if(reqmodel.KPIName.Contains("包材"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == "包材综合损耗"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    else if (reqmodel.KPIName.Contains("原料"))
                    {
                        tagList = await _dal.FindList(p =>  p.DataName == "原料综合损耗"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }  
                    else if (reqmodel.KPIName.Contains("厂房综合表现性"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == "厂房综合表现性"
                       && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    else if (reqmodel.KPIName.Contains("喉头产出率"))
                    {
                        tagList = await _dal.FindList(p => p.ModelRef == area.ID && p.DataName == "制造喉头产出率"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                        if(tagList.Count  == 0)
                        {
                            tagList = await _dal.FindList(p =>  p.DataName == "制造喉头产出率"
                            && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                        }
                    }
                }

                if (tagList.Count > 0)
                {
                    if (reqmodel.KPIName.Contains("年累计产量"))
                    {
                        //跨月
                        if (ss != ee)
                        {
                            var monthend1 = TimeDurationHelper.GetMonthLastDay(reqmodel.StartTime.Value);
                            var monthQty1 = monthend1.Day;
                            var monthQty2 = TimeDurationHelper.GetMonthLastDay(reqmodel.EndTime.Value).Day;
                            var dayQty1 = (monthend1.AddDays(1) - reqmodel.StartTime.Value).Days;
                            var dayQty2 = reqmodel.EndTime.Value.Day;
                            var tgt = tagList.Where(p => (p.Year * 100 + p.Month) == ss).Sum(p => p.Tgt) / monthQty1 * dayQty1
                                  + tagList.Where(p => (p.Year * 100 + p.Month) == ee).Sum(p => p.Tgt) / monthQty2 * dayQty2
                                  + tagList.Where(p => (p.Year * 100 + p.Month) > ss && (p.Year * 100 + p.Month) < ee).Sum(p => p.Tgt);
                            value = Math.Round(tgt, 2).ToString();
                        }
                        else
                        {
                            var monthQty = TimeDurationHelper.GetMonthLastDay(reqmodel.StartTime.Value).Day;
                            var dayQty = (reqmodel.EndTime.Value.AddDays(1) - reqmodel.StartTime.Value).Days;
                            var tgt = tagList.Sum(p => p.Tgt) / monthQty * dayQty;
                            value = Math.Round(tgt, 2).ToString();
                        }
                    }
                    else
                    {
                        value = Math.Round(tagList.Sum(p => p.Tgt) / tagList.Count, 2).ToString();
                    }
                }
            }

            return value;
        }
        /// <summary>
        /// 获取目标值
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <param name="type">类型</param>
        /// <returns></returns>
        private async Task<List<decimal>> GetKpiTargetList(KpiValueRequestSingleModel reqmodel, string type = "预算")
        {
            var weekList = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
            var area = (await _eq.FindList(p => p.ID == reqmodel.LineCode || p.EquipmentCode == reqmodel.LineCode)).FirstOrDefault();
            List<decimal> data = new List<decimal>();
            foreach (var week in weekList)
            {
                var yearS = week.WeekStart.Year;
                var months = week.WeekStart.Month;
                var ss = yearS * 100 + months;
                var yearE = week.WeekEnd.Year;
                var monthE = week.WeekEnd.Month;
                var ee = yearE * 100 + monthE;
                var value = 0m;
                if (area != null)
                {
                    var tagList = await _dal.FindList(p => p.ModelRef == area.ID && p.DataName == reqmodel.KPIName
                     && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    if (reqmodel.KPIName.Contains("产量同期对比"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == "年累计产量"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    if (reqmodel.KPIName.Contains("生产力"))
                    {
                        tagList = await _dal.FindList(p => p.DataName == "生产力"
                        && p.DateType == type && (p.Year * 100 + p.Month) >= ss && (p.Year * 100 + p.Month) <= ee);
                    }
                    if (tagList.Count > 0)
                    {
                        if (reqmodel.KPIName.Contains("产量同期对比"))
                        {
                            //跨月
                            if (ss != ee)
                            {
                                var monthend = TimeDurationHelper.GetMonthLastDay(week.WeekStart);
                                var monthQty1 = monthend.Day;
                                var monthQty2 = TimeDurationHelper.GetMonthLastDay(week.WeekEnd).Day;
                                var dayQty1 = (monthend.AddDays(1) - week.WeekStart).Days;
                                var dayQty2 = (week.WeekEnd-monthend).Days;
                                value = tagList.Where(p => (p.Year * 100 + p.Month) == ss).Sum(p => p.Tgt) / monthQty1 * dayQty1
                                      + tagList.Where(p => (p.Year * 100 + p.Month) == ee).Sum(p => p.Tgt) / monthQty2 * dayQty2;
                            }
                            else
                            {
                                var monthQty = TimeDurationHelper.GetMonthLastDay(week.WeekStart).Day;
                                var dayQty = (week.WeekEnd.AddDays(1) - week.WeekStart).Days;
                                value = tagList.Sum(p => p.Tgt) / monthQty * dayQty;
                            }
                        }
                        else
                        {
                            value = Math.Round(tagList.Sum(p => p.Tgt) / tagList.Count, 2);
                        }
                    }
                }
                data.Add(value);
            }

            return data;
        }

        #endregion
        #region 获取KPI数据
        /// <summary>
        /// 获取生产力（人/时）
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<List<DataSeriesModel>> GetProductLabourSeries(KpiValueRequestSingleModel reqmodel)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            try
            {
                DataSeriesModel target = new DataSeriesModel()
                {
                    Name = "目标",
                    Data = await GetKpiTargetList(reqmodel)
                };

                var weekList = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
                List<decimal> aData = new List<decimal>();
                List<decimal> hData = new List<decimal>();
                List<decimal> pData = new List<decimal>();
                foreach (var week in weekList)
                {
                    var start = week.WeekStart >= reqmodel.StartTime.Value.Date ? week.WeekStart : reqmodel.StartTime.Value.Date;
                    var end = week.WeekEnd <= reqmodel.EndTime.Value.Date ? week.WeekEnd : reqmodel.EndTime.Value.Date;
                    var p = await GetCompUsingQty(start, end);
                    var h = await GetHrWorkHour(start, end);
                    var a = h > 0 ? p / h : p / 700;
                    aData.Add(Math.Round(a, 0));
                    hData.Add(h);
                    pData.Add(p);
                }

                DataSeriesModel actual = new DataSeriesModel()
                {
                    Name = "实际",
                    Data = aData
                };

                DataSeriesModel hr = new DataSeriesModel()
                {
                    Name = "人时",
                    Data = hData
                };

                DataSeriesModel pd = new DataSeriesModel()
                {
                    Name = "产量",
                    Data = pData
                };
                list.Add(target);
                list.Add(actual);
                list.Add(hr);
                list.Add(pd);
            }
            catch
            {

            }
            return list;
        }

        /// <summary>
        /// 获取TOP6设备故障
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<List<DataSeriesModel>> GetEqFaultSeries(KpiValueRequestSingleModel reqmodel)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            if (reqmodel.LineCode == "CookingArea")
            {
                reqmodel.LineCode = "PackingArea";
            }
            var root = (await _eq.FindList(p => p.ID == reqmodel.LineCode || p.EquipmentCode == reqmodel.LineCode)).FirstOrDefault();
            if (root == null)
            {
                list.Add(new DataSeriesModel
                {
                    Name = "故障时间",
                    Data = new List<decimal>(),
                    XSeries = new List<string>()
                });
            }
            else
            {
                var eqIds = await GetLinesEqList(root.ID);
                var reasonIds = await GetDownTimeReason("设备故障");
                var endtime = reqmodel.EndTime.Value.Date.AddDays(1);
                var downTimes = await _dal.Db.Queryable<PerformanceEntity>()
                              .Where(p => reasonIds.Contains(p.ReasonId)
                              && p.Categroy == "非计划停机"//增加筛选非计划停机条件
                              && ((p.StartTimeUtc >= reqmodel.StartTime.Value && p.EndTimeUtc != null && p.EndTimeUtc <= endtime) ||
                              (p.EndTimeUtc == null && p.StartTimeUtc <= endtime))
                              )
                              .Select(p => new
                              {
                                  p.EquipmentId,
                                  totals = p.TimeDifferenceInSeconds
                              })
                              .ToListAsync();
                List<KeyValuePair<string, decimal>> kvList = new List<KeyValuePair<string, decimal>>();

                if (eqIds != null && downTimes != null)
                {
                    // 提前对 downTimes 进行分组，减少每次循环中的计算量
                    var groupedDownTimes = downTimes
                        .Where(p => p.EquipmentId != null)
                        .GroupBy(p => p.EquipmentId)
                        .ToDictionary(g => g.Key, g => (long)g.Sum(p => p.totals)); // 使用 long 类型

                    foreach (var d in eqIds)
                    {
                        if (d.Value == null)
                        {
                            continue;
                        }

                        long timeItem = 0;
                        foreach (var equipmentId in d.Value)
                        {
                            if (groupedDownTimes.TryGetValue(equipmentId, out var totalTime))
                            {
                                timeItem += totalTime;
                            }
                        }

                        if (timeItem > 0)
                        {
                            kvList.Add(new KeyValuePair<string, decimal>(d.Key, Math.Round(timeItem / 3600m, 2)));
                        }
                    }
                }
                var result = kvList.OrderByDescending(p => p.Value).Take(6).ToList();
                List<string> xSeries = new List<string>();
                List<decimal> data = new List<decimal>();

                foreach (var r in result)
                {
                    var line = await _eq.FindEntity(r.Key);
                    xSeries.Add(line?.EquipmentCode);
                    data.Add(r.Value);
                }

                list.Add(new DataSeriesModel
                {
                    Name = "故障时间",
                    Data = data,
                    XSeries = xSeries
                });
            }
            return list;
        }

        /// <summary>
        /// 获取产线下属设备列表
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, List<string>>> GetLinesEqList(string pid)
        {
            var childLines = await GetEqIdsByParentId(pid, "Line");
            Dictionary<string, List<string>> dc = new Dictionary<string, List<string>>();
            foreach (var c in childLines)
            {
                var childEq = await GetEqIdsByParentId(c, "Unit");
                if (childEq.Count > 0)
                {
                    dc.Add(c, childEq);
                }
            }
            return dc;
        }
        /// <summary>
        /// 递归获取下级设备ID
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        private async Task<List<string>> GetEqIdsByParentId(string pid, string level)
        {
            List<string> ids = new List<string>();
            var child = await _eq.FindList(p => p.ParentId == pid);
            var eqs = child.Where(p => p.Level == level).Select(p => p.ID);
            if (eqs.Count() > 0)
            {
                ids.AddRange(eqs);
            }

            foreach (var c in child.Where(p => p.Level != level))
            {
                var clist = await GetEqIdsByParentId(c.ID, level);
                if (clist.Count > 0)
                {
                    ids.AddRange(clist);
                }
            }
            return ids;
        }

        /// <summary>
        /// 获取停机原因
        /// </summary>
        /// <param name="groupName"></param>
        /// <returns></returns>
        private async Task<List<string>> GetDownTimeReason(string groupName)
        {
            List<string> reasonId = new List<string>();
            var group = await _dal.Db.Queryable<DowntimeGroupEntity>().Where(p => p.Description == groupName && p.Status == "Active").Select(p => p.ID).ToListAsync();
            var reasons = await _dal.Db.Queryable<DowntimeReasonEntity>().Where(p => group.Contains(p.GroupId) && p.Status == "Active").Select(p => p.ID).ToListAsync();
            if (reasons.Count > 0)
            {
                reasonId.AddRange(reasons);
            }
            var childGroup = await _dal.Db.Queryable<DowntimeGroupEntity>().Where(p => group.Contains(p.ParentGroupId) && p.Status == "Active").ToListAsync();
            if (childGroup.Count > 0)
            {
                foreach (var item in childGroup)
                {
                    var childReasons = await GetDownTimeReason(item.Description);
                    if (childReasons.Count > 0)
                    {
                        reasonId.AddRange(childReasons);
                    }
                }
            }
            return reasonId;
        }
        /// <summary>
        /// 生产计划达成率
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<string> GetPlanFinishRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21" };
            try
            {
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => /*p.PoStatus == "3" c.Status == 1 &&  */ s.Auart == "ZXH1"
                                     && mrpString.Contains(s.Dispo)
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        c.OrderId,
                        c.GoodCount,
                        s.Psmng
                    })
                    .ToListAsync();
                if (orderList.Count > 0)
                {
                    var a = orderList.Sum(p => p.GoodCount);
                    var t = orderList.Sum(p => p.Psmng);
                    var rate = Math.Round(a * 100 / t, 0);
                    value = rate.ToString();
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 本日产量计划达成率
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<string> GetDayPlanFinishRate()
        {
            var value = "0";
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21" };
            try
            {
                var start = DateTime.Now.Date;
                var end = start.AddDays(1);
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => (p.PoStatus == "3" || p.PoStatus == "6") && s.Auart == "ZXH1"
                                     && mrpString.Contains(s.Dispo)
                                     && s.Gstrp >= start
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp < end)
                    .Select((c, p, s) => new
                    {
                        c.OrderId,
                        c.GoodCount,
                        s.Psmng
                    })
                    .ToListAsync();
                if (orderList.Count > 0)
                {
                    var a = orderList.Sum(p => p.GoodCount);
                    var t = orderList.Sum(p => p.Psmng);
                    var rate = Math.Round(a * 100 / t, 0);
                    value = rate.ToString();
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 订单转换率
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<string> GetPlanSlewRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21" };
            try
            {

                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) =>
                                   //p.PoStatus == "3"&& c.Status == 1 && s.Auart == "ZXH1"
                                   /*  p.PoStatus == "3" c.Status == 1 &&*/  s.Auart == "ZXH1"
                                     && mrpString.Contains(s.Dispo)
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        c.OrderId,
                        c.Runtime
                    })
                    .ToListAsync();
                var reasonIds = await GetDownTimeReason("产品转换");
                var ids = orderList.Select(s => s.ID).Distinct().ToList();

                var downTimes = await _dal.Db.Queryable<DowntimeEntity>()
                              .Where(p => reasonIds.Contains(p.ReasonId) && p.EndTimeUtc.HasValue && ids.Contains(p.OrderId))
                              .Select(p => new
                              {
                                  p.EquipmentId,
                                  totals = (p.EndTimeUtc.Value - p.StartTimeUtc.Value).TotalHours
                              })
                              .ToListAsync();
                if (orderList.Count > 0 && downTimes.Count > 0)
                {
                    var a = orderList.Sum(p => p.Runtime);
                    var t = Convert.ToDecimal(downTimes.Sum(p => p.totals));
                    if (a > 0)
                    {
                        var rate = Math.Round(t * 100 / (a + t), 0);
                        value = rate.ToString();
                    }

                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 订单满足率
        /// </summary>
        /// <param name="reqmodel">请求模型</param>
        /// <returns></returns>
        private async Task<string> GetPlanFullFillRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "100";
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21" };
            //var reasonList = new List<string> { "Insufficient supply", "Defective products" };
            try
            {

                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => /*p.PoStatus == "3"*/ c.Status == 1 && s.Auart == "ZXH1"
                                     && mrpString.Contains(s.Dispo)
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        c.OrderId,
                        p.ProduceStatus,
                        p.Reason
                    })
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var a = orderList.Where(p => p.ProduceStatus == "NotComplete" && (p.Reason.Contains("Insufficient") || p.Reason.Contains("Defective"))).Count();
                    var t = orderList.Count;
                    if (a > 0)
                    {
                        var rate = Math.Round((t - a) * 100.0 / t, 0);
                        value = rate.ToString();
                    }
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 计算维修及时率
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<string> GetRepairTimeRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "100";
            try
            {
                var condition = $" FROM [TPM_B_REPAIR_ORDER]  where [URGENCY] =  '紧急' and [REPORT_DATE] >='{reqmodel.StartTime.Value.Date.ToString("yyyy-MM-dd")}' and [REPORT_DATE] <'{reqmodel.EndTime.Value.Date.AddDays(1).ToString("yyyy-MM-dd")}' ";
                var okStatus = "('Done')"; //已维修
                var totalStatus = "('Done','Doing','NotStart')"; //是“已维修”，“进行中”，“未开始”
                var countSql = $" SELECT Count(ID) FROM [TPM_B_REPAIR_RECORD] where REPAIR_WO_ID in ( select ID {condition}) and [STATUS]  IN {totalStatus} ";
                var count = await _dal.Db.Ado.SqlQuerySingleAsync<int>(countSql);
                if (count > 0)
                {
                    var okSql = $" SELECT Count(ID) FROM [TPM_B_REPAIR_RECORD] where REPAIR_WO_ID in ( select ID {condition}) and [STATUS]  IN {okStatus} ";
                    var okCount = await _dal.Db.Ado.SqlQuerySingleAsync<int>(okSql);

                    var rate = Math.Round(okCount * 100.0m / count, 1);
                    value = rate.ToString();
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 平均维修时间
        /// </summary>
        /// <param name="reqmodel">请求模型</param>
        /// <returns></returns>
        private async Task<string> GetRepairAvgTime(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            //var reasonList = new List<string> { "Insufficient supply", "Defective products" };
            try
            {
                var condition = $" FROM [TPM_B_REPAIR_ORDER]  where [STATUS] =  'Confirmed' and [CONFIRM_DATE] >='{reqmodel.StartTime.Value.Date.ToString("yyyy-MM-dd")}' and [CONFIRM_DATE] <'{reqmodel.EndTime.Value.Date.AddDays(1).ToString("yyyy-MM-dd")}' ";
                var countSql = $" SELECT Count(ID) {condition} ";
                var count = await _dal.Db.Ado.SqlQuerySingleAsync<int>(countSql);
                if (count > 0)
                {
                    var durationSql = $" SELECT ISNULL( SUM(REPAIR_DURATION),0) FROM [TPM_B_REPAIR_RECORD] where REPAIR_WO_ID in ( select ID {condition}) ";
                    var duration = await _dal.Db.Ado.SqlQuerySingleAsync<decimal>(durationSql);
                    if (duration > 0)
                    {
                        var rate = Math.Round(duration / count, 1);
                        value = rate.ToString();
                    }
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 厂房综合表现性
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<string> GetPlatePerformance(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            //var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21" };
            try
            {

                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => /*p.PoStatus == "3" && */s.Auart == "ZXH1"
                                     && c.Status == 1
                                     && s.Arbpl.Contains("FIL")
                                     && !s.Status.Contains("DLFL")
                                     && s.OeeSpeed > 0
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        c.OrderId,
                        c.GoodCount,
                        c.Runtime,
                        s.OeeSpeed,
                        s.MngPu
                    })
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var a = 0m;
                    var total = 0m;
                    var orderids = orderList.Select(x => x.OrderId).Distinct();
                    foreach (var o in orderids)
                    {
                        var list = orderList.Where(p => p.OrderId == o).ToList();
                        var tcount = list.Sum(p => p.GoodCount * p.MngPu);
                        var tStandard = list.Select(p => p.GoodCount * p.MngPu / p.OeeSpeed).Sum().Value;
                        var tRun = list.Sum(p => p.Runtime);
                        if (tRun > 0)
                        {
                            a += tStandard / tRun * tcount;
                            total += tcount;
                        }
                    }
                    if (total > 0)
                    {
                        var rate = Math.Round(a * 100 / total, 1);
                        value = rate.ToString();
                    }
                }
            }
            catch
            {

            }
            return value;
        }

        /// <summary>
        /// 包材损耗率
        /// </summary>
        /// <param name="reqmodel">请求模型</param>
        /// <returns></returns>
        private async Task<string> GetPackLossRate(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            try
            {
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => /*p.PoStatus == "3" && */ s.Auart == "ZXH1"
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID
                    })
                    .Distinct()
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var ids = orderList.Select(x => x.ID).Distinct().ToList();

                    var mtrId = await _dal.Db.Queryable<MaterialEntity>()
                        .Where(x => x.Type == "ZPKG")
                        .Select(x => x.ID)
                        .ToListAsync();
                    var actualBom = await _dal.Db.Queryable<PoConsumeActualEntity>()
                         .Where(p => ids.Contains(p.ProductionOrderId) && p.Quantity != null)
                         .Select(p => new
                         {
                             p.ID,
                             p.PoConsumeRequirementId,
                             p.Quantity
                         })
                         .ToListAsync();
                    if (actualBom.Count > 0)
                    {
                        var standradBom = await _dal.Db.Queryable<PoConsumeRequirementEntity>()
                            .Where(p => ids.Contains(p.ProductionOrderId) && p.Quantity != null && mtrId.Contains(p.MaterialId))
                             .Select(p => new
                             {
                                 p.ID,
                                 p.Quantity
                             })
                           .ToListAsync();
                        var sQty = standradBom.Sum(p => p.Quantity);
                        if (sQty > 0)
                        {
                            var standardids = standradBom.Select(p => p.ID).ToList();
                            var aQty = actualBom.Where(p => standardids.Contains(p.PoConsumeRequirementId)).Sum(p => p.Quantity);
                            var data = Math.Round((aQty.Value - sQty.Value) * 100.0m / sQty.Value, 1);
                            value = data.ToString();
                        }
                    }
                }
            }
            catch
            {

            }
            return value;
        }
        /// <summary>
        /// 获取全厂产能
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        public async Task<string> GetPlantCapacity(KpiValueRequestSingleModel reqmodel)
        {
            var yearS = reqmodel.StartTime.Value.Year;
            var yearE = reqmodel.EndTime.Value.Year;
            var value = "0";
            try
            {
                var area = (await _eq.FindList(p => p.ID == reqmodel.LineCode || p.EquipmentCode == reqmodel.LineCode)).FirstOrDefault();
                if (area != null)
                {
                    var tagList = await _dal.FindList(p => p.ModelRef == area.ID && p.DataName == reqmodel.KPIName
                     && p.DateType == "预算" && p.Year >= yearS && p.Year <= yearE);
                    if (tagList.Count > 0)
                    {
                        value = Math.Round(tagList.Sum(p => p.Tgt) / tagList.Count, 0).ToString();
                    }
                }
            }
            catch
            {

            }

            return value;
        }

        /// <summary>
        /// 获取半成品酱料消耗量
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <returns></returns>
        public async Task<decimal> GetCompUsingQty(DateTime start, DateTime end)
        {
            var data = 0m;
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21", "H22", "H23" };
            try
            {
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) => (s.Arbpl.Contains("FIL") || s.Arbpl.Contains("PAC"))
                                  //         .Where((c, p, s) => p.PoStatus == "3" && (s.Arbpl.Contains("FIL") || s.Arbpl.Contains("PAC")) 注释20250217 zyq
                                  && mrpString.Contains(s.Dispo)
                                   && s.Gstrp >= start.Date
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= end.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        s.MatnrComp
                    })
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var ids = orderList.Select(x => x.ID).Distinct().ToList();
                    var mtrCodeList = orderList.Select(x => x.MatnrComp).Distinct().ToList();

                    var mtrId = await _dal.Db.Queryable<MaterialEntity>()
                        .Where(x => mtrCodeList.Contains(x.Code))
                        .Select(x => x.ID)
                        .ToListAsync();

                    var actualBom = await _dal.Db.Queryable<PoConsumeActualEntity, PoConsumeRequirementEntity>(
                           (a, r) => new object[]
                         {
                            JoinType.Inner , a.PoConsumeRequirementId == r.ID
                         })
                         .Where((a, r) => ids.Contains(a.ProductionOrderId) && a.Quantity != null && mtrId.Contains(r.MaterialId))
                         .SumAsync((a, r) => a.Quantity);
                    data = actualBom.HasValue ? actualBom.Value : 0;
                }
            }
            catch
            {

            }
            return data;
        }
        /// <summary>
        /// 获取人工人时
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <returns></returns>
        public async Task<decimal> GetHrWorkHour(DateTime start, DateTime end)
        {
            var data = 0m;
            var startString = start.ToString("yyyy-MM-dd");
            var endString = end.ToString("yyyy-MM-dd");
            try
            {
                data = await _dal.Db.Queryable<WorkingHourEntity>()
                   .Where(x => x.Date.CompareTo(startString) > 0 && x.Date.CompareTo(endString) < 0)
                   .SumAsync(x => x.ActualWorkingHour);
            }
            catch
            {
                data = Convert.ToDecimal((end - start).TotalDays) * 700 * 12;
            }
            return data;

        }


        /// <summary>
        /// 规格群组
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<KpiListValueModel> GetGroupSize(KpiValueRequestSingleModel reqmodel)
        {
            var value = "0";
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21", "H22", "H23" };

            //计算所有规格参数

            KpiListValueModel model = new KpiListValueModel()
            {
                LineCode = reqmodel.LineCode,
                KpiName = reqmodel.KPIName,
                KpiCode = reqmodel.KPIName,
                IsBigBetter = true,
                StartTime = reqmodel.StartTime,
                EndTime = reqmodel.EndTime,
                TimeSeries = new List<string>(),
                DataSeries = new List<DataSeriesModel>()
            };




            List<DataSeriesModel> list = new List<DataSeriesModel>();

            try
            {

                //拿数据源
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) =>/*p.PoStatus == "3"*/ c.Status == 1 && (s.Arbpl.Contains("FIL") || s.Arbpl.Contains("PAC"))
                                     && s.Gstrp >= reqmodel.StartTime.Value.Date
                                     && !s.Status.Contains("DLFL")
                                     && mrpString.Contains(s.Dispo)
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        s.MatnrComp
                    })
                    .ToListAsync();

                //判断数据源
                if (orderList.Count > 0)
                {
                    var ids = orderList.Select(x => x.ID).Distinct().ToList();
                    var mtrCodeList = orderList.Select(x => x.MatnrComp).Distinct().ToList();

                    //汇总规格群组物料
                    var mtrList = await _dal.Db.Queryable<MaterialEntity>()
                        .Where(x => mtrCodeList.Contains(x.Code) && !string.IsNullOrEmpty(x.MatklZh) && !string.IsNullOrEmpty(x.Kschl1))//规格 群租
                        .Select(x => new
                        {
                            x.ID,
                            x.Kschl1,
                            x.MatklZh,//群组 //规格

                        })
                        .ToListAsync();


                    var mtrId = mtrList.Select(x => x.ID).ToList();
                    var actualBom = await _dal.Db.Queryable<PoConsumeActualEntity, PoConsumeRequirementEntity>(
                           (a, r) => new object[]
                         {
                            JoinType.Inner , a.PoConsumeRequirementId == r.ID
                         })
                         .Where((a, r) => ids.Contains(a.ProductionOrderId) && a.Quantity != null && mtrId.Contains(r.MaterialId))
                         .Select((a, r) => new
                         {
                             a.Quantity,
                             r.MaterialId
                         })
                         .ToListAsync();

                    //汇总规格群组
                    var listQuantity = (from b in actualBom
                                        join m in mtrList on b.MaterialId equals m.ID
                                        select new
                                        {
                                            b.Quantity,
                                            gSize = m.Kschl1 + '-' + m.MatklZh
                                        }).ToList();

                    //计算分类数据
                    var lgroup = listQuantity.GroupBy(p => p.gSize)
                                      .Select(p => new
                                      {
                                          p.Key,
                                          percentage = 0m,
                                          qty = p.Sum(x => x.Quantity.Value) / 1000
                                      })
                                      .OrderByDescending(p => p.qty).ToList();



                    if (lgroup.Count() > 0)
                    {
                        List<string> listName = new List<string>();
                        List<decimal> listNumber = new List<decimal>();
                        List<decimal> listpercentage = new List<decimal>();

                        //数据之和
                        var tSum = lgroup.Sum(p => p.qty);

                        if (tSum > 0)
                        {
                            string num = string.Empty;
                            string name = string.Empty;
                            decimal percentage = 0m;

                            //循环计算占比
                            for (int i = 0; i < lgroup.Count; i++)
                            {
                                //名称
                                name = lgroup[i].Key;
                                listName.Add(name);

                                listNumber.Add(Convert.ToDecimal(lgroup[i].qty));

                                percentage = Math.Round(lgroup[i].qty / tSum / Convert.ToDecimal(100), 2);
                                listpercentage.Add(percentage);


                            }
                            model.TimeSeries = listName;//绑定纵坐标
                            model.DataSeries.Add(new DataSeriesModel
                            {
                                Name = "实际",//数量
                                            //XSeries = lgroup.Select(p => p.Key).ToList(),
                                Data = listNumber
                            });
                            model.DataSeries.Add(new DataSeriesModel
                            {
                                Name = "目标",//占比
                                            //XSeries = lgroup.Select(p => p.Key).ToList(),
                                Data = listpercentage
                            });

                        }
                    }
                }
                else
                {
                    model.TimeSeries = new List<string> { "暂无", "暂无", "暂无", "暂无", "暂无" };
                    model.DataSeries.Add(new DataSeriesModel
                    {
                        Name = "目标",
                        Data = new List<decimal> { 0, 0, 0, 0, 0 }
                    });
                    model.DataSeries.Add(new DataSeriesModel
                    {
                        Name = "实际",
                        Data = new List<decimal> { 0, 0, 0, 0, 0 }
                    });
                }
            }
            catch
            {

            }

            return model;
        }


        /// <summary>
        /// 产量同期对比
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<List<DataSeriesModel>> GetProductCompareSeries(KpiValueRequestSingleModel reqmodel)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            try
            {
                DataSeriesModel target = new DataSeriesModel()
                {
                    Name = "目标",
                    Data = await GetKpiTargetList(reqmodel)
                };

                var weekList = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
                var weekList2 = TimeDurationHelper.GetWeeks(reqmodel.StartTime.Value.AddYears(-1), reqmodel.EndTime.Value.AddYears(-1));
                List<decimal> aData = new List<decimal>();
                List<decimal> cData = new List<decimal>();
                List<decimal> lData = new List<decimal>();
                foreach (var week in weekList)
                {
                    var start = week.WeekStart >= reqmodel.StartTime.Value.Date ? week.WeekStart : reqmodel.StartTime.Value.Date;
                    var end = week.WeekEnd <= reqmodel.EndTime.Value.Date ? week.WeekEnd : reqmodel.EndTime.Value.Date;
                    var p = await GetCompUsingQty(start, end);
                    var h = await GetWeekProductTgtQty(start, end);

                    decimal value = p / Convert.ToDecimal(1000);

                    aData.Add(Math.Round(value, 2));
                    cData.Add(h);
                }

                foreach (var week in weekList2)
                {
                    var start = week.WeekStart >= reqmodel.StartTime.Value.Date ? week.WeekStart : reqmodel.StartTime.Value.Date;
                    var end = week.WeekEnd <= reqmodel.EndTime.Value.Date ? week.WeekEnd : reqmodel.EndTime.Value.Date;
                    var p = await GetCompUsingQty(start, end);

                    decimal value = p / Convert.ToDecimal(1000);

                    lData.Add(Math.Round(value, 2));
                }

                DataSeriesModel actual = new DataSeriesModel()
                {
                    Name = "今年",
                    Data = aData
                };
                DataSeriesModel vs = new DataSeriesModel()
                {
                    Name = "VS预算",
                    Data = cData
                };
                DataSeriesModel last = new DataSeriesModel()
                {
                    Name = "去年",
                    Data = lData
                };
                list.Add(target);
                list.Add(actual);
                list.Add(target);
                list.Add(last);
            }
            catch
            {
                list.Add(new DataSeriesModel { Name = "今年", Data = new List<decimal>() });
                list.Add(new DataSeriesModel { Name = "VS预算", Data = new List<decimal>() });
                list.Add(new DataSeriesModel { Name = "去年", Data = new List<decimal>() });
            }
            return list;
        }
        /// <summary>
        /// 获取TOP6产量排名
        /// </summary>
        /// <param name="reqmodel"></param>
        /// <returns></returns>
        private async Task<List<DataSeriesModel>> GetProductTop6Series(KpiValueRequestSingleModel reqmodel, int size = 6)
        {
            List<DataSeriesModel> list = new List<DataSeriesModel>();
            var mrpString = new List<string> { "H11", "H12", "H13", "H14", "H21", "H22", "H23" };
            try
            {
                var orderList = await _dal.Db.Queryable<ConfirmationEntity, ProductionOrderEntity, SappackorderEntity>(
                    (c, p, s) => new object[]
                    {
                    JoinType.Inner , c.OrderId == p.ID,
                    JoinType.Inner, p.ProductionOrderNo == s.Aufnr
                    })
                    .Where((c, p, s) =>/*p.PoStatus == "3" c.Status == 1 &&*/ (s.Arbpl.Contains("FIL") || s.Arbpl.Contains("PAC"))
                                    && s.Gstrp >= reqmodel.StartTime.Value.Date
                                      && mrpString.Contains(s.Dispo)
                                     && !s.Status.Contains("DLFL")
                                     && s.Gstrp <= reqmodel.EndTime.Value.Date)
                    .Select((c, p, s) => new
                    {
                        p.ID,
                        s.MatnrComp
                    })
                    .ToListAsync();

                if (orderList.Count > 0)
                {
                    var ids = orderList.Select(x => x.ID).Distinct().ToList();
                    var mtrCodeList = orderList.Select(x => x.MatnrComp).Distinct().ToList();

                    var mtrList = await _dal.Db.Queryable<MaterialEntity>()
                        .Where(x => mtrCodeList.Contains(x.Code) && !string.IsNullOrEmpty(x.Categorycode))
                        .Select(x => new
                        {
                            x.ID,
                            x.Categorycode
                        })
                        .ToListAsync();
                    var mtrId = mtrList.Select(x => x.ID).ToList();
                    var actualBom = await _dal.Db.Queryable<PoConsumeActualEntity, PoConsumeRequirementEntity>(
                           (a, r) => new object[]
                         {
                            JoinType.Inner , a.PoConsumeRequirementId == r.ID
                         })
                         .Where((a, r) => ids.Contains(a.ProductionOrderId) && a.Quantity != null && mtrId.Contains(r.MaterialId))
                         .Select((a, r) => new
                         {
                             a.Quantity,
                             r.MaterialId
                         })
                         .ToListAsync();
                    var listQuantity = (from b in actualBom
                                        join m in mtrList on b.MaterialId equals m.ID
                                        select new
                                        {
                                            b.Quantity,
                                            m.Categorycode
                                        }).ToList();
                    var lgroup = listQuantity.GroupBy(p => p.Categorycode)
                                      .Select(p => new
                                      {
                                          p.Key,
                                          qty = p.Sum(x => x.Quantity.Value) / 1000
                                      })
                                      .OrderByDescending(p => p.qty)
                                      .Take(size).ToList();
                    if (lgroup.Count() > 0)
                    {
                        list.Add(new DataSeriesModel
                        {
                            Name = "实际",
                            XSeries = lgroup.Select(p => p.Key).ToList(),
                            Data = lgroup.Select(p => Math.Round(p.qty, 2)).ToList()
                        });
                    }
                }
            }
            catch
            {

            }
            if (list.Count == 0)
            {
                list.Add(new DataSeriesModel { Name = "实际", Data = new List<decimal>() });
            }
            return list;
        }
        /// <summary>
        /// 获取周生产预算值
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <returns></returns>
        private async Task<decimal> GetWeekProductTgtQty(DateTime start, DateTime end)
        {
            var data = 0m;
            try
            {
                var startYear = start.Year;
                var startMonth = start.Month;
                var monthStart = TimeDurationHelper.GetMonthFirstDay(start);
                var monthEnd = TimeDurationHelper.GetMonthLastDay(end);
                var endYear = end.Year;
                var endMonth = end.Month;
                var monthData1 = await _dal.Db.Queryable<ProdtgtContainerEntity>().Where(p => p.Year == startYear && p.Month == startMonth).SumAsync(p => p.Tgt);
                var workdayCount1 = await GetWorkDayCount(new DateTime(startYear, startMonth, 1));
                if (workdayCount1 > 0)
                {
                    var monthPerDay = monthData1 / workdayCount1;
                    if (end <= monthEnd)
                    {
                        data = monthPerDay * (end.AddDays(1) - start).Days;
                    }
                    else
                    {
                        data += monthPerDay * (monthEnd.AddDays(1) - start).Days;
                        var monthData2 = await _dal.Db.Queryable<ProdtgtContainerEntity>().Where(p => p.Year == endYear && p.Month == endMonth).SumAsync(p => p.Tgt);
                        var workdayCount2 = await GetWorkDayCount(new DateTime(endYear, endMonth, 1));

                        if (workdayCount2 > 0)
                        {
                            var monthPerDay2 = monthData2 / workdayCount2;
                            data += monthPerDay2 * (end - monthEnd).Days;
                        }
                        else
                        {
                            data += monthPerDay * (end - monthEnd).Days;
                        }
                    }
                }

            }
            catch
            {
                data = 0;
            }
            return Math.Round(data, 0);
        }
        /// <summary>
        /// 获取工作日数量
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private async Task<decimal> GetWorkDayCount(DateTime dateTime)
        {
            var nextMonth = dateTime.AddMonths(1);
            var count = await _dal.Db.Queryable<SappackorderEntity>()
                  .Where(p => !p.Status.Contains("DLFL") && p.Gstrp >= dateTime && p.Gstrp < nextMonth)
                  .Select(p => p.Gstrp)
                  .Distinct()
                  .CountAsync();
            return count;
        }
        /// <summary>
        /// 获取年累计产量
        /// </summary>
        /// <param name="reqmodel">请求参数</param>
        /// <returns></returns>
        private async Task<string> GetYearProduction(KpiValueRequestSingleModel reqmodel)
        {
            var value = await GetCompUsingQty(reqmodel.StartTime.Value, reqmodel.EndTime.Value);
            return Math.Round(value / 1000, 2).ToString();
        }
        #endregion
    }
}
