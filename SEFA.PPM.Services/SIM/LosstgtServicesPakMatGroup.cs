
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using System;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPM.Services
{
    public class LosstgtServicesPakMatGroup : BaseServices<LosstgtEntity>, ILosstgtServicesPakMatGroup
    {
        private readonly IBaseRepository<LosstgtEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _eq;
        private readonly IBaseRepository<MaterialEntity> _material;
        private readonly IKpitgtServices _kpitgtServices;
        private readonly IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<MaterialGroupEntity> _mterGroup;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _unit;
        private readonly IBaseRepository<LosstgtViewEntity> _LosstgtViewEntity;
        public LosstgtServicesPakMatGroup(IBaseRepository<LosstgtEntity> dal, IKpitgtServices kpitgtServices, IUser user, IBaseRepository<EquipmentEntity> eq, IUnitOfWork unitOfWork, IBaseRepository<MaterialEntity> material, IBaseRepository<DFM.Model.Models.UnitmanageEntity> unit, IBaseRepository<LosstgtViewEntity> losstgtViewEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _kpitgtServices = kpitgtServices;
            _user = user;
            _eq = eq;
            _unitOfWork = unitOfWork;
            _material = material;
            _unit = unit;
            _LosstgtViewEntity = losstgtViewEntity;
        }

        public async Task<List<LosstgtGroupModel>> GetList(LosstgtRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LosstgtViewEntity>()
                             .And(p=>p.LossType.Equals("包材损耗-物料群组"))
                             .AndIF(!string.IsNullOrEmpty(reqModel.Year),p=>p.Year==Convert.ToInt32(reqModel.Year))
                             .AndIF(!string.IsNullOrEmpty(reqModel.Item),p=>p.Item.Equals(reqModel.Item))
                             .ToExpression();
            var data = await _LosstgtViewEntity.FindList(whereExpression);
            var results = (from a in data
                           select new LosstgtGroupModel
                           {
                               //LossType = a.LossType,
                               ItemGroup=a.ItemGroup,
                               Item = a.Item,
                               Year = a.Year,
                               UnitName = a.UnitName,
                               Tgt = a.Tgt

                           }).ToList();
            return results;
        }

        public async Task<PageModel<LosstgtEntity>> GetPageList(LosstgtRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LosstgtEntity>()
                             .And(p => p.LossType.Equals("包材损耗-物料群组"))
                             .AndIF(!string.IsNullOrEmpty(reqModel.Year), p => p.Year == Convert.ToInt32(reqModel.Year))
                             .AndIF(!string.IsNullOrEmpty(reqModel.Item), p => p.Item.Equals(reqModel.Item))
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            var whereUnitExpression = Expressionable.Create<DFM.Model.Models.UnitmanageEntity>()
                                                    .ToExpression();
            var unitData = await _unit.FindList(whereUnitExpression);
            foreach (var item in data.data)
            {
                var unitModel = unitData.Where(p => p.ID.Equals(item.Unit)).FirstOrDefault();
                if (unitModel != null)
                {
                    item.Unit = unitModel.Name;
                }
                else
                {
                    item.Unit = null;
                }

            }
            return data;
        }
        public async Task<bool> SaveForm(LosstgtEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
        public async Task<List<string>> GetItem()
        {
            List<string> res = new List<string>();
            var whereExpression = Expressionable.Create<MaterialEntity>()
                     .ToExpression();
            var data = await _material.FindList(whereExpression);
            res = data.GroupBy(p => p.MatklZh).Select(p => p.Key).ToList();
            return res;
        }
        public async Task<List<string>> GetItemGroup()
        {
            List<string> res = new List<string>();
            var whereExpression = Expressionable.Create<MaterialEntity>()
                     .ToExpression();
            var data = await _material.FindList(whereExpression);
            res = data.GroupBy(p => p.Kschl1).Select(p => p.Key).ToList();
            return res;
        }

        public async Task<ResultString> ImportData([FromForm] FileImportDto input)
        {

            ResultString result = new ResultString();
            try
            {
                _unitOfWork.BeginTran();
                var stream = input.File.OpenReadStream();
                // 检查文件是否存在
                if (stream == null)
                {
                    result.AddError("未找到文件,请重新上传");
                    return result;
                }
                //获取表格数据
                var fileData = await _kpitgtServices.ReadExcel(stream);
                if (fileData.Rows.Count < 1)
                {
                    result.AddError("表格中没有效数据");
                    return result;
                }
                List<LosstgtEntity> excelDataList = new List<LosstgtEntity>();
                List<LosstgtEntity> upDataList = new List<LosstgtEntity>();
                List<LosstgtEntity> insertDataList = new List<LosstgtEntity>();
                var wheredtgExpression = Expressionable.Create<LosstgtEntity>()
                                     .ToExpression();
                var dtgDataList = await _dal.FindList(wheredtgExpression);
                var whereUnitExpression = Expressionable.Create<DFM.Model.Models.UnitmanageEntity>()
                          .ToExpression();
                var unitDataList = await _unit.FindList(whereUnitExpression);
                var whereExpression = Expressionable.Create<EquipmentEntity>()
                      .ToExpression();
                var eqDataList = await _eq.FindList(whereExpression);
                var whereMaterialExpression = Expressionable.Create<MaterialEntity>()
                                     .ToExpression();
                var materList = await _material.FindList(whereMaterialExpression);
                string year = string.Empty;
                for (int i = 0; i < fileData.Rows.Count; i++)
                {
                    if (i == 0)
                    {
                        //获取年份
                        year = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        if (string.IsNullOrEmpty(year))
                        {
                            result.AddError("表格中年份不能为空");
                            return result;
                        }
                    }
                    else if (i == 1)
                    {
                        continue;
                    }
                    else
                    {
                        string unit=string.Empty;
                        string itemGroup=string.Empty;
                        var item = fileData.Rows[i]["Column1"] == null ? "" : fileData.Rows[i]["Column1"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(item))
                        {
                            result.AddError(string.Format(@"第'{0}'行，包材（物料）小分类列为空，导入失败", i + 1));
                            return result;
                        }
                        else 
                        {
                            //判断物料中是否能够找到该包材（物料）小分类
                            var material = materList.Where(p => p.MatklZh.Equals(item)).FirstOrDefault();
                            if (material != null)
                            {
                                itemGroup = material.Kschl1;
                            }
                            else 
                            {
                                result.AddError(string.Format(@"第'{0}'行，包材（物料）小分类列未匹配到对应的小分列信息请检查后输入，导入失败", i + 1));
                                return result;
                            }

                        }
                        var tgt = fileData.Rows[i]["Column2"] == null ? "" : fileData.Rows[i]["Column2"].ToString();
                        //判断空
                        if (string.IsNullOrEmpty(tgt))
                        {
                            continue;
                        }
                        else 
                        {
                             unit = fileData.Rows[i]["Column3"] == null ? "" : fileData.Rows[i]["Column3"].ToString();
                            //判断空
                            if (string.IsNullOrEmpty(unit))
                            {
                                result.AddError(string.Format(@"第'{0}'行，单位列为空，导入失败", i + 1));
                                return result;
                            }
                        }
                        LosstgtEntity entity = new LosstgtEntity();
                        entity.CreateCustomGuid(_user.Name);
                        entity.Item = item;
                        entity.ItemGroup = itemGroup;
                        entity.LossType = "包材损耗-物料群组";
                        entity.Year = Convert.ToInt32(year);
                        entity.Tgt = Convert.ToDecimal(tgt);
                        entity.Unit = unit;
                        excelDataList.Add(entity);
                    }

                }
                //检查是否存在相同的数据如果有则更新，反之则插入
                foreach (var item in excelDataList)
                {
                    //检查输入的单位是否能在维护的表中找到，没有则新增
                    var unitData = unitDataList.Where(p => p.Name.Equals(item.Unit)).FirstOrDefault();
                    if (unitData != null)
                    {
                        item.Unit = unitData.ID;
                    }
                    else
                    {
                        DFM.Model.Models.UnitmanageEntity unitmanageEntity = new DFM.Model.Models.UnitmanageEntity();
                        unitmanageEntity.Name = item.Unit;
                        unitmanageEntity.Enable = 1;
                        unitmanageEntity.Deleted = 0;
                        var unitAdd = _unit.Add(unitmanageEntity);
                        item.Unit = unitmanageEntity.ID;
                    }
                    var oldData = dtgDataList.Where(p => p.Item.Equals(item.Item) &&
                                                         p.ItemGroup.Equals(item.ItemGroup)&&
                                                         p.LossType.Equals("包材损耗-物料群组") &&
                                                         p.Year == item.Year  && p.Unit.Equals(item.Unit)).FirstOrDefault();
                    if (oldData != null)
                    {
                        oldData.Tgt = item.Tgt;
                        upDataList.Add(oldData);
                    }
                    else
                    {
                        insertDataList.Add(item);
                    }
                }
                if (insertDataList != null && insertDataList.Count > 0)
                {
                    if (await _dal.Add(insertDataList) <= 0)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("插入失败");
                        return result;
                    }
                }
                if (upDataList != null && upDataList.Count > 0)
                {
                    if (!await _dal.Update(upDataList))
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError("更新失败");
                        return result;
                    }
                }
                _unitOfWork.CommitTran();
                result.Succeed = true;
                result.Data = string.Format("导入成功，共插入{0}条数据，更新{1}条数据", insertDataList.Count, upDataList.Count);
                return result;
            }
            catch (Exception e)
            {
                _unitOfWork.RollbackTran();
                result.AddError(e.StackTrace.ToString());
                return result;
            }
        }
    }
}