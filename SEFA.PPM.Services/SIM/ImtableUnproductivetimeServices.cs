
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using Abp.Domain.Entities;
using System.Linq;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.Models.Interface;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Database;
using SEFA.Base.Common.HttpContextUser;
using System.Collections;

namespace SEFA.PPM.Services
{
    public class ImtableUnproductivetimeServices : BaseServices<ImtableUnproductivetimeEntity>, IImtableUnproductivetimeServices
    {
        private readonly IBaseRepository<ImtableUnproductivetimeEntity> _dal;
        private readonly IBaseRepository<UnproductiveTimeEntity> _unPro;
        private readonly IUnitOfWork  _unitOfWork;
        public IUser _user;
        public ImtableUnproductivetimeServices(IBaseRepository<ImtableUnproductivetimeEntity> dal, IBaseRepository<UnproductiveTimeEntity> unPro, IUnitOfWork unitOfWork, IUser user)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _unPro = unPro;
            _unitOfWork = unitOfWork;
            _user = user;
        }

        public async Task<List<ImtableUnproductivetimeEntity>> GetList(ImtableUnproductivetimeRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ImtableUnproductivetimeEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<ImtableUnproductivetimeEntity>> GetPageList(ImtableUnproductivetimeRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ImtableUnproductivetimeEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }
        /// <summary>
        /// 修改时同步修改辅助工时中间表
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel> SyncUpdateData(UnproductiveTimeEntity entity)
        {
            var result = new MessageModel
            {
                msg = "操作失败！",
                success = false
            };
            List<ImtableUnproductivetimeEntity> imtableUnproductivetimesAdd = new List<ImtableUnproductivetimeEntity>();
            List<ImtableUnproductivetimeEntity> imtableUnproductivetimesUpdate = new List<ImtableUnproductivetimeEntity>();
            List<string> strings = new List<string>();
            try
            {
                if (entity != null)
                {
                    if (entity.CreateDate.Date!=DateTime.Now.Date)
                    {
                        DateTime startOfTheDayBeforeYesterday = new DateTime(entity.CreateDate.Year, entity.CreateDate.Month, entity.CreateDate.Day, 0, 0, 0);
                        DateTime endOfTheDayBeforeYesterday = startOfTheDayBeforeYesterday.AddDays(1).AddSeconds(-1);
                        var unproductiveTimes = await _unPro.FindEntity(entity.ID);
                        if (unproductiveTimes != null)
                        {
                            if (entity.LineId == unproductiveTimes.LineId && entity.ReasonGroupId == unproductiveTimes.ReasonGroupId && entity.ReasonId == unproductiveTimes.ReasonId && entity.ActualLaborMinutes == unproductiveTimes.ActualLaborMinutes)
                            {

                            }
                            else if (entity.LineId == unproductiveTimes.LineId && entity.ReasonGroupId == unproductiveTimes.ReasonGroupId && entity.ReasonId == unproductiveTimes.ReasonId)
                            {
                                var UnproModel = await _unPro.FindList(p => p.CreateDate >= startOfTheDayBeforeYesterday && p.CreateDate <= endOfTheDayBeforeYesterday && p.LineId == entity.LineId && p.ReasonGroupId == p.ReasonGroupId && p.ReasonId == entity.ReasonId);
                                var k = UnproModel.Sum(p => p.ActualLaborMinutes);
                                var ImtableEntity = await _dal.FindEntity(p => p.LineId == entity.LineId && p.ReasonGroup == entity.ReasonGroupId && p.Reason == entity.ReasonId && p.Year == entity.CreateDate.Year && p.Month == entity.CreateDate.Month && p.Day == entity.CreateDate.Day);
                                if (ImtableEntity != null)
                                {
                                    ImtableEntity.UnproductiveTime = k;
                                    ImtableEntity.Modify(ImtableEntity.ID, _user.Name.ToString());
                                    imtableUnproductivetimesUpdate.Add(ImtableEntity);
                                }

                            }
                            else
                            {
                                var model = await _dal.FindEntity(p => p.LineId == unproductiveTimes.LineId && p.ReasonGroup == unproductiveTimes.ReasonGroupId && p.Reason == unproductiveTimes.ReasonId && p.Year == unproductiveTimes.CreateDate.Year && p.Month == unproductiveTimes.CreateDate.Month && p.Day == unproductiveTimes.CreateDate.Day);
                                if (model != null)
                                {
                                    model.UnproductiveTime -= unproductiveTimes.ActualLaborMinutes;
                                    if (model.UnproductiveTime > 0)
                                    {
                                        model.UnproductiveTime = model.UnproductiveTime;
                                        model.Modify(model.ID, _user.Name.ToString());
                                        imtableUnproductivetimesUpdate.Add(model);
                                    }
                                    else
                                    {
                                        strings.Add(model.ID);
                                    }
                                }
                                //var UnproModel = await _unPro.FindList(p => p.CreateDate >= startOfTheDayBeforeYesterday && p.CreateDate <= endOfTheDayBeforeYesterday && p.LineId == entity.LineId && p.ReasonGroupId == entity.ReasonGroupId && p.ReasonId == entity.ReasonId);
                                /* var Uuprodate = UnproModel.GroupBy(p => new { p.ReasonGroupId, p.ReasonId, p.LineId })
                                .Select(p => new
                                {
                                    p.Key.LineId,
                                    p.Key.ReasonGroupId,
                                    p.Key.ReasonId,
                                    ActualLaborHours = p.Sum(p => p.ActualLaborMinutes)
                                }).ToList();*/
                                //往中间表插入数据
                                /*                            foreach (var u in Uuprodate)
                                                            {*/
                                var ImtableEntity = await _dal.FindEntity(p => p.LineId == entity.LineId && p.ReasonGroup == entity.ReasonGroupId && p.Reason == entity.ReasonId && p.Year == entity.CreateDate.Year && p.Month == entity.CreateDate.Month && p.Day == entity.CreateDate.Day);
                                if (ImtableEntity == null)
                                {
                                    ImtableUnproductivetimeEntity imtable = new ImtableUnproductivetimeEntity();
                                    imtable.Create(_user.Name.ToString());
                                    imtable.Year = entity.CreateDate.Year;
                                    imtable.Month = entity.CreateDate.Month;
                                    imtable.Day = entity.CreateDate.Day;
                                    imtable.LineId = entity.LineId;
                                    imtable.ReasonGroup = entity.ReasonGroupId;
                                    imtable.Reason = entity.ReasonId;
                                    imtable.UnproductiveTime = entity.ActualLaborMinutes;
                                    imtable.HourType = "LABOR";
                                    imtableUnproductivetimesAdd.Add(imtable);
                                }
                                else
                                {
                                    ImtableEntity.UnproductiveTime += entity.ActualLaborMinutes;
                                    ImtableEntity.Modify(ImtableEntity.ID, _user.Name.ToString());
                                    imtableUnproductivetimesUpdate.Add(ImtableEntity);
                                }

                                /* }*/
                            }
                        }

                    }


                }
                _unitOfWork.BeginTran();
                if (imtableUnproductivetimesAdd != null && imtableUnproductivetimesAdd.Count > 0)
                {
                    await _dal.Add(imtableUnproductivetimesAdd);
                }
                bool imtableUn = true;
                if (imtableUnproductivetimesUpdate.Count > 0)
                {
                    imtableUn = await _dal.Update(imtableUnproductivetimesUpdate);
                }
                bool imtableDel = true;
                if (strings.Count > 0)
                {
                    imtableDel = await _dal.DeleteById(strings);
                }
                _unitOfWork.CommitTran();
                result.msg = "修改时同步修改辅助工时中间表" + "修改条数：" + imtableUnproductivetimesUpdate.Count+"，增加条数：" + imtableUnproductivetimesAdd.Count;
                result.success = true;
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = ex.Message;
                return result;
                throw;
            }
        }

        /// <summary>
        /// 同步往辅助工时中间表插入数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel> SyncInsertData()
        {
            var result = new MessageModel
            {
                msg = "操作失败！",
                success = false
            };
            List<ImtableUnproductivetimeEntity> imtableUnproductivetimesAdd = new List<ImtableUnproductivetimeEntity>();


            DateTime today = DateTime.Now; // 获取当前时间
            DateTime yesterDay = today.AddDays(-1); // 前一天

            DateTime startOfTheDayBeforeYesterday = new DateTime(yesterDay.Year, yesterDay.Month, yesterDay.Day, 0, 0, 0);
            DateTime endOfTheDayBeforeYesterday = startOfTheDayBeforeYesterday.AddDays(1).AddSeconds(-1);

            var Unpro = await _unPro.FindList(p => p.CreateDate >= startOfTheDayBeforeYesterday && p.CreateDate <= endOfTheDayBeforeYesterday);
            try
            {
                if (Unpro.Count > 0)
                {
                    var Uuprodate = Unpro.GroupBy(p => new { p.ReasonGroupId, p.ReasonId, p.LineId })
                        .Select(p => new
                        {
                            p.Key.LineId,
                            p.Key.ReasonGroupId,
                            p.Key.ReasonId,
                            ActualLaborHours = p.Sum(p => p.ActualLaborMinutes)
                        }).ToList();
                    //往中间表插入数据
                    foreach (var u in Uuprodate)
                    {
                        ImtableUnproductivetimeEntity imtable = new ImtableUnproductivetimeEntity();
                        imtable.Create(_user.Name.ToString());
                        imtable.Year = yesterDay.Year;
                        imtable.Month = yesterDay.Month;
                        imtable.Day = yesterDay.Day;
                        imtable.LineId = u.LineId;
                        imtable.ReasonGroup = u.ReasonGroupId;
                        imtable.Reason = u.ReasonId;
                        imtable.UnproductiveTime = u.ActualLaborHours;
                        imtable.HourType = "LABOR";
                        imtableUnproductivetimesAdd.Add(imtable);
                    }
                }


                _unitOfWork.BeginTran();
                if (imtableUnproductivetimesAdd != null && imtableUnproductivetimesAdd.Count > 0)
                {
                    await _dal.Add(imtableUnproductivetimesAdd);
                }
                _unitOfWork.CommitTran();
                result.msg = "操作成功同步到辅助工时中间表" + imtableUnproductivetimesAdd.Count + "条数据。";
                result.success = true;
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = ex.Message;
                return result;
                throw;
            }
        }
        /*public async Task<bool> SyncInsertData(UnproductiveTimeEntity entity)
        {
            return true;
            if (entity != null)
            {
                //缺生产线
                var whereExpression = Expressionable.Create<ImtableUnproductivetimeEntity>()
                             .And(p => p.Year == entity.CreateDate.Year)
                             .And(p => p.Month == entity.CreateDate.Month)
                             .And(p => p.Day == entity.CreateDate.Day)
                             .AndIF(!string.IsNullOrEmpty(entity.LineId), p => p.LineId.Equals(entity.LineId))
                             .AndIF(!string.IsNullOrEmpty(entity.ReasonGroupId), p => p.ReasonGroup.Equals(entity.ReasonGroupId))
                             .AndIF(!string.IsNullOrEmpty(entity.ReasonId), p => p.Reason.Equals(entity.ReasonId))
                             .ToExpression();
                var oldImpro = await _dal.FindEntity(whereExpression);
                if (oldImpro != null)
                {
                    oldImpro.UnproductiveTime += entity.ActualLaborMinutes;
                    return await _dal.Update(oldImpro);
                }
                else 
                {
                    ImtableUnproductivetimeEntity imtableUnproductivetimeEntity = new ImtableUnproductivetimeEntity();

					imtableUnproductivetimeEntity.LineId = entity.LineId;
					imtableUnproductivetimeEntity.Year = entity.CreateDate.Year;
                    imtableUnproductivetimeEntity.Month = entity.CreateDate.Month;
                    imtableUnproductivetimeEntity.Day = entity.CreateDate.Day;
                    imtableUnproductivetimeEntity.ReasonGroup = entity.ReasonGroupId;
                    imtableUnproductivetimeEntity.UnproductiveTime=entity.ActualLaborMinutes;
                    imtableUnproductivetimeEntity.Reason = entity.ReasonId;
                    imtableUnproductivetimeEntity.HourType = "LABOR";
                    imtableUnproductivetimeEntity.LineId = entity.LineId;
                    return await _dal.Add(imtableUnproductivetimeEntity) > 0;
                }

            }
            else 
            {
               return false;
            }
        }
        public async Task<bool> SyncUpdateData(UnproductiveTimeEntity entity)
		{
			
			if (entity != null&&!string.IsNullOrEmpty(entity.ID))
            {
                var whereUnproExpression = Expressionable.Create<UnproductiveTimeEntity>()
                             .And(p => p.ID.Equals(entity.ID))
                             .ToExpression();
                var oldUnproModel=await _unPro.FindEntity(whereUnproExpression);
                if (oldUnproModel != null)
                {
                    var whereExpression = Expressionable.Create<ImtableUnproductivetimeEntity>()
                             .And(p=>p.Year==oldUnproModel.CreateDate.Year)
                             .And(p => p.Month == oldUnproModel.CreateDate.Month)
                             .And(p => p.Day == oldUnproModel.CreateDate.Day)
                              .AndIF(!string.IsNullOrEmpty(entity.LineId), p => p.LineId.Equals(entity.LineId))
                             .AndIF(!string.IsNullOrEmpty(oldUnproModel.ReasonGroupId),p=>p.ReasonGroup.Equals(oldUnproModel.ReasonGroupId))
                             .AndIF(!string.IsNullOrEmpty(oldUnproModel.ReasonId), p => p.Reason.Equals(oldUnproModel.ReasonId))
                             .ToExpression();
                    var oldImpro = await _dal.FindEntity(whereExpression);
                    oldImpro.Year = entity.CreateDate.Year;
                    oldImpro.Month = entity.CreateDate.Month;
                    oldImpro.Day = entity.CreateDate.Day;
                    oldImpro.LineId = entity.LineId;
                    oldImpro.ReasonGroup = entity.ReasonGroupId;
                    oldImpro.Reason = entity.ReasonId;
                    oldImpro.UnproductiveTime += (entity.ActualLaborMinutes - oldUnproModel.ActualLaborMinutes);
                    return await _dal.Update(oldImpro);
                }
                else 
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }*/
        /// <summary>
        /// 辅助工时删除时同步更新辅助工时中间表
        /// </summary>
        /// <param name="idList"></param>
        /// <returns></returns>
        public async Task<MessageModel> SyncDelData(string[] idList)
        {
            var result = new MessageModel
            {
                msg = "操作失败！",
                success = false
            };
            List<ImtableUnproductivetimeEntity> imtableUnproductivetimesUpdate = new List<ImtableUnproductivetimeEntity>();
            List<string> deleteIds = new List<string>();
            try
            {
                if (idList.Length > 0)
                {
                    var UnproModel = (await _unPro.FindList(P => idList.Contains(P.ID)));
                    
                    var deleleteList = UnproModel.GroupBy(P => new {dx= P.CreateDate.Date, P.LineId, P.ReasonGroupId, P.ReasonId }).ToList();
                    var k = deleleteList.Select(x => x.Key).ToList();
                    
                    var ImtableEntity = await _dal.FindList(p => k.Any(x => x.LineId == p.LineId && p.ReasonGroup == x.ReasonGroupId && p.Reason == x.ReasonId && p.Year == x.dx.Year && p.Month == x.dx.Month && p.Day == x.dx.Day));
                    foreach (var x in deleleteList)
                    {
                        if (x.Key.dx.Date != DateTime.Now.Date)
                        {
                            var imtable = ImtableEntity.Find(p => x.Key.LineId == p.LineId && p.ReasonGroup == x.Key.ReasonGroupId && p.Reason == x.Key.ReasonId && p.Year == x.Key.dx.Year && p.Month == x.Key.dx.Month && p.Day == x.Key.dx.Day);
                            if (imtable != null)
                            {
                                var sumNum = x.Sum(p => p.ActualLaborMinutes);
                                imtable.UnproductiveTime -= sumNum;
                                if (imtable.UnproductiveTime <= 0)
                                {
                                    deleteIds.Add(imtable.ID);
                                }
                                else
                                {
                                    imtable.UnproductiveTime = imtable.UnproductiveTime;
                                    imtable.Modify(imtable.ID, _user.Name.ToString());
                                    imtableUnproductivetimesUpdate.Add(imtable);
                                }
                            }
                        }
                        
                    }
                    _unitOfWork.BeginTran();
                    bool subLotUp = true;
                    if (imtableUnproductivetimesUpdate.Count > 0)
                    {
                        subLotUp = await _dal.Update(imtableUnproductivetimesUpdate);
                    }
                    if (deleteIds.Count>0)
                    {
                        subLotUp = await _dal.DeleteByIds(deleteIds.ToArray());
                    }
                    _unitOfWork.CommitTran();
                    result.msg = "删除时同步同步修改辅助工时中间表" + "修改条数：" + imtableUnproductivetimesUpdate.Count+"删除条数"+ deleteIds.Count;
                    result.success = true;
                    return result;
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = ex.Message;
                return result;
                throw;
            }

            return result;
        }

       /* public async Task<bool> SyncDelData(string[] idList)
		{
			return true;

			if (idList != null)
            {
                List<ImtableUnproductivetimeEntity> delList = new List<ImtableUnproductivetimeEntity>();
                foreach (var item in idList)
                {
                    var whereUnproExpression = Expressionable.Create<UnproductiveTimeEntity>()
                                                           .And(p => p.ID.Equals(item))
                                                           .ToExpression();
                    var oldUnproModel = await _unPro.FindEntity(whereUnproExpression);
                    if (oldUnproModel != null)
                    {
                        var whereExpression = Expressionable.Create<ImtableUnproductivetimeEntity>()
                                 .And(p => p.Year == oldUnproModel.CreateDate.Year)
                                 .And(p => p.Month == oldUnproModel.CreateDate.Month)
                                 .And(p => p.Day == oldUnproModel.CreateDate.Day)
                                  .AndIF(!string.IsNullOrEmpty(oldUnproModel.LineId), p => p.LineId.Equals(oldUnproModel.LineId))
                                 .AndIF(!string.IsNullOrEmpty(oldUnproModel.ReasonGroupId), p => p.ReasonGroup.Equals(oldUnproModel.ReasonGroupId))
                                 .AndIF(!string.IsNullOrEmpty(oldUnproModel.ReasonId), p => p.Reason.Equals(oldUnproModel.ReasonId))
                                 .ToExpression();
                        var oldImpro = await _dal.FindEntity(whereExpression);
                        if (oldImpro != null)
                        {
                            oldImpro.UnproductiveTime-= oldUnproModel.ActualLaborMinutes;
                            delList.Add(oldImpro);
                        }
                        else 
                        {
                            return false;
                        }
                    }
                    else 
                    {
                        return false;
                    }
                }
                try
                {
                    _unitOfWork.BeginTran();
                    await _dal.Update(delList);
                    _unitOfWork.CommitTran();
                    return true;
                }
                catch(Exception e) 
                {
                  _unitOfWork.RollbackTran();
                  return false;
                }
            }
            else
            {
                return false;
            }
        }*/
        public async Task<bool> SaveForm(ImtableUnproductivetimeEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}