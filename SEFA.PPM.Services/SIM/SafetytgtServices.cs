
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using AutoMapper;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.Base.Common.WebApiClients.HttpApis;

namespace SEFA.PPM.Services
{
    public class SafetytgtServices : BaseServices<SafetytgtEntity>, ISafetytgtServices
    {
        private readonly IBaseRepository<SafetytgtEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _eq;
        private readonly IMapper _mapper;
        public SafetytgtServices(IBaseRepository<SafetytgtEntity> dal, IBaseRepository<EquipmentEntity> eq, IMapper mapper)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._eq = eq;
            _mapper = mapper;
        }

        public async Task<List<SafetytgtEntity>> GetList(SafetytgtRequestModel reqModel)
        {
            List<SafetytgtEntity> data = new List<SafetytgtEntity>();

                var whereExpression = Expressionable.Create<SafetytgtEntity>()
                     .AndIF(!string.IsNullOrEmpty(reqModel.EventType), p => p.EventType.Equals(reqModel.EventType))
                     .AndIF(!string.IsNullOrEmpty(reqModel.ModelRef), p => p.ModelRef.Equals(reqModel.ModelRef))
                     .AndIF(reqModel.StartDate.HasValue, p => p.DateOfOccurrence >= reqModel.StartDate.Value)
                     .AndIF(reqModel.EndDate.HasValue, p => p.DateOfOccurrence <= reqModel.EndDate.Value)
                     .ToExpression();
                data = await _dal.FindList(whereExpression);
            foreach (var item in data)
            {
                if (item.EventType.Equals("0"))
                {
                    item.EventType = "工伤";
                }
                else
                {
                    item.EventType = "外部投诉";
                }

            }

            return data;
        }
        //public async Task<List<object>> GetEventType()
        //{
        //    await Task.Delay(1000);
        //    List<object> data=new List<object>();
        //    for (int i = 0; i < 2; i++)
        //    {
        //        if (i == 0)
        //        {
        //            var obj = new
        //            {
        //                code = i.ToString(),
        //                data = "发生工伤的日期"
        //            };
        //            data.Add(obj);
        //        }
        //        else 
        //        {
        //            var obj = new
        //            {
        //                code = i.ToString(),
        //                data = "已确认外部投诉宗数"
        //            };
        //            data.Add(obj);
        //        }
        //    }
        //    return data;
        //}

        public async Task<PageModel<SafetytgtEntity>> GetPageList(SafetytgtRequestModel reqModel)
        {
            PageModel<SafetytgtEntity> data = new PageModel<SafetytgtEntity>();
            var whereExpression = Expressionable.Create<SafetytgtEntity>()
                  .AndIF(!string.IsNullOrEmpty(reqModel.EventType), p => p.EventType.Equals(reqModel.EventType))
                  .AndIF(!string.IsNullOrEmpty(reqModel.ModelRef), p => p.ModelRef.Equals(reqModel.ModelRef))
                  .AndIF(reqModel.StartDate.HasValue, p => p.DateOfOccurrence >= reqModel.StartDate.Value)
                  .AndIF(reqModel.EndDate.HasValue, p => p.DateOfOccurrence <= reqModel.EndDate.Value)
                  .ToExpression();
            data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            foreach (var item in data.data)
            {
                if (item.EventType.Equals("0"))
                {
                    item.EventType = "工伤";
                }
                else 
                {
                    item.EventType = "外部投诉";
                }

            }
            return data;
        }
        public async Task<bool> CheckModelRef(SafetytgtEntity entity)
        {

            var whereEqExpression = Expressionable.Create<EquipmentEntity>()
                 .ToExpression();
            var eqData = await _eq.FindList(whereEqExpression);
            var eqModel = eqData.Where(p => p.ID.Equals(entity.ModelRef)).FirstOrDefault();
            if (eqModel != null)
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        /// <summary>
        /// 看板安全信息统计/统计当年数据
        /// </summary>
        /// <returns></returns>
        public async Task<SafetMsgModel> GetSafetMsg(SafetytgtRequestModel reqModel)
        {
            //暂定默认ModelRef为现在编写方式，如不一样后续调整
            SafetMsgModel model = new SafetMsgModel();
            //获取当年日期
            DateTime startTime = new DateTime(DateTime.Now.Year, 1, 1).Date;
            DateTime endTime = new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59);
            var safeList = await _dal.FindList(p => p.DateOfOccurrence >= startTime && p.DateOfOccurrence <= endTime);
            var eqList = await _eq.FindList(p=>p.ID!=null && p.Deleted ==0);
            if (safeList.Count>0)
            {
                var datas = from a in safeList
                            join b in eqList on a.ModelRef equals b.ID
                            select new
                            {
                                a.ID,
                                b.EquipmentName,
                                a.EventType
                            };
                if (reqModel.type == 0)//制造车间对应看板
                {
                    
                    var data = datas.Where(p => p.EventType == "GS" && p.EquipmentName.Contains("COOK"));
                    var data1 = datas.Where(p => p.EventType == "TS" && p.EquipmentName.Contains("COOK"));
                    model.injury = data.Count();
                    model.complaint = data1.Count();
                }
                else
                {
                    var data = datas.Where(p => p.EventType == "GS" && p.EquipmentName.Contains("F&P") || p.EquipmentName.Contains("FIL") || p.EquipmentName.Contains("PAC"));
                    var data1 = datas.Where(p => p.EventType == "TS" && p.EquipmentName.Contains("F&P") || p.EquipmentName.Contains("FIL") || p.EquipmentName.Contains("PAC"));
                    model.injury = data.Count();
                    model.complaint = data1.Count();
                }
            }
           
            return model;
        }
        public async Task<bool> SaveForm(SafetytgtEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}