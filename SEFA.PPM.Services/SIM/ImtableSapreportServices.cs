
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.Interface;
using SEFA.Base.Common.Common;
using System;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Drawing;
using System.Reactive;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.DFM.Model.Models;
using System.Linq;
using static SEFA.PPM.Services.ImtableSapreportServices;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Net.NetworkInformation;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SEFA.Base.Common.Extensions;
using SEFA.Base;
using Microsoft.IdentityModel.Protocols.WsFed;
using System.Reflection;
using SEFA.Base.Model.Models;
using SEFA.PPM.Model.ViewModels.SIM.View;
using static SEFA.PPM.Services.ImtableRowmatLossServices;
using System.Data;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using SEFA.Base.Common.LogHelper;
using Abp.Specifications;
using SEFA.PPM.Model.ViewModels.SIM;

namespace SEFA.PPM.Services
{
	public class ImtableSapreportServices : BaseServices<ImtableSapreportEntity>, IImtableSapreportServices
	{
		private readonly IBaseRepository<ImtableSapreportEntity> _dal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IBaseRepository<EquipmentEntity> _EquipmentEntity;
		private readonly IBaseRepository<SappackorderEntity> _Sappackorderdal;
		private readonly IBaseRepository<SapPoRoutingEntity> _SapPoRoutingdal;
		private readonly IBaseRepository<ProductionOrderEntity> _productionOrderdal;
		private readonly IBaseRepository<PoProducedExecutionEntity> _proexedal;
		private readonly IBaseRepository<ImtableUnproductivetimeEntity> _ImtableUnproductivetimeEntity;
		private readonly IBaseRepository<ImtableOeeEntity> _ImtableOeeEntity;
		private readonly IBaseRepository<MonthlyYielViewEntity> _MonthlyYielViewEntity;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _PoSegmentRequirementdal;
		private readonly IBaseRepository<SapSegmentEntity> _SapSegmentdal;
		private readonly IBaseRepository<MaterialVersionEntity> _MaterialVersiondal;
		private readonly IBaseRepository<MaterialEntity> _Materialdal;
		private readonly IBaseRepository<SalescontainerEntity> _Salescontainerdal;
        private readonly IBaseRepository<PoProducedActualEntity> _dal2;
        private readonly IBaseRepository<PoProducedRequirementEntity> _dal4;
        private readonly IBaseRepository<ConfirmationEntity> _dal5;
        private readonly IBaseRepository<DowntimeEntity> _dal6; 
        private readonly IBaseRepository<DowntimeReasonEntity> _dal7; 
        private readonly IBaseRepository<DowntimeGroupEntity> _dal8; 
        private readonly IBaseRepository<PoConsumeActualEntity> _dal9; 

        public ImtableSapreportServices(IBaseRepository<ImtableSapreportEntity> dal, IUnitOfWork unitOfWork, IUser user, IBaseRepository<EquipmentEntity> EquipmentEntity, IBaseRepository<ImtableUnproductivetimeEntity> ImtableUnproductivetimeEntity, IBaseRepository<ImtableOeeEntity> ImtableOeeEntity, IBaseRepository<MonthlyYielViewEntity> MonthlyYielViewEntity, IBaseRepository<SappackorderEntity> sappackorderdal, IBaseRepository<ProductionOrderEntity> productionOrderdal, IBaseRepository<PoProducedExecutionEntity> proexedal, IBaseRepository<PoSegmentRequirementEntity> poSegmentRequirementdal, IBaseRepository<ConfirmationEntity> dal5, IBaseRepository<DowntimeEntity> dal6, IBaseRepository<DowntimeReasonEntity> dal7, IBaseRepository<DowntimeGroupEntity> dal8, IBaseRepository<PoConsumeActualEntity> dal9,IBaseRepository<SapSegmentEntity> sapSegmentdal, IBaseRepository<SapPoRoutingEntity> sapPoRoutingdal, IBaseRepository<PoProducedActualEntity> dal2, IBaseRepository<MaterialVersionEntity> materialVersiondal = null, IBaseRepository<MaterialEntity> materialdal = null, IBaseRepository<SalescontainerEntity> salescontainerdal = null)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			_user = user;
			_EquipmentEntity = EquipmentEntity;
			_ImtableUnproductivetimeEntity = ImtableUnproductivetimeEntity;
			_ImtableOeeEntity = ImtableOeeEntity;
			_MonthlyYielViewEntity = MonthlyYielViewEntity;
			_Sappackorderdal = sappackorderdal;
			_productionOrderdal = productionOrderdal;
			_proexedal = proexedal;
			_PoSegmentRequirementdal = poSegmentRequirementdal;
			_SapSegmentdal = sapSegmentdal;
			_SapPoRoutingdal = sapPoRoutingdal;
			_MaterialVersiondal = materialVersiondal;
			_Materialdal = materialdal;
			_Salescontainerdal = salescontainerdal;
			_dal2 = dal2;
			_dal5 = dal5;
			_dal6 = dal6;
			_dal7 = dal7;
			_dal8 = dal8;
            _dal9= dal9;
        }

		public async Task<List<ImtableSapreportEntity>> GetList(ImtableSapreportRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
							 .ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}

		public async Task<PageModel<ImtableSapreportEntity>> GetPageList(ImtableSapreportRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<MessageModel<string>> CreateImtableSapreport(string poexeId, int type)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var poProducedExecution = await _proexedal.FindEntity(poexeId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poProducedExecution";
				return result;
			}
			var productionOrder = await _productionOrderdal.FindEntity(poProducedExecution.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			var poSegmentRequirement = await _PoSegmentRequirementdal.FindEntity(poProducedExecution.PoSegmentRequirementId);
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement";
				return result;
			}
			var sapSegment = await _SapSegmentdal.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment";
				return result;
			}
			var sapPoRouting = await _SapPoRoutingdal.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo && x.Arbpl == sapSegment.SegmentName);
			if (sapPoRouting == null)
			{
				result.msg = "未找到sapPoRouting";
				return result;
			}
			var materialVersion = await _MaterialVersiondal.FindEntity(productionOrder.MaterialVersionId);
			if (materialVersion == null)
			{
				result.msg = "未找到materialVersion";
				return result;
			}
			var material = await _Materialdal.FindEntity(materialVersion.MaterialId);
			if (material == null)
			{
				result.msg = "未找到material";
				return result;
			}
			var addImtableSapreports = new List<ImtableSapreportEntity>();
			var updateImtableSapreports = new List<ImtableSapreportEntity>();
			var imtableSapreport = await _dal.FindEntity(x => x.ProductionOrderId == poProducedExecution.ProductionOrderId && x.WorkCenter == sapSegment.SegmentName);
			if (imtableSapreport == null)
			{
				imtableSapreport = new ImtableSapreportEntity();
				imtableSapreport.StartTime = poProducedExecution.StartTime.Value;
				//需要计算判断配方是否隔天生产（0 or 1），
				imtableSapreport.ProductionTime = 0;//需添加工作中心包含FlL的插入数据，不包含为空
				imtableSapreport.CreateCustomGuid(_user.Name);
				addImtableSapreports.Add(imtableSapreport);
			}
			else
			{
				imtableSapreport.ProductionTime = 0;
				imtableSapreport.Modify(imtableSapreport.ID, _user.Name);
				updateImtableSapreports.Add(imtableSapreport);
			}
			var firstRunOrder = (await _proexedal.FindList(x => x.ProductionOrderId == poProducedExecution.ProductionOrderId, x => x.StartTime)).FirstOrDefault();
			if (firstRunOrder != null)
			{
				imtableSapreport.Year = firstRunOrder.StartTime.Value.Year;
				imtableSapreport.Month = firstRunOrder.StartTime.Value.Month;
				imtableSapreport.Day = firstRunOrder.StartTime.Value.Day;
			}
                imtableSapreport.PlantId = sapPoRouting.Dwerk;
                imtableSapreport.PlantDecs = sapPoRouting.Dwerk == "2010" ? "李锦记新会三厂" : "";
                imtableSapreport.LineId = productionOrder.LineCode;
                imtableSapreport.PlanStartTime = productionOrder.PlanStartTime.Value;
                imtableSapreport.PlanEndTime = productionOrder.PlanEndTime.Value;
                imtableSapreport.ProductionOrderId = productionOrder.ProductionOrderNo;
                imtableSapreport.WorkCenter = sapSegment.SegmentName;
                imtableSapreport.CostCenter = sapPoRouting.Kostl;
                imtableSapreport.CostCenterDesc = "";
                //imtableSapreport.ReasonGroup = "";//需增加功能
                //imtableSapreport.Reason = "";
                imtableSapreport.MaterialId = material.Code;
                imtableSapreport.MaterialDecs = material.NAME;
                imtableSapreport.Formula = material.Description;
                imtableSapreport.FormulaType = material.Categorycode;
                imtableSapreport.FormulaGroup = material.Seriescode;
		    

			//原因大类和明细
            var GroupRenson = await _dal6.FindEntity(p => p.PoExecutionId == poexeId);
            if (GroupRenson != null)
            {
                var equipmentName = await _EquipmentEntity.FindEntity(GroupRenson.EquipmentId);
                
                if (equipmentName.EquipmentName.Contains("手工"))
                {
                    var renson = await _dal7.FindEntity(GroupRenson.ReasonId);
                    var group = await _dal8.FindEntity(renson.GroupId);
                    imtableSapreport.ReasonGroup = group.Description;
					imtableSapreport.Reason = renson.Description;
                }
            }

            //灌包装
            if (productionOrder.SapOrderType == "ZXH1")
			{
				var sappackorder = await _Sappackorderdal.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
				if (sappackorder == null)
				{
					result.msg = "未找到sappackorder";
					return result;
				}


                imtableSapreport.SalesContainer = sappackorder.Bezei;
				var salescontainer = await _Salescontainerdal.FindEntity(x => x.SalesContainer == sappackorder.Magrv/* && x.Description == sappackorder.Bezei*/);
				imtableSapreport.SalesContainerGroup = salescontainer?.ItemCode;
				imtableSapreport.MrpCode = sappackorder.Dispo;
				imtableSapreport.MrpDesc = "";//===========缺少数据源
				imtableSapreport.ActualConsumeQtyKg = 0;

                //找产出表
                var productionActual = await _dal2.FindList(x => x.ProductionOrderId == productionOrder.ID);
                var productionActual1= productionActual.GroupBy(p => new { pro = p.PoProducedRequirementId }).Select(p => new { pro = p.Key.pro, qty = p.Sum(t => t.Quantity) });
                if (productionActual == null)
                {
                    result.msg = "未找到productionActual";
                    return result;
                }
				//var productionQty =;
				//找订单工序生产需求
				var PoSegmentRequirementId = "";
				decimal? ActualQty = 0;
                foreach (var item in productionActual1)
                {
                    var PoProducedreport = await _dal4.FindEntity(x => x.ID == item.pro);
                    if (PoProducedreport == null)
                    {
                        result.msg = "未找到PoProducedreport";
                        return result;
                    }
					else
					{
						PoSegmentRequirementId = PoProducedreport.PoSegmentRequirementId;
						ActualQty = item.qty;
                    }
                    
                }

                //找SegmentRequirement
                var poSegmentRequirementActual = await _PoSegmentRequirementdal.FindEntity(PoSegmentRequirementId);
                if (poSegmentRequirementActual == null)
                {
                    result.msg = "未找到poSegmentRequirement";
                    return result;
                }
                //Segment工序（工作中心）
                var sapSegmentActual = await _SapSegmentdal.FindEntity(poSegmentRequirementActual.SegmentId);
                if (sapSegmentActual == null)
                {
                    result.msg = "未找到sapSegment";
                    return result;
                }
                var imtableSapreport2 = await _dal.FindEntity(x => x.ProductionOrderId == productionOrder.ID && x.WorkCenter == sapSegmentActual.SegmentName);
				if (imtableSapreport2==null)
				{
                    switch (sappackorder.Amein)
                    {
                        case "CAR":
                        case "TR":
                        case "DR":
                            imtableSapreport.PlanQtyBox = sappackorder.Psmng;
                            imtableSapreport.ActualQtyBox = ActualQty;
							imtableSapreport.PlatesNum = Math.Ceiling((decimal)ActualQty / sappackorder.Plqty);
							imtableSapreport.BoxPerPlate = sappackorder.Plqty;
                            break;
                        case "EA":
                            imtableSapreport.PlanQtyBottle = sappackorder.Psmng;
                            imtableSapreport.ActualQtyBottle = ActualQty;
                            imtableSapreport.BottleUnit = sappackorder.Amein;
                            break;
                        case "KG":
                            imtableSapreport.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport.ActualQtyKg = ActualQty;

                            imtableSapreport.ActualQtyTon = Math.Round(((ActualQty) ?? 0) / 1000, 3);
                            imtableSapreport.TonUnit = "吨";
                            break;
                        case "吨":
                            imtableSapreport.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport.ActualNetWeightKg = 0;
                            imtableSapreport.ActualQtyTon = ActualQty;
                            imtableSapreport.TonUnit = sappackorder.Amein;

                            imtableSapreport.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport.ActualQtyKg = ActualQty * 1000;

                            break;
                        default:
                            break;
                    }
                }
				else
				{
                    switch (sappackorder.Amein)
                    {
                        case "CAR":
                        case "TR":
                        case "DR":
                            imtableSapreport2.PlanQtyBox = sappackorder.Psmng;
                            imtableSapreport2.ActualQtyBox = ActualQty;//===========缺少逻辑
                            imtableSapreport2.PlatesNum = Math.Ceiling((decimal)ActualQty / sappackorder.Plqty);
                            imtableSapreport2.BoxPerPlate = sappackorder.Plqty;
                            break;
                        case "EA":
                            imtableSapreport2.PlanQtyBottle = sappackorder.Psmng;
                            imtableSapreport2.ActualQtyBottle = ActualQty;//===========缺少逻辑
                            imtableSapreport2.BottleUnit = sappackorder.Amein;
                            break;
                        case "KG":
                            imtableSapreport2.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport2.ActualQtyKg = ActualQty;//===========缺少逻辑
                                                             //imtableSapreport.KgUnit = sappackorder.Amein;

                            imtableSapreport2.ActualQtyTon = Math.Round(((ActualQty) ?? 0) / 1000, 3);
                            imtableSapreport2.TonUnit = "吨";
                            break;
                        case "吨":
                            //imtableSapreport2.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport2.ActualNetWeightKg = 0;//===========缺少逻辑
                            imtableSapreport2.ActualQtyTon = ActualQty;
                            imtableSapreport2.TonUnit = sappackorder.Amein;

                            imtableSapreport2.PlanQtyTon = sappackorder.Psmng;
                            imtableSapreport2.ActualQtyKg = ActualQty * 1000;//===========缺少逻辑

                            break;
                        default:
                            break;
                    }
                    imtableSapreport2.Modify(imtableSapreport2.ID, _user.Name);
                    updateImtableSapreports.Add(imtableSapreport2);
                }
            }
            //煮制
            else
			{
                imtableSapreport.MrpCode = "H23";
				var ActualConsumeQty = await _dal9.FindList(p => p.ProductionOrderId == poProducedExecution.ProductionOrderId);
				if (ActualConsumeQty.Count>0)
				{
					//工单实际投入重量
                    imtableSapreport.ActualConsumeQtyKg = ActualConsumeQty.Sum(p => p.Quantity);
                }
            }
           

            var ProExecution = await _proexedal.FindList(p=>p.RunEquipmentId== poProducedExecution.RunEquipmentId&&p.ID!= poexeId);
            var ProExecution1 = ProExecution.OrderByDescending(p => p.EndTime).FirstOrDefault();
            if (ProExecution == null)
            {
                result.msg = "未找到EquipmentId";
                return result;
            }
            var material1 = await _Materialdal.FindEntity(ProExecution1.MaterialId);
            if (material1 == null)
            {
                result.msg = "未找到material";
                return result;
            }
			if (material1.Description== material.Description)
			{
                imtableSapreport.OneTimeCompletion = 0;//连续生产为0
            }
			else
			{
                var ProExecutionOrder = await _proexedal.FindList(p => p.ProductionOrderId == poProducedExecution.ProductionOrderId);
                var ProExecutionOrder1 = ProExecutionOrder.OrderBy(p => p.StartTime).Select(P=>P.StartTime).FirstOrDefault();
                TimeSpan difference = (TimeSpan)(ProExecutionOrder1 - ProExecution1.EndTime);
                double hoursDifference = difference.TotalHours;
                if (hoursDifference>3)
                {
                    imtableSapreport.OneTimeCompletion = 0;//连续生产为0
                }
				else
				{
                    imtableSapreport.OneTimeCompletion = 1;//非连续生产为1
                }
            }
			//人时、机时
            var Confirmation = await _dal5.FindEntity(p => p.OrderId == poProducedExecution.ProductionOrderId &&p.SapSegmentId== poSegmentRequirement.SegmentId&&p.Status==1);
            if (Confirmation!=null)
			{
				if (imtableSapreport== null)
				{
                    imtableSapreport.LaborHour = Confirmation.CrewHours;//后续确认后补充
                    imtableSapreport.HourType1 = "LABOR";//人时类型
                    imtableSapreport.MachHour = Confirmation.Runtime;//后续确认后补充
                    imtableSapreport.HourType2 = "MACH";//机时类型
                    imtableSapreport.Status = 1;
                    imtableSapreport.CreateCustomGuid(_user.Name);
                    addImtableSapreports.Add(imtableSapreport);
                }
				else
				{
                    imtableSapreport.LaborHour = Confirmation.CrewHours;//后续确认后补充
                    imtableSapreport.HourType1 = "LABOR";//人时类型
                    imtableSapreport.MachHour = Confirmation.Runtime;//后续确认后补充
                    imtableSapreport.HourType2 = "MACH";//机时类型
                    imtableSapreport.Status = 1;
                    imtableSapreport.Modify(imtableSapreport.ID, _user.Name);
                    updateImtableSapreports.Add(imtableSapreport);
                }
            }
           
            var sappackorder1 = await _Sappackorderdal.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
            //判断订单号为20119开头的订单需填写（返工工单）
            if (productionOrder.ProductionOrderNo.StartsWith("20119"))
			{
				imtableSapreport.ReworkorderBatchId = sappackorder1.BatchFw;
				imtableSapreport.ReworkorderReworkqty = sappackorder1.Psmng;
				imtableSapreport.ReworkorderUnit = sappackorder1.Amein;
				imtableSapreport.ReworkorderAuthority = productionOrder.ReWorkDRI;
				imtableSapreport.ReworkorderType = productionOrder.ReWorkType;
				imtableSapreport.ReworkorderReason = productionOrder.ReWorkReason;
				imtableSapreport.ReworkorderMethod = productionOrder.ReWorkMathord;
				imtableSapreport.ReworkorderRemark = productionOrder.ReWorkRemark;
				//imtableSapreport.ActualTime = sappackorder1.Psmng;

            }
            ImtableSapreportEntity imtableSapreportEntity = new ImtableSapreportEntity();
			{
				//Year                          =null,            
				//Month							=null,
				//Day							=null,
				//PlantDecs						=null,
				//PlantId						=null,
				//LineId						=null,
				//PlanStartTime					=null,
				//PlanEndTime					=null,
				//StartTime						=null,
				//ProductionOrderId				=null,
				//ProductionTime				=null,
				//WorkCenter					=null,
				//CostCenter					=null,
				//CostCenterDesc				=null,
				//ReasonGroup					=null,
				//Reason						=null,
				//MaterialId					=null,
				//MaterialDecs					=null,
				//Formula						=null,
				//FormulaType					=null,
				//FormulaGroup					=null,
				//SalesContainer				=null,
				//SalesContainerGroup			=null,
				//MrpCode						=null,
				//MrpDesc						=null,
				//PlanQtyBox					=null,
				//ActualQtyBox					=null,
				//BoxUnit						=null,
				//PlanQtyBottle					=null,
				//ActualQtyBottle				=null,
				//BottleUnit					=null,
				//ActualConsumeQtyKg			=null,
				//PlanQtyTon					=null,
				//ActualNetWeightKg				=null,
				//ActualQtyKg					=null,
				//KgUnit						=null,
				//ActualQtyTon					=null,
				//TonUnit						=null,





				//OneTimeCompletion				=null,
				//LaborHour						=null,
				//HourType1						=null,
				//MachHour						=null,
				//HourType2						=null,
				//PlatesNum						=null,
				//BoxPerPlate					=null,

				//ReworkorderCreatedate			=null,
				//ReworkorderBatchId			=null,

				//ReworkorderReworkqty			=null,
				//ReworkorderUnit				=null,
				//ReworkorderAuthority			=null,
				//ReworkorderType				=null,
				//ReworkorderReason				=null,
				//ReworkorderMethod				=null,
				//ReworkorderRemark				=null,
				//ActualTime					=null,
			};
			addImtableSapreports.Add(imtableSapreportEntity);
			//var s = FAJsonConvert.ToJson(imtableSapreportEntity);
			_unitOfWork.BeginTran();
			try
			{
				if (addImtableSapreports?.Count > 0)
				{
					await _dal.Add(addImtableSapreports);
				}
				if (updateImtableSapreports?.Count > 0)
				{
					await _dal.Update(updateImtableSapreports);
				}
                _unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				_unitOfWork.RollbackTran();
				return result;
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		public async Task<bool> SaveForm(ImtableSapreportEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
        #region 产量明细表（产量+工时）

        public class ProductionInfoModels
        {

        };




        /// <summary>
        /// 产量明细存储过程
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="FormulaType"></param>
        /// <param name="WorkCenter"></param>
        /// <param name="LineName"></param>
        /// <param name="Specification"></param>
        /// <param name="ProductCategory"></param>
        /// <param name="SpeciCategory"></param>
        /// <returns></returns>
        public static string GetProductionInfoSql(DateTime startTime, DateTime endTime, string MaterialCode, string FormulaType, string WorkCenter, string LineName, string Specification, string ProductCategory, string SpeciCategory)
        {
            return string.Format("exec [dbo].[sp_Report_GetProductionOrderProductionInfoAndLabors] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}'", startTime, endTime, WorkCenter, LineName, MaterialCode, Specification, ProductCategory, SpeciCategory);
        }

    //    /// <summary>
    //    /// 产量明细表（产量+工时）
    //    /// </summary>
    //    /// <param name="reqModel"></param>
    //    /// <returns></returns>
    //    public async Task<PageModel<ImtableSapreportEntity>> ProductionInfo(ProductionInfoRequestModel reqModel)
    //    {
    //        PageModel<ImtableSapreportEntity> result = new PageModel<ImtableSapreportEntity>();
    //        RefAsync<int> dataCount = 0;
    //        List<ImtableSapreportEntity> imtableSapreportEntity = new List<ImtableSapreportEntity>();
    //        DateTime[] result1 = new DateTime[2];
    //        result1[0] = new DateTime(reqModel.Year, reqModel.Month, 1); // 年份开始时间：1月1日
    //        result1[1] = result1[0].AddMonths(1).AddDays(-1); // 年份结束时间：下一年的1月1日之前的最后一秒
    //        reqModel.startTime = result1[0];
    //        reqModel.endTime = result1[1];
    //        var sql = GetProductionInfoSql(reqModel.startTime, reqModel.endTime, reqModel.MaterialCode, reqModel.FormulaType, reqModel.WorkCenter, reqModel.LineName, reqModel.Specification, reqModel.ProductCategory, reqModel.SpeciCategory);
    //        var Model = await Task.Run(() =>
    //          _dal.Db.Ado.GetDataTable(sql)
    //        );

    //        foreach (var item in dataList)
    //        {

    //        }

    //        /*var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
				//.And(p => p.Year == reqModel.Year && p.Month == reqModel.Month)
				//			 .ToExpression();
    //        var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);*/
    //        var data = await _dal.FindList(p => p.Year == reqModel.Year && p.Month == reqModel.Month);
    //        foreach (var item in data)
    //        {
    //            var mtrdata = await _Materialdal.FindEntity(p => p.ID == item.MaterialId);
    //            if (mtrdata != null)
    //            {
    //                item.MaterialId = mtrdata.Code;
    //            }
    //            var linedata = await _EquipmentEntity.FindEntity(p => p.ID == item.LineId);
    //            if (linedata != null)
    //            {
    //                item.LineId = linedata.EquipmentName;
    //            }
    //            var WorkCenterdata = await _EquipmentEntity.FindEntity(p => p.ID == item.WorkCenter);
    //            if (linedata != null)
    //            {
    //                item.WorkCenter = WorkCenterdata.EquipmentName;
    //            }
    //            imtableSapreportEntity.Add(item);
    //        }
    //        //var data = await _dal.FindList(whereExpression);
    //        int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
    //        var dataList = imtableSapreportEntity.OrderBy(p => p.ReworkorderCreatedate).Skip(startIndex).Take(reqModel.pageSize).ToList();
    //        result.dataCount = imtableSapreportEntity.Count;
    //        result.data = dataList;
    //        return result;
    //        //return imtableSapreportEntity;
    //    }

        /// <summary>
        /// 产量明细表（产量+工时）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<ImtableSapreportEntity>> ProductionInfo(ImtableSapreportRequestModel reqModel)
        {
            PageModel<ImtableSapreportEntity> result = new PageModel<ImtableSapreportEntity>();
            RefAsync<int> dataCount = 0;
            List<ImtableSapreportEntity> imtableSapreportEntity = new List<ImtableSapreportEntity>();
            /*var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
				.And(p => p.Year == reqModel.Year && p.Month == reqModel.Month)
							 .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);*/
            var data = await _dal.FindList(p => p.Year == reqModel.Year && p.Month == reqModel.Month);
            foreach (var item in data)
            {
                var mtrdata = await _Materialdal.FindEntity(p => p.ID == item.MaterialId);
                if (mtrdata != null)
                {
                    item.MaterialId = mtrdata.Code;
                }
                var linedata = await _EquipmentEntity.FindEntity(p => p.ID == item.LineId);
                if (linedata != null)
                {
                    item.LineId = linedata.EquipmentName;
                }
                var WorkCenterdata = await _EquipmentEntity.FindEntity(p => p.ID == item.WorkCenter);
                if (linedata != null)
                {
                    item.WorkCenter = WorkCenterdata.EquipmentName;
                }
                imtableSapreportEntity.Add(item);
            }
            //var data = await _dal.FindList(whereExpression);
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = imtableSapreportEntity.OrderBy(p => p.ReworkorderCreatedate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = imtableSapreportEntity.Count;
            result.data = dataList;
            return result;
            //return imtableSapreportEntity;
        }
        #endregion

        /// <summary>
        /// 返工工单报表接收
        /// </summary>
        public class ReworkWorkOrderModels
        {
            public string PLAN_START_TIME { get; set; }
            public string PRODUCTION_ORDER_NO { get; set; }
            public string EQUIPMENT_NAME { get; set; }
            public string MaterialCode { get; set; }
            public string MaterialId { get; set; }
            public string MaterialName { get; set; }
            public decimal planQty { get; set; }
            public string Unit { get; set; }
            public string REWORKDRI { get; set; }
            public decimal actualQty { get; set; }
            public string REWORKTYPE { get; set; }
            public decimal laborHours { get; set; }
            public decimal machineHours { get; set; }
            public string Lot_No { get; set; }
            public string REWORKREASON { get; set; }
            public string REWORKMATHORD { get; set; }
            public string REWORKREMARK { get; set; }
            public string SEGMENT_CODE { get; set; }
        }

        /// <summary>
        /// 查询返工工单存储过程
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="FormulaType"></param>
        /// <param name="LineName"></param>
        /// <param name="WorkCenter"></param>
        /// <returns></returns>
        public static string GetMaterialLotSql(DateTime startTime, DateTime endTime, string MaterialCode, string FormulaType, string LineName, string WorkCenter)
        {
            return string.Format("exec [dbo].[sp_Report_GetReworkPOsInfomation] '{0}','{1}','{2}','{3}','{4}','{5}'", startTime, endTime,MaterialCode,FormulaType,LineName, WorkCenter);
        }

        /// <summary>
        /// 返工工单（产量+工时）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<ReworkWorkOrderModel>> ReworkWorkOrder(ReworkWorkOrderRequestModel reqModel)
		{
            PageModel<ReworkWorkOrderModel> result = new PageModel<ReworkWorkOrderModel>();
            RefAsync<int> dataCount = 0;
            DateTime[] result1 = new DateTime[2];
            result1[0] = new DateTime(reqModel.Year, reqModel.Month, 1); // 年份开始时间：1月1日
            //result1[1] = result1[0].AddYears(1).AddDays(-1); // 年份结束时间：下一年的1月1日之前的最后一秒
            result1[1] = result1[0].AddMonths(1).AddDays(-1); // 年份结束时间：下一年的1月1日之前的最后一秒
            reqModel.startTime = result1[0];
            reqModel.endTime = result1[1];
            var sql = GetMaterialLotSql(reqModel.startTime, reqModel.endTime, reqModel. MaterialCode, reqModel.FormulaType, reqModel.LineName, reqModel. WorkCenter);
            var Model = await Task.Run(() =>
              _dal.Db.Ado.GetDataTable(sql)
            );
            List<ReworkWorkOrderModel> reworkWorkOrders = new List<ReworkWorkOrderModel>();
            List<ReworkWorkOrderModels> data = new List<ReworkWorkOrderModels>();//有数据的分组

            foreach (DataRow item in Model.Rows)
            {
                ReworkWorkOrderModels reworkWorkOrder = new ReworkWorkOrderModels();
                reworkWorkOrder.PLAN_START_TIME = item["PLAN_START_TIME"].ToString();
                reworkWorkOrder.PRODUCTION_ORDER_NO = item["PRODUCTION_ORDER_NO"].ToString(); 
                reworkWorkOrder.EQUIPMENT_NAME = item["EQUIPMENT_NAME"].ToString(); 
                reworkWorkOrder.MaterialCode = item["MaterialCode"].ToString();
                reworkWorkOrder.MaterialId = item["MaterialId"].ToString();
                reworkWorkOrder.MaterialName = item["MaterialName"].ToString();
                reworkWorkOrder.planQty = Convert.ToDecimal(item["planQty"]);
                reworkWorkOrder.Unit = item["Unit"].ToString();
                reworkWorkOrder.REWORKDRI = item["REWORKDRI"].ToString();
                reworkWorkOrder.actualQty = item["actualQty"].ToString()==""?0: Convert.ToDecimal(item["actualQty"]);
                reworkWorkOrder.REWORKTYPE = item["REWORKTYPE"].ToString();
                reworkWorkOrder.Lot_No = item["Lot_No"].ToString();
                reworkWorkOrder.laborHours = item["laborHours"].ToString()==""?0:Convert.ToDecimal( item["laborHours"]);
                reworkWorkOrder.machineHours = item["machineHours"].ToString()==""?0: Convert.ToDecimal( item["machineHours"]);
                reworkWorkOrder.REWORKREASON = item["REWORKREASON"].ToString();
                reworkWorkOrder.REWORKMATHORD = item["REWORKMATHORD"].ToString();
                reworkWorkOrder.REWORKREMARK = item["REWORKREMARK"].ToString();
                reworkWorkOrder.SEGMENT_CODE = item["SEGMENT_CODE"].ToString();
                data.Add(reworkWorkOrder);
            }
			
            foreach (var item in data)
            {
                ReworkWorkOrderModel reworkWorkOrder = new ReworkWorkOrderModel();
				reworkWorkOrder.ReworkorderCreatedate = item.PLAN_START_TIME;
				var planStartTime = item.PLAN_START_TIME.ToString();
				if (planStartTime!="")
				{
                    reworkWorkOrder.Month =(Convert.ToDateTime( item.PLAN_START_TIME)).Month.ToString();
                }
				else
				{
					reworkWorkOrder.Month = "";
				}
				reworkWorkOrder.LineId = item.EQUIPMENT_NAME;
                reworkWorkOrder.ProductionOrderId = item.PRODUCTION_ORDER_NO;
				reworkWorkOrder.MaterialId = item.MaterialCode;
				reworkWorkOrder.MaterialDecs = item.MaterialName;
				reworkWorkOrder.ReworkorderBatchId =item.Lot_No;
				reworkWorkOrder.ReworkorderReworkqty = item.planQty;
                reworkWorkOrder.ReworkorderUnit = item.Unit;
				reworkWorkOrder.ReworkorderAuthority = item.REWORKDRI;
                reworkWorkOrder.ReworkorderType = item.REWORKTYPE;
				reworkWorkOrder.ReworkorderReason = item.REWORKREASON;
				reworkWorkOrder.ReworkorderMethod = item.REWORKMATHORD;
				reworkWorkOrder.ActualQtyBottle = item.actualQty;
				reworkWorkOrder.LaborHour = item.laborHours;
                reworkWorkOrder.HourType1 = "LABOR";
                reworkWorkOrder.MachHour =item.machineHours;
                reworkWorkOrder.HourType1 = "MACH";
                reworkWorkOrder.CostCenter = item.SEGMENT_CODE;
                reworkWorkOrder.ReworkorderRemark = item.REWORKREMARK;
				reworkWorkOrders.Add(reworkWorkOrder);
            }

            /*var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
				.And(p => p.Year == reqModel.Year && p.Month == reqModel.Month && p.ProductionOrderId.StartsWith("20119"))
				.ToExpression();
			//var data = await _dal.FindList(whereExpression);
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);*/
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = reworkWorkOrders.OrderBy(p => p.ReworkorderCreatedate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = reworkWorkOrders.Count;
            result.data = dataList;
            return result;
		}
		#region 12.3.5	单位人时劳动力 - 生产线采用效益表

		/// <summary>
		/// 人时部分
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<ManHourModel>> GetManHour(ImtableSapreportRequestModel reqModel)
		{

			//工时管去年和/输入查询年全部数据
			var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
					   .And(p => p.Year == reqModel.Year - 1 || p.Year == reqModel.Year)
					   .ToExpression();
			var data = await _dal.FindList(whereExpression);
			var datas1 = data.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, hour = p.Sum(t => t.LaborHour) }).ToList();

			//辅助工时去年和/输入查询年全部数据
			var whereExpression1 = Expressionable.Create<ImtableUnproductivetimeEntity>()
					   .And(p => p.Year == reqModel.Year - 1 || p.Year == reqModel.Year)
					   .ToExpression();
			var data1 = await _ImtableUnproductivetimeEntity.FindList(whereExpression1);
			var datas2 = data1.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, hour = p.Sum(t => t.UnproductiveTime) }).ToList();

			//停机去年和/输入查询年全部数据
			var whereExpression2 = Expressionable.Create<ImtableOeeEntity>()
					   .And(p => p.Year == reqModel.Year - 1 || p.Year == reqModel.Year)
					   .ToExpression();
			var data2 = await _ImtableOeeEntity.FindList(whereExpression2);
			var datas3 = data2.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, hour = p.Sum(t => t.TotalPlannedtime + t.TotalUnplannedtime) }).ToList();

			//包装车间下产线
			var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
			LineModel.OrderBy(p => p.EquipmentName).Select(p =>new { p.EquipmentName,p.ID }).ToList();
			List<ManHourModel> manHourModels = new List<ManHourModel>();
			foreach (var item in LineModel)
			{
                ManHourModel obj1 = new ManHourModel { };
                manHourModels.Add(obj1);

				#region 参数
				List<decimal> arr = new List<decimal>();
				List<decimal> arr1 = new List<decimal>();
				//汇总去年
				decimal Sumhour_s = 0;
				//汇总查询年
				decimal Sumhour_s1 = 0;

				#region VS人时5
				decimal Summ1 = 0;
				decimal Summ2 = 0;
				decimal Summ3 = 0;
				decimal Summ4 = 0;
				decimal Summ5 = 0;
				decimal Summ6 = 0;
				decimal Summ7 = 0;
				decimal Summ8 = 0;
				decimal Summ9 = 0;
				decimal Summ10 = 0;
				decimal Summ11 = 0;
				decimal Summ12 = 0;
				decimal Summ13 = 0;
				#endregion
				obj1.LineName = item.EquipmentName;
				#endregion
				//月份
				int month = 12;
                Type classType = typeof(ManHourModel);
                #region 去年

                for (int i = 1; i <= month; i++)
				{
                    PropertyInfo propertyInfo = classType.GetProperty("m" + i);
                    decimal hour = 0;
					decimal hour1 = 0;
					decimal hour2 = 0;
					//工时关联
					var model = datas1.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var items in model)
					{
						hour = items.hour;
					}

					//去年停机
					var timeModel = datas3.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var t in timeModel)
					{
						hour1 = t.hour;
					}

					//去年辅助工时
					var totalModel = datas2.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var d in totalModel)
					{
						hour2 = d.hour;
					}
                    decimal Sumhour = hour + hour1 + hour2;//单月
                    propertyInfo.SetValue(obj1, Sumhour);

					Sumhour_s += Sumhour;//去年汇总
					arr.Add(Sumhour);
                }
                obj1.m13 = Sumhour_s;
                #endregion

                #region 查询输入的年
                for (int j = 1; j <= month; j++)
				{
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    decimal hour = 0;
					decimal hour1 = 0;
					decimal hour2 = 0;
					//去年辅助工时
					var model = datas1.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var items in model)
					{
						hour = items.hour;
					}

					//去年停机
					var timeModel = datas3.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var t in timeModel)
					{
						hour1 = t.hour;
					}

					//去年辅助工时
					var totalModel = datas2.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var d in totalModel)
					{
						hour2 = d.hour;
					}

					decimal Sumhour = hour + hour1 + hour2;//单月
					propertyInfos.SetValue(obj1, Sumhour);
                    Sumhour_s1 += Sumhour;//输入年汇总
					arr1.Add(Sumhour);
				}
                obj1.s13 = Sumhour_s1;
                #endregion
                #region VS%
                if (arr[0] != 0)
				{
					Summ1 = Math.Round(arr1[0] / arr[0] - 1, 2);
				}
				if (arr[1] != 0)
				{
					Summ2 = Math.Round(arr1[1] / arr[1] - 1, 2);
				}
				if (arr[2] != 0)
				{
					Summ3 = Math.Round(arr1[2] / arr[2] - 1, 2);
				}
				if (arr[3] != 0)
				{
					Summ4 = Math.Round(arr1[3] / arr[3] - 1, 2);
				}
				if (arr[4] != 0)
				{
					Summ5 = Math.Round(arr1[4] / arr[4] - 1, 2);
				}
				if (arr[5] != 0)
				{
					Summ6 = Math.Round(arr1[5] / arr[5] - 1, 2);
				}
				if (arr[6] != 0)
				{
					Summ7 = Math.Round(arr1[6] / arr[6] - 1, 2);
				}
				if (arr[7] != 0)
				{
					Summ8 = Math.Round(arr1[7] / arr[7] - 1, 2);
				}
				if (arr[8] != 0)
				{
					Summ9 = Math.Round(arr1[8] / arr[8] - 1, 2);
				}
				if (arr[9] != 0)
				{
					Summ10 = Math.Round(arr1[9] / arr[9] - 1, 2);
				}
				if (arr[10] != 0)
				{
					Summ11 = Math.Round(arr1[10] / arr[10] - 1, 2);
				}
				if (arr[11] != 0)
				{
					Summ12 = Math.Round(arr1[11] / arr[11] - 1, 2);
				}
				if (Sumhour_s != 0)
				{
					Summ13 = Math.Round(Sumhour_s1 / Sumhour_s - 1, 2);
				}
				obj1.vs1 = Summ1;
				obj1.vs2 = Summ2;
				obj1.vs3 = Summ3;
				obj1.vs4 =Summ4;
				obj1.vs5 =Summ5;
				obj1.vs6 =Summ6;
				obj1.vs7 =Summ7;
				obj1.vs8 =Summ8;
				obj1.vs9 = Summ9;
                obj1.vs10 = Summ10;
                obj1.vs11 = Summ11; 
                obj1.vs12 = Summ12;
                obj1.vs13 = Summ13;
				#endregion
			}

			#region L12/L14/L19线总计行处理


			#region L12
			decimal months = 0;
			decimal month1 = 0;
			decimal month2 = 0;
			decimal month3 = 0;
			decimal month4 = 0;
			decimal month5 = 0;
			decimal month6 = 0;
			decimal month7 = 0;
			decimal month8 = 0;
			decimal month9 = 0;
			decimal month10 = 0;
			decimal month11 = 0;
			decimal month12 = 0;
			decimal month13 = 0;
			decimal month14 = 0;
			decimal month15 = 0;
			decimal month16 = 0;
			decimal month17 = 0;
			decimal month18 = 0;
			decimal month19 = 0;
			decimal month20 = 0;
			decimal month21 = 0;
			decimal month22 = 0;
			decimal month23 = 0;
			decimal month24 = 0;
			decimal month25 = 0;
			//VS
			decimal month26 = 0;
			decimal month27 = 0;
			decimal month28 = 0;
			decimal month29 = 0;
			decimal month30 = 0;
			decimal month31 = 0;
			decimal month32 = 0;
			decimal month33 = 0;
			decimal month34 = 0;
			decimal month35 = 0;
			decimal month36 = 0;
			decimal month37 = 0;
			decimal month38 = 0;

			#endregion

			#region L14
			decimal months_1 = 0;
			decimal months_2 = 0;
			decimal months_3 = 0;
			decimal months_4 = 0;
			decimal months_5 = 0;
			decimal months_6 = 0;
			decimal months_7 = 0;
			decimal months_8 = 0;
			decimal months_9 = 0;
			decimal months_10 = 0;
			decimal months_11 = 0;
			decimal months_12 = 0;
			decimal months_13 = 0;
			decimal months_14 = 0;
			decimal months_15 = 0;
			decimal months_16 = 0;
			decimal months_17 = 0;
			decimal months_18 = 0;
			decimal months_19 = 0;
			decimal months_20 = 0;
			decimal months_21 = 0;
			decimal months_22 = 0;
			decimal months_23 = 0;
			decimal months_24 = 0;
			decimal months_25 = 0;
			decimal months_26 = 0;
			//VS
			decimal months_27 = 0;
			decimal months_28 = 0;
			decimal months_29 = 0;
			decimal months_30 = 0;
			decimal months_31 = 0;
			decimal months_32 = 0;
			decimal months_33 = 0;
			decimal months_34 = 0;
			decimal months_35 = 0;
			decimal months_36 = 0;
			decimal months_37 = 0;
			decimal months_38 = 0;
			decimal months_39 = 0;

			#endregion

			#region L19
			decimal monthss_1 = 0;
			decimal monthss_2 = 0;
			decimal monthss_3 = 0;
			decimal monthss_4 = 0;
			decimal monthss_5 = 0;
			decimal monthss_6 = 0;
			decimal monthss_7 = 0;
			decimal monthss_8 = 0;
			decimal monthss_9 = 0;
			decimal monthss_10 = 0;
			decimal monthss_11 = 0;
			decimal monthss_12 = 0;
			decimal monthss_13 = 0;
			decimal monthss_14 = 0;
			decimal monthss_15 = 0;
			decimal monthss_16 = 0;
			decimal monthss_17 = 0;
			decimal monthss_18 = 0;
			decimal monthss_19 = 0;
			decimal monthss_20 = 0;
			decimal monthss_21 = 0;
			decimal monthss_22 = 0;
			decimal monthss_23 = 0;
			decimal monthss_24 = 0;
			decimal monthss_25 = 0;
			decimal monthss_26 = 0;
			//VS
			decimal monthss_27 = 0;
			decimal monthss_28 = 0;
			decimal monthss_29 = 0;
			decimal monthss_30 = 0;
			decimal monthss_31 = 0;
			decimal monthss_32 = 0;
			decimal monthss_33 = 0;
			decimal monthss_34 = 0;
			decimal monthss_35 = 0;
			decimal monthss_36 = 0;
			decimal monthss_37 = 0;
			decimal monthss_38 = 0;
			decimal monthss_39 = 0;

			#endregion

			for (int i = 0; i < manHourModels.Count; i++)
			{
				var line = manHourModels[i].LineName;
				if (line.Contains("L12"))
				{
					months += Convert.ToDecimal(manHourModels[i].m1);
					month1 += Convert.ToDecimal(manHourModels[i].m2);
					month2 += Convert.ToDecimal(manHourModels[i].m3);
					month3 += Convert.ToDecimal(manHourModels[i].m4);
					month4 += Convert.ToDecimal(manHourModels[i].m5);
					month5 += Convert.ToDecimal(manHourModels[i].m6);
					month6 += Convert.ToDecimal(manHourModels[i].m7);
					month7 += Convert.ToDecimal(manHourModels[i].m8);
					month8 += Convert.ToDecimal(manHourModels[i].m9);
					month9 += Convert.ToDecimal(manHourModels[i].m10);
					month10 += Convert.ToDecimal(manHourModels[i].m11);
					month11 += Convert.ToDecimal(manHourModels[i].m12);
					month12 += Convert.ToDecimal(manHourModels[i].m13);

					month13 += Convert.ToDecimal(manHourModels[i].s1);
					month14 += Convert.ToDecimal(manHourModels[i].s2);
					month15 += Convert.ToDecimal(manHourModels[i].s3);
					month16 += Convert.ToDecimal(manHourModels[i].s4);
					month17 += Convert.ToDecimal(manHourModels[i].s5);
					month18 += Convert.ToDecimal(manHourModels[i].s6);
					month19 += Convert.ToDecimal(manHourModels[i].s7);
					month20 += Convert.ToDecimal(manHourModels[i].s8);
					month21 += Convert.ToDecimal(manHourModels[i].s9);
					month22 += Convert.ToDecimal(manHourModels[i].s10);
					month23 += Convert.ToDecimal(manHourModels[i].s11);
					month24 += Convert.ToDecimal(manHourModels[i].s12);
					month25 += Convert.ToDecimal(manHourModels[i].s13);
				}

				if (line.Contains("L14"))
				{
					months_1 += Convert.ToDecimal(manHourModels[i].m1);
					months_2 += Convert.ToDecimal(manHourModels[i].m2);
					months_3 += Convert.ToDecimal(manHourModels[i].m3);
					months_4 += Convert.ToDecimal(manHourModels[i].m4);
					months_5 += Convert.ToDecimal(manHourModels[i].m5);
					months_6 += Convert.ToDecimal(manHourModels[i].m6);
					months_7 += Convert.ToDecimal(manHourModels[i].m7);
					months_8 += Convert.ToDecimal(manHourModels[i].m8);
					months_9 += Convert.ToDecimal(manHourModels[i].m9);
					months_10 += Convert.ToDecimal(manHourModels[i].m10);
					months_11 += Convert.ToDecimal(manHourModels[i].m11);
					months_12 += Convert.ToDecimal(manHourModels[i].m12);
					months_13 += Convert.ToDecimal(manHourModels[i].m13);

					months_14 += Convert.ToDecimal(manHourModels[i].s1);
					months_15 += Convert.ToDecimal(manHourModels[i].s2);
					months_16 += Convert.ToDecimal(manHourModels[i].s3);
					months_17 += Convert.ToDecimal(manHourModels[i].s4);
					months_18 += Convert.ToDecimal(manHourModels[i].s5);
					months_19 += Convert.ToDecimal(manHourModels[i].s6);
					months_20 += Convert.ToDecimal(manHourModels[i].s7);
					months_21 += Convert.ToDecimal(manHourModels[i].s8);
					months_22 += Convert.ToDecimal(manHourModels[i].s9);
					months_23 += Convert.ToDecimal(manHourModels[i].s10);
					months_24 += Convert.ToDecimal(manHourModels[i].s11);
					months_25 += Convert.ToDecimal(manHourModels[i].s12);
					months_26 += Convert.ToDecimal(manHourModels[i].s13);
				}

				if (line.Contains("L19"))
				{
					monthss_1 += Convert.ToDecimal(manHourModels[i].m1);
					monthss_2 += Convert.ToDecimal(manHourModels[i].m2);
					monthss_3 += Convert.ToDecimal(manHourModels[i].m3);
					monthss_4 += Convert.ToDecimal(manHourModels[i].m4);
					monthss_5 += Convert.ToDecimal(manHourModels[i].m5);
					monthss_6 += Convert.ToDecimal(manHourModels[i].m6);
					monthss_7 += Convert.ToDecimal(manHourModels[i].m7);
					monthss_8 += Convert.ToDecimal(manHourModels[i].m8);
					monthss_9 += Convert.ToDecimal(manHourModels[i].m9);
					monthss_10 += Convert.ToDecimal(manHourModels[i].m10);
					monthss_11 += Convert.ToDecimal(manHourModels[i].m11);
					monthss_12 += Convert.ToDecimal(manHourModels[i].m12);
					monthss_13 += Convert.ToDecimal(manHourModels[i].m13);

					monthss_14 += Convert.ToDecimal(manHourModels[i].s1);
					monthss_15 += Convert.ToDecimal(manHourModels[i].s2);
					monthss_16 += Convert.ToDecimal(manHourModels[i].s3);
					monthss_17 += Convert.ToDecimal(manHourModels[i].s4);
					monthss_18 += Convert.ToDecimal(manHourModels[i].s5);
					monthss_19 += Convert.ToDecimal(manHourModels[i].s6);
					monthss_20 += Convert.ToDecimal(manHourModels[i].s7);
					monthss_21 += Convert.ToDecimal(manHourModels[i].s8);
					monthss_22 += Convert.ToDecimal(manHourModels[i].s9);
					monthss_23 += Convert.ToDecimal(manHourModels[i].s10);
					monthss_24 += Convert.ToDecimal(manHourModels[i].s11);
					monthss_25 += Convert.ToDecimal(manHourModels[i].s12);
					monthss_26 += Convert.ToDecimal(manHourModels[i].s13);
				}
			}
			#region L12/L14/L19线总计

			#region L12VS
			if (months != 0)
			{
				month26 = Math.Round(month13 / months - 1);
			}
			if (month1 != 0)
			{
				month27 = Math.Round(month14 / month1 - 1);
			}
			if (month2 != 0)
			{
				month28 = Math.Round(month15 / month2 - 1);
			}
			if (month3 != 0)
			{
				month29 = Math.Round(month16 / month3 - 1);
			}
			if (month4 != 0)
			{
				month30 = Math.Round(month17 / month4 - 1);
			}
			if (month5 != 0)
			{
				month31 = Math.Round(month18 / month5 - 1);
			}
			if (month6 != 0)
			{
				month32 = Math.Round(month19 / month6 - 1);
			}
			if (month7 != 0)
			{
				month33 = Math.Round(month20 / month7 - 1);
			}
			if (month8 != 0)
			{
				month34 = Math.Round(month21 / month8 - 1);
			}
			if (month9 != 0)
			{
				month35 = Math.Round(month22 / month9 - 1);
			}
			if (month10 != 0)
			{
				month36 = Math.Round(month23 / month10 - 1);
			}
			if (month11 != 0)
			{
				month37 = Math.Round(month24 / month11 - 1);
			}
			if (month12 != 0)
			{
				month38 = Math.Round(month25 / month12 - 1);
			}

			#endregion
			#region L14VS

			if (months_1 != 0)
			{
				months_27 = months_14 / months_1 - 1;
			}
			if (months_2 != 0)
			{
				months_28 = months_15 / months_2 - 1;
			}
			if (months_3 != 0)
			{
				months_29 = months_16 / months_3 - 1;
			}
			if (months_4 != 0)
			{
				months_30 = months_17 / months_4 - 1;
			}
			if (months_5 != 0)
			{
				months_31 = months_18 / months_5 - 1;
			}
			if (months_6 != 0)
			{
				months_32 = months_19 / months_6 - 1;
			}
			if (months_7 != 0)
			{
				months_33 = months_20 / months_7 - 1;
			}
			if (months_8 != 0)
			{
				months_34 = months_21 / months_8 - 1;
			}
			if (months_9 != 0)
			{
				months_35 = months_22 / months_9 - 1;
			}
			if (months_10 != 0)
			{
				months_36 = months_23 / months_10 - 1;
			}
			if (months_11 != 0)
			{
				months_37 = months_24 / months_11 - 1;
			}
			if (months_12 != 0)
			{
				months_38 = months_25 / months_12 - 1;
			}
			if (months_13 != 0)
			{
				months_39 = months_26 / months_13 - 1;
			}

			#endregion
			#region L19VS
			if (monthss_1 != 0)
			{
				monthss_27 = monthss_14 / monthss_1 - 1;
			}
			if (monthss_2 != 0)
			{
				monthss_28 = monthss_15 / monthss_2 - 1;
			}
			if (monthss_3 != 0)
			{
				monthss_29 = monthss_16 / monthss_3 - 1;
			}
			if (monthss_4 != 0)
			{
				monthss_30 = monthss_17 / monthss_4 - 1;
			}
			if (monthss_5 != 0)
			{
				monthss_31 = monthss_18 / monthss_5 - 1;
			}
			if (monthss_6 != 0)
			{
				monthss_32 = monthss_19 / monthss_6 - 1;
			}
			if (monthss_7 != 0)
			{
				monthss_33 = monthss_20 / monthss_7 - 1;
			}
			if (monthss_8 != 0)
			{
				monthss_34 = monthss_21 / monthss_8 - 1;
			}
			if (monthss_9 != 0)
			{
				monthss_35 = monthss_22 / monthss_9 - 1;
			}
			if (monthss_10 != 0)
			{
				monthss_36 = monthss_23 / monthss_10 - 1;
			}
			if (monthss_11 != 0)
			{
				monthss_37 = monthss_24 / monthss_11 - 1;
			}
			if (monthss_12 != 0)
			{
				monthss_38 = monthss_25 / monthss_12 - 1;
			}
			if (monthss_13 != 0)
			{
				monthss_39 = monthss_26 / monthss_13 - 1;
			}
            #endregion
            ManHourModel obj2 = new ManHourModel { };
            manHourModels.Add(obj2);
            #region 组合L12合并
			obj2.LineName="L12总计";
			obj2.m1=months;
			obj2.m2=month1;
			obj2.m3=month2;
			obj2.m4=month3;
			obj2.m5=month4;
			obj2.m6=month5;
			obj2.m7=month6;
			obj2.m8=month7;
			obj2.m9=month8;
			obj2.m10=month9;
			obj2.m11=month10;
			obj2.m12=month11;
			obj2.m13=month12;
			obj2.s1=month13;
			obj2.s2=month14;
			obj2.s3=month15;
			obj2.s4=month16;
			obj2.s5=month17;
			obj2.s6=month18;
			obj2.s7=month19;
			obj2.s8=month20;
			obj2.s9=month21;
			obj2.s10=month22;
			obj2.s11=month23;
			obj2.s12=month24;
			obj2.s13=month25;
			obj2.vs1=month26;
			obj2.vs2=month27;
			obj2.vs3=month28;
			obj2.vs4=month29;
			obj2.vs5=month30;
			obj2.vs6=month31;
			obj2.vs7=month32;
			obj2.vs8=month33;
			obj2.vs9=month34;
			obj2.vs10=month35;
			obj2.vs11=month36;
			obj2.vs12=month37;
            obj2.vs13=month38;
            #endregion
            #region 组合L14合并
            ManHourModel obj3 = new ManHourModel { };
            manHourModels.Add(obj3);
            obj3.LineName="L14总计";
            obj3.m1=months_1;
			obj3.m2=months_2;
			obj3.m3=months_3;
			obj3.m4=months_4;
			obj3.m5=months_5;
			obj3.m6=months_6;
			obj3.m7=months_7;
			obj3.m8=months_8;
			obj3.m9=months_9;
			obj3.m10=months_10;
			obj3.m11=months_11;
			obj3.m12=months_12;
			obj3.m13=months_13;

			obj3.s1=months_14;
			obj3.s2=months_15;
			obj3.s3=months_16;
			obj3.s4=months_17;
			obj3.s5=months_18;
			obj3.s6=months_19;
			obj3.s7=months_20;
			obj3.s8=months_21;
			obj3.s9=months_22;
			obj3.s10=months_23;
			obj3.s11=months_24;
			obj3.s12=months_25;
			obj3.s13=months_26;

			obj3.vs1=months_27;
			obj3.vs2=months_28;
			obj3.vs3=months_29;
			obj3.vs4=months_30;
			obj3.vs5=months_31;
			obj3.vs6=months_32;
			obj3.vs7=months_33;
			obj3.vs8=months_34;
			obj3.vs9=months_35;
			obj3.vs10=months_36;
			obj3.vs11=months_37;
			obj3.vs12=months_38;
			obj3.vs13=months_39;
            #endregion
            #region 组合L19合并
            ManHourModel obj4 = new ManHourModel { };
            manHourModels.Add(obj4);
			obj4.LineName="L12总计";
			obj4.m1=monthss_1;
			obj4.m2=monthss_2;
			obj4.m3=monthss_3;
			obj4.m4=monthss_4;
			obj4.m5=monthss_5;
			obj4.m6=monthss_6;
			obj4.m7=monthss_7;
			obj4.m8=monthss_8;
			obj4.m9=monthss_9;
			obj4.m10=monthss_10;
			obj4.m11=monthss_11;
			obj4.m12=monthss_12;
			obj4.m13=monthss_13;

			obj4.s1=monthss_14;
			obj4.s2=monthss_15;
			obj4.s3=monthss_16;
			obj4.s4=monthss_17;
			obj4.s5=monthss_18;
			obj4.s6=monthss_19;
			obj4.s7=monthss_20;
			obj4.s8=monthss_21;
			obj4.s9=monthss_22;
			obj4.s10=monthss_23;
			obj4.s11=monthss_24;
			obj4.s12=monthss_25;
			obj4.s13=monthss_26;

			obj4.vs1=monthss_27;
			obj4.vs2=monthss_28;
			obj4.vs3=monthss_29;
			obj4.vs4=monthss_30;
			obj4.vs5=monthss_31;
			obj4.vs6=monthss_32;
			obj4.vs7=monthss_33;
			obj4.vs8=monthss_34;
			obj4.vs9=monthss_35;
			obj4.vs10=monthss_36;
			obj4.vs11=monthss_37;
			obj4.vs12=monthss_38;
            obj4.vs13=monthss_39;
			#endregion
			#endregion
			#endregion


			return manHourModels;
		}

		/// <summary>
		/// 产量部分
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<ManHourModel>> GetProduction(ImtableSapreportRequestModel reqModel)
		{

			//工时管去年和/输入查询年全部数据
			var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
					   .And(p => p.Year == reqModel.Year - 1 || p.Year == reqModel.Year)
					   .ToExpression();
			var data = await _dal.FindList(whereExpression);
			var datas1 = data.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, QtyTon = p.Sum(t => t.ActualQtyTon) }).ToList();
			//包装车间下产线
			var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
			LineModel.OrderBy(p => p.EquipmentName).Select(p => new { p.EquipmentName,p.ID }).ToList();
			List<ManHourModel> manHourModels = new List<ManHourModel>();
			foreach (var item in LineModel)
			{
                ManHourModel obj1 = new ManHourModel { };
                manHourModels.Add(obj1);
                #region 参数
                List<decimal> arr = new List<decimal>();
				List<decimal> arr1 = new List<decimal>();
				//汇总去年
				decimal Sumhour_s = 0;
				//汇总查询年
				decimal Sumhour_s1 = 0;

				#region VS人时
				decimal Summ1 = 0;
				decimal Summ2 = 0;
				decimal Summ3 = 0;
				decimal Summ4 = 0;
				decimal Summ5 = 0;
				decimal Summ6 = 0;
				decimal Summ7 = 0;
				decimal Summ8 = 0;
				decimal Summ9 = 0;
				decimal Summ10 = 0;
				decimal Summ11 = 0;
				decimal Summ12 = 0;
				decimal Summ13 = 0;
				#endregion
				obj1.LineName = item.EquipmentName;
				#endregion
				//月份
				int month = 12;
                Type classType = typeof(ManHourModel);
                #region 去年
                for (int i = 1; i <= month; i++)
				{
                    PropertyInfo propertyInfo = classType.GetProperty("m" + i);
                    decimal hour = 0;
					//产量
					var model = datas1.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var items in model)
					{
						hour = Convert.ToDecimal(items.QtyTon);
					}
					Sumhour_s += hour;//去年汇总
					arr.Add(hour);
					propertyInfo.SetValue(obj1, hour);
				}
				obj1.m13 = Sumhour_s;
                #endregion

                #region 查询输入的年
                for (int j = 1; j <= month; j++)
				{
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    decimal hour = 0;
					//去年产量工时
					var model = datas1.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var items in model)
					{
						hour = Convert.ToDecimal(items.QtyTon);
					}
					Sumhour_s1 += hour;//查询年汇总
					arr1.Add(hour);
					propertyInfos.SetValue(obj1,hour);
				}
                obj1.m13 = Sumhour_s1;
                #endregion

                #region VS%
                if (arr[0] != 0)
				{
					Summ1 = Math.Round(arr1[0] / arr[0] - 1, 2);
				}
				if (arr[1] != 0)
				{
					Summ2 = Math.Round(arr1[1] / arr[1] - 1, 2);
				}
				if (arr[2] != 0)
				{
					Summ3 = Math.Round(arr1[2] / arr[2] - 1, 2);
				}
				if (arr[3] != 0)
				{
					Summ4 = Math.Round(arr1[3] / arr[3] - 1, 2);
				}
				if (arr[4] != 0)
				{
					Summ5 = Math.Round(arr1[4] / arr[4] - 1, 2);
				}
				if (arr[5] != 0)
				{
					Summ6 = Math.Round(arr1[5] / arr[5] - 1, 2);
				}
				if (arr[6] != 0)
				{
					Summ7 = Math.Round(arr1[6] / arr[6] - 1, 2);
				}
				if (arr[7] != 0)
				{
					Summ8 = Math.Round(arr1[7] / arr[7] - 1, 2);
				}
				if (arr[8] != 0)
				{
					Summ9 = Math.Round(arr1[8] / arr[8] - 1, 2);
				}
				if (arr[9] != 0)
				{
					Summ10 = Math.Round(arr1[9] / arr[9] - 1, 2);
				}
				if (arr[10] != 0)
				{
					Summ11 = Math.Round(arr1[10] / arr[10] - 1, 2);
				}
				if (arr[11] != 0)
				{
					Summ12 = Math.Round(arr1[11] / arr[11] - 1, 2);
				}
				if (Sumhour_s != 0)
				{
					Summ13 = Math.Round(Sumhour_s1 / Sumhour_s - 1, 2);
				}
                obj1.vs1 = Summ1;
                obj1.vs2 = Summ2;
                obj1.vs3 = Summ3;
                obj1.vs4 = Summ4;
                obj1.vs5 = Summ5;
                obj1.vs6 = Summ6;
                obj1.vs7 = Summ7;
                obj1.vs8 = Summ8;
                obj1.vs9 = Summ9;
                obj1.vs10 = Summ10;
                obj1.vs11 = Summ11;
                obj1.vs12 = Summ12;
                obj1.vs13 = Summ13;
                #endregion
            }

			#region L12/L14/L19线总计行处理

			#region L12
			decimal months = 0;
			decimal month1 = 0;
			decimal month2 = 0;
			decimal month3 = 0;
			decimal month4 = 0;
			decimal month5 = 0;
			decimal month6 = 0;
			decimal month7 = 0;
			decimal month8 = 0;
			decimal month9 = 0;
			decimal month10 = 0;
			decimal month11 = 0;
			decimal month12 = 0;
			decimal month13 = 0;
			decimal month14 = 0;
			decimal month15 = 0;
			decimal month16 = 0;
			decimal month17 = 0;
			decimal month18 = 0;
			decimal month19 = 0;
			decimal month20 = 0;
			decimal month21 = 0;
			decimal month22 = 0;
			decimal month23 = 0;
			decimal month24 = 0;
			decimal month25 = 0;
			//VS
			decimal month26 = 0;
			decimal month27 = 0;
			decimal month28 = 0;
			decimal month29 = 0;
			decimal month30 = 0;
			decimal month31 = 0;
			decimal month32 = 0;
			decimal month33 = 0;
			decimal month34 = 0;
			decimal month35 = 0;
			decimal month36 = 0;
			decimal month37 = 0;
			decimal month38 = 0;

			#endregion

			#region L14
			decimal months_1 = 0;
			decimal months_2 = 0;
			decimal months_3 = 0;
			decimal months_4 = 0;
			decimal months_5 = 0;
			decimal months_6 = 0;
			decimal months_7 = 0;
			decimal months_8 = 0;
			decimal months_9 = 0;
			decimal months_10 = 0;
			decimal months_11 = 0;
			decimal months_12 = 0;
			decimal months_13 = 0;
			decimal months_14 = 0;
			decimal months_15 = 0;
			decimal months_16 = 0;
			decimal months_17 = 0;
			decimal months_18 = 0;
			decimal months_19 = 0;
			decimal months_20 = 0;
			decimal months_21 = 0;
			decimal months_22 = 0;
			decimal months_23 = 0;
			decimal months_24 = 0;
			decimal months_25 = 0;
			decimal months_26 = 0;
			//VS
			decimal months_27 = 0;
			decimal months_28 = 0;
			decimal months_29 = 0;
			decimal months_30 = 0;
			decimal months_31 = 0;
			decimal months_32 = 0;
			decimal months_33 = 0;
			decimal months_34 = 0;
			decimal months_35 = 0;
			decimal months_36 = 0;
			decimal months_37 = 0;
			decimal months_38 = 0;
			decimal months_39 = 0;

			#endregion

			#region L19
			decimal monthss_1 = 0;
			decimal monthss_2 = 0;
			decimal monthss_3 = 0;
			decimal monthss_4 = 0;
			decimal monthss_5 = 0;
			decimal monthss_6 = 0;
			decimal monthss_7 = 0;
			decimal monthss_8 = 0;
			decimal monthss_9 = 0;
			decimal monthss_10 = 0;
			decimal monthss_11 = 0;
			decimal monthss_12 = 0;
			decimal monthss_13 = 0;
			decimal monthss_14 = 0;
			decimal monthss_15 = 0;
			decimal monthss_16 = 0;
			decimal monthss_17 = 0;
			decimal monthss_18 = 0;
			decimal monthss_19 = 0;
			decimal monthss_20 = 0;
			decimal monthss_21 = 0;
			decimal monthss_22 = 0;
			decimal monthss_23 = 0;
			decimal monthss_24 = 0;
			decimal monthss_25 = 0;
			decimal monthss_26 = 0;
			//VS
			decimal monthss_27 = 0;
			decimal monthss_28 = 0;
			decimal monthss_29 = 0;
			decimal monthss_30 = 0;
			decimal monthss_31 = 0;
			decimal monthss_32 = 0;
			decimal monthss_33 = 0;
			decimal monthss_34 = 0;
			decimal monthss_35 = 0;
			decimal monthss_36 = 0;
			decimal monthss_37 = 0;
			decimal monthss_38 = 0;
			decimal monthss_39 = 0;

			#endregion

			for (int i = 0; i < manHourModels.Count; i++)
			{
				var line = manHourModels[i].LineName;
                if (line.Contains("L12"))
                {
                    months += Convert.ToDecimal(manHourModels[i].m1);
                    month1 += Convert.ToDecimal(manHourModels[i].m2);
                    month2 += Convert.ToDecimal(manHourModels[i].m3);
                    month3 += Convert.ToDecimal(manHourModels[i].m4);
                    month4 += Convert.ToDecimal(manHourModels[i].m5);
                    month5 += Convert.ToDecimal(manHourModels[i].m6);
                    month6 += Convert.ToDecimal(manHourModels[i].m7);
                    month7 += Convert.ToDecimal(manHourModels[i].m8);
                    month8 += Convert.ToDecimal(manHourModels[i].m9);
                    month9 += Convert.ToDecimal(manHourModels[i].m10);
                    month10 += Convert.ToDecimal(manHourModels[i].m11);
                    month11 += Convert.ToDecimal(manHourModels[i].m12);
                    month12 += Convert.ToDecimal(manHourModels[i].m13);

                    month13 += Convert.ToDecimal(manHourModels[i].s1);
                    month14 += Convert.ToDecimal(manHourModels[i].s2);
                    month15 += Convert.ToDecimal(manHourModels[i].s3);
                    month16 += Convert.ToDecimal(manHourModels[i].s4);
                    month17 += Convert.ToDecimal(manHourModels[i].s5);
                    month18 += Convert.ToDecimal(manHourModels[i].s6);
                    month19 += Convert.ToDecimal(manHourModels[i].s7);
                    month20 += Convert.ToDecimal(manHourModels[i].s8);
                    month21 += Convert.ToDecimal(manHourModels[i].s9);
                    month22 += Convert.ToDecimal(manHourModels[i].s10);
                    month23 += Convert.ToDecimal(manHourModels[i].s11);
                    month24 += Convert.ToDecimal(manHourModels[i].s12);
                    month25 += Convert.ToDecimal(manHourModels[i].s13);
                }

                if (line.Contains("L14"))
                {
                    months_1 += Convert.ToDecimal(manHourModels[i].m1);
                    months_2 += Convert.ToDecimal(manHourModels[i].m2);
                    months_3 += Convert.ToDecimal(manHourModels[i].m3);
                    months_4 += Convert.ToDecimal(manHourModels[i].m4);
                    months_5 += Convert.ToDecimal(manHourModels[i].m5);
                    months_6 += Convert.ToDecimal(manHourModels[i].m6);
                    months_7 += Convert.ToDecimal(manHourModels[i].m7);
                    months_8 += Convert.ToDecimal(manHourModels[i].m8);
                    months_9 += Convert.ToDecimal(manHourModels[i].m9);
                    months_10 += Convert.ToDecimal(manHourModels[i].m10);
                    months_11 += Convert.ToDecimal(manHourModels[i].m11);
                    months_12 += Convert.ToDecimal(manHourModels[i].m12);
                    months_13 += Convert.ToDecimal(manHourModels[i].m13);

                    months_14 += Convert.ToDecimal(manHourModels[i].s1);
                    months_15 += Convert.ToDecimal(manHourModels[i].s2);
                    months_16 += Convert.ToDecimal(manHourModels[i].s3);
                    months_17 += Convert.ToDecimal(manHourModels[i].s4);
                    months_18 += Convert.ToDecimal(manHourModels[i].s5);
                    months_19 += Convert.ToDecimal(manHourModels[i].s6);
                    months_20 += Convert.ToDecimal(manHourModels[i].s7);
                    months_21 += Convert.ToDecimal(manHourModels[i].s8);
                    months_22 += Convert.ToDecimal(manHourModels[i].s9);
                    months_23 += Convert.ToDecimal(manHourModels[i].s10);
                    months_24 += Convert.ToDecimal(manHourModels[i].s11);
                    months_25 += Convert.ToDecimal(manHourModels[i].s12);
                    months_26 += Convert.ToDecimal(manHourModels[i].s13);
                }

                if (line.Contains("L19"))
                {
                    monthss_1 += Convert.ToDecimal(manHourModels[i].m1);
                    monthss_2 += Convert.ToDecimal(manHourModels[i].m2);
                    monthss_3 += Convert.ToDecimal(manHourModels[i].m3);
                    monthss_4 += Convert.ToDecimal(manHourModels[i].m4);
                    monthss_5 += Convert.ToDecimal(manHourModels[i].m5);
                    monthss_6 += Convert.ToDecimal(manHourModels[i].m6);
                    monthss_7 += Convert.ToDecimal(manHourModels[i].m7);
                    monthss_8 += Convert.ToDecimal(manHourModels[i].m8);
                    monthss_9 += Convert.ToDecimal(manHourModels[i].m9);
                    monthss_10 += Convert.ToDecimal(manHourModels[i].m10);
                    monthss_11 += Convert.ToDecimal(manHourModels[i].m11);
                    monthss_12 += Convert.ToDecimal(manHourModels[i].m12);
                    monthss_13 += Convert.ToDecimal(manHourModels[i].m13);

                    monthss_14 += Convert.ToDecimal(manHourModels[i].s1);
                    monthss_15 += Convert.ToDecimal(manHourModels[i].s2);
                    monthss_16 += Convert.ToDecimal(manHourModels[i].s3);
                    monthss_17 += Convert.ToDecimal(manHourModels[i].s4);
                    monthss_18 += Convert.ToDecimal(manHourModels[i].s5);
                    monthss_19 += Convert.ToDecimal(manHourModels[i].s6);
                    monthss_20 += Convert.ToDecimal(manHourModels[i].s7);
                    monthss_21 += Convert.ToDecimal(manHourModels[i].s8);
                    monthss_22 += Convert.ToDecimal(manHourModels[i].s9);
                    monthss_23 += Convert.ToDecimal(manHourModels[i].s10);
                    monthss_24 += Convert.ToDecimal(manHourModels[i].s11);
                    monthss_25 += Convert.ToDecimal(manHourModels[i].s12);
                    monthss_26 += Convert.ToDecimal(manHourModels[i].s13);
                }
            }
			#region L12/L14/L19线总计

			#region L12VS
			if (months != 0)
			{
				month26 = Math.Round(month13 / months - 1);
			}
			if (month1 != 0)
			{
				month27 = Math.Round(month14 / month1 - 1);
			}
			if (month2 != 0)
			{
				month28 = Math.Round(month15 / month2 - 1);
			}
			if (month3 != 0)
			{
				month29 = Math.Round(month16 / month3 - 1);
			}
			if (month4 != 0)
			{
				month30 = Math.Round(month17 / month4 - 1);
			}
			if (month5 != 0)
			{
				month31 = Math.Round(month18 / month5 - 1);
			}
			if (month6 != 0)
			{
				month32 = Math.Round(month19 / month6 - 1);
			}
			if (month7 != 0)
			{
				month33 = Math.Round(month20 / month7 - 1);
			}
			if (month8 != 0)
			{
				month34 = Math.Round(month21 / month8 - 1);
			}
			if (month9 != 0)
			{
				month35 = Math.Round(month22 / month9 - 1);
			}
			if (month10 != 0)
			{
				month36 = Math.Round(month23 / month10 - 1);
			}
			if (month11 != 0)
			{
				month37 = Math.Round(month24 / month11 - 1);
			}
			if (month12 != 0)
			{
				month38 = Math.Round(month25 / month12 - 1);
			}














			#endregion
			#region L14VS

			if (months_1 != 0)
			{
				months_27 = months_14 / months_1 - 1;
			}
			if (months_2 != 0)
			{
				months_28 = months_15 / months_2 - 1;
			}
			if (months_3 != 0)
			{
				months_29 = months_16 / months_3 - 1;
			}
			if (months_4 != 0)
			{
				months_30 = months_17 / months_4 - 1;
			}
			if (months_5 != 0)
			{
				months_31 = months_18 / months_5 - 1;
			}
			if (months_6 != 0)
			{
				months_32 = months_19 / months_6 - 1;
			}
			if (months_7 != 0)
			{
				months_33 = months_20 / months_7 - 1;
			}
			if (months_8 != 0)
			{
				months_34 = months_21 / months_8 - 1;
			}
			if (months_9 != 0)
			{
				months_35 = months_22 / months_9 - 1;
			}
			if (months_10 != 0)
			{
				months_36 = months_23 / months_10 - 1;
			}
			if (months_11 != 0)
			{
				months_37 = months_24 / months_11 - 1;
			}
			if (months_12 != 0)
			{
				months_38 = months_25 / months_12 - 1;
			}
			if (months_13 != 0)
			{
				months_39 = months_26 / months_13 - 1;
			}

			#endregion
			#region L19VS
			if (monthss_1 != 0)
			{
				monthss_27 = monthss_14 / monthss_1 - 1;
			}
			if (monthss_2 != 0)
			{
				monthss_28 = monthss_15 / monthss_2 - 1;
			}
			if (monthss_3 != 0)
			{
				monthss_29 = monthss_16 / monthss_3 - 1;
			}
			if (monthss_4 != 0)
			{
				monthss_30 = monthss_17 / monthss_4 - 1;
			}
			if (monthss_5 != 0)
			{
				monthss_31 = monthss_18 / monthss_5 - 1;
			}
			if (monthss_6 != 0)
			{
				monthss_32 = monthss_19 / monthss_6 - 1;
			}
			if (monthss_7 != 0)
			{
				monthss_33 = monthss_20 / monthss_7 - 1;
			}
			if (monthss_8 != 0)
			{
				monthss_34 = monthss_21 / monthss_8 - 1;
			}
			if (monthss_9 != 0)
			{
				monthss_35 = monthss_22 / monthss_9 - 1;
			}
			if (monthss_10 != 0)
			{
				monthss_36 = monthss_23 / monthss_10 - 1;
			}
			if (monthss_11 != 0)
			{
				monthss_37 = monthss_24 / monthss_11 - 1;
			}
			if (monthss_12 != 0)
			{
				monthss_38 = monthss_25 / monthss_12 - 1;
			}
			if (monthss_13 != 0)
			{
				monthss_39 = monthss_26 / monthss_13 - 1;
			}
            #endregion


            #region 组合L12合并
            ManHourModel obj2 = new ManHourModel { };
            manHourModels.Add(obj2);
            obj2.LineName = "L12总计";
            obj2.m1 = months;
            obj2.m2 = month1;
            obj2.m3 = month2;
            obj2.m4 = month3;
            obj2.m5 = month4;
            obj2.m6 = month5;
            obj2.m7 = month6;
            obj2.m8 = month7;
            obj2.m9 = month8;
            obj2.m10 = month9;
            obj2.m11 = month10;
            obj2.m12 = month11;
            obj2.m13 = month12;
            obj2.s1 = month13;
            obj2.s2 = month14;
            obj2.s3 = month15;
            obj2.s4 = month16;
            obj2.s5 = month17;
            obj2.s6 = month18;
            obj2.s7 = month19;
            obj2.s8 = month20;
            obj2.s9 = month21;
            obj2.s10 = month22;
            obj2.s11 = month23;
            obj2.s12 = month24;
            obj2.s13 = month25;
            obj2.vs1 = month26;
            obj2.vs2 = month27;
            obj2.vs3 = month28;
            obj2.vs4 = month29;
            obj2.vs5 = month30;
            obj2.vs6 = month31;
            obj2.vs7 = month32;
            obj2.vs8 = month33;
            obj2.vs9 = month34;
            obj2.vs10 = month35;
            obj2.vs11 = month36;
            obj2.vs12 = month37;
            obj2.vs13 = month38;
            #endregion
            #region 组合L14合并
            ManHourModel obj3 = new ManHourModel { };
            manHourModels.Add(obj3);
            obj3.LineName = "L14总计";
            obj3.m1 = months_1;
            obj3.m2 = months_2;
            obj3.m3 = months_3;
            obj3.m4 = months_4;
            obj3.m5 = months_5;
            obj3.m6 = months_6;
            obj3.m7 = months_7;
            obj3.m8 = months_8;
            obj3.m9 = months_9;
            obj3.m10 = months_10;
            obj3.m11 = months_11;
            obj3.m12 = months_12;
            obj3.m13 = months_13;

            obj3.s1 = months_14;
            obj3.s2 = months_15;
            obj3.s3 = months_16;
            obj3.s4 = months_17;
            obj3.s5 = months_18;
            obj3.s6 = months_19;
            obj3.s7 = months_20;
            obj3.s8 = months_21;
            obj3.s9 = months_22;
            obj3.s10 = months_23;
            obj3.s11 = months_24;
            obj3.s12 = months_25;
            obj3.s13 = months_26;

            obj3.vs1 = months_27;
            obj3.vs2 = months_28;
            obj3.vs3 = months_29;
            obj3.vs4 = months_30;
            obj3.vs5 = months_31;
            obj3.vs6 = months_32;
            obj3.vs7 = months_33;
            obj3.vs8 = months_34;
            obj3.vs9 = months_35;
            obj3.vs10 = months_36;
            obj3.vs11 = months_37;
            obj3.vs12 = months_38;
            obj3.vs13 = months_39;
            #endregion
            #region 组合L19合并
            ManHourModel obj4 = new ManHourModel { };
            manHourModels.Add(obj4);
            obj4.LineName = "L12总计";
            obj4.m1 = monthss_1;
            obj4.m2 = monthss_2;
            obj4.m3 = monthss_3;
            obj4.m4 = monthss_4;
            obj4.m5 = monthss_5;
            obj4.m6 = monthss_6;
            obj4.m7 = monthss_7;
            obj4.m8 = monthss_8;
            obj4.m9 = monthss_9;
            obj4.m10 = monthss_10;
            obj4.m11 = monthss_11;
            obj4.m12 = monthss_12;
            obj4.m13 = monthss_13;

            obj4.s1 = monthss_14;
            obj4.s2 = monthss_15;
            obj4.s3 = monthss_16;
            obj4.s4 = monthss_17;
            obj4.s5 = monthss_18;
            obj4.s6 = monthss_19;
            obj4.s7 = monthss_20;
            obj4.s8 = monthss_21;
            obj4.s9 = monthss_22;
            obj4.s10 = monthss_23;
            obj4.s11 = monthss_24;
            obj4.s12 = monthss_25;
            obj4.s13 = monthss_26;

            obj4.vs1 = monthss_27;
            obj4.vs2 = monthss_28;
            obj4.vs3 = monthss_29;
            obj4.vs4 = monthss_30;
            obj4.vs5 = monthss_31;
            obj4.vs6 = monthss_32;
            obj4.vs7 = monthss_33;
            obj4.vs8 = monthss_34;
            obj4.vs9 = monthss_35;
            obj4.vs10 = monthss_36;
            obj4.vs11 = monthss_37;
            obj4.vs12 = monthss_38;
            obj4.vs13 = monthss_39;
            #endregion
            #endregion
            #endregion
            return manHourModels;
		}

		/// <summary>
		/// 生产力部分
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<ManHourModel>> GetProductivity(ImtableSapreportRequestModel reqModel)
		{

			//去年和/输入查询年全部数据
			var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
					   .And(p => p.Year == reqModel.Year - 1 || p.Year == reqModel.Year)
					   .ToExpression();
			var data = await _dal.FindList(whereExpression);
			var datas1 = data.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, hour = p.Sum(t => t.LaborHour) }).ToList();

			var datas2 = data.GroupBy(p => new { p.Year, p.Month, p.LineId }).Select(p => new { year = p.Key.Year, month = p.Key.Month, lineid = p.Key.LineId, Qtykg = p.Sum(t => t.ActualQtyKg) }).ToList();

			//包装车间下产线
			var LineModel = await _EquipmentEntity.FindList(p => p.ParentId == "02405071-5072-0299-163e-0370f6000000" && p.Deleted == 0);
			LineModel.OrderBy(p => p.EquipmentName).Select(p => new { p.EquipmentName, p.ID }).ToList();
			List<ManHourModel> manHourModels = new List<ManHourModel>();
			foreach (var item in LineModel)
			{
                ManHourModel obj1 = new ManHourModel { };
                manHourModels.Add(obj1);

                #region 参数
                List<decimal> arr = new List<decimal>();
				List<decimal> arr1 = new List<decimal>();
				//汇总去年
				decimal Sumhour_s = 0;
				//汇总查询年
				decimal Sumhour_s1 = 0;

				#region VS人时
				decimal Summ1 = 0;
				decimal Summ2 = 0;
				decimal Summ3 = 0;
				decimal Summ4 = 0;
				decimal Summ5 = 0;
				decimal Summ6 = 0;
				decimal Summ7 = 0;
				decimal Summ8 = 0;
				decimal Summ9 = 0;
				decimal Summ10 = 0;
				decimal Summ11 = 0;
				decimal Summ12 = 0;
				decimal Summ13 = 0;
                #endregion
                obj1.LineName = item.EquipmentName;
                #endregion
                //月份
                int month = 12;
                Type classType = typeof(ManHourModel);
                #region 去年
                for (int i = 1; i <= month; i++)
				{
                    PropertyInfo propertyInfo = classType.GetProperty("m" + i);
                    decimal hour = 0;
					decimal hour1 = 0;
					//人时
					var timeModel = datas1.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var items in timeModel)
					{
						hour = items.hour;
					}

					//生产重量
					var qtyModel = datas2.Where(p => p.month == i && p.year == reqModel.Year - 1 && p.lineid == item.ID);
					foreach (var t in qtyModel)
					{
						hour1 = Convert.ToDecimal(t.Qtykg);
					}
					decimal Sumhour = 0;
					if (hour != 0)
					{
						Sumhour = hour1 / hour;//单月
					}

					Sumhour_s += Sumhour;//去年汇总
					arr.Add(Sumhour);
                    propertyInfo.SetValue(obj1, Sumhour);
				}
				#endregion

				#region 查询输入的年
				for (int j = 1; j <= month; j++)
				{
                    PropertyInfo propertyInfos = classType.GetProperty("s" + j);
                    decimal hour = 0;
					decimal hour1 = 0;
					//人时
					var timeModel = datas1.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var items in timeModel)
					{
						hour = items.hour;
					}

					//生产重量
					var qtyModel = datas2.Where(p => p.month == j && p.year == reqModel.Year && p.lineid == item.ID);
					foreach (var d in qtyModel)
					{
						hour1 = Convert.ToDecimal(d.Qtykg);
					}
					decimal Sumhour = 0;
					if (hour != 0)
					{
						Sumhour = hour1 / hour;//单月
					}
					Sumhour_s1 += Sumhour;//去年汇总
					arr1.Add(Sumhour);
                    propertyInfos.SetValue(obj1, Sumhour);
                }
				#endregion

				#region VS%
				if (arr[0] != 0)
				{
					Summ1 = Math.Round(arr1[0] / arr[0] - 1, 2);
				}
				if (arr[1] != 0)
				{
					Summ2 = Math.Round(arr1[1] / arr[1] - 1, 2);
				}
				if (arr[2] != 0)
				{
					Summ3 = Math.Round(arr1[2] / arr[2] - 1, 2);
				}
				if (arr[3] != 0)
				{
					Summ4 = Math.Round(arr1[3] / arr[3] - 1, 2);
				}
				if (arr[4] != 0)
				{
					Summ5 = Math.Round(arr1[4] / arr[4] - 1, 2);
				}
				if (arr[5] != 0)
				{
					Summ6 = Math.Round(arr1[5] / arr[5] - 1, 2);
				}
				if (arr[6] != 0)
				{
					Summ7 = Math.Round(arr1[6] / arr[6] - 1, 2);
				}
				if (arr[7] != 0)
				{
					Summ8 = Math.Round(arr1[7] / arr[7] - 1, 2);
				}
				if (arr[8] != 0)
				{
					Summ9 = Math.Round(arr1[8] / arr[8] - 1, 2);
				}
				if (arr[9] != 0)
				{
					Summ10 = Math.Round(arr1[9] / arr[9] - 1, 2);
				}
				if (arr[10] != 0)
				{
					Summ11 = Math.Round(arr1[10] / arr[10] - 1, 2);
				}
				if (arr[11] != 0)
				{
					Summ12 = Math.Round(arr1[11] / arr[11] - 1, 2);
				}
				if (Sumhour_s != 0)
				{
					Summ13 = Math.Round(Sumhour_s1 / Sumhour_s - 1, 2);
				}
                obj1.vs1 = Summ1;
                obj1.vs2 = Summ2;
                obj1.vs3 = Summ3;
                obj1.vs4 = Summ4;
                obj1.vs5 = Summ5;
                obj1.vs6 = Summ6;
                obj1.vs7 = Summ7;
                obj1.vs8 = Summ8;
                obj1.vs9 = Summ9;
                obj1.vs10 = Summ10;
                obj1.vs11 = Summ11;
                obj1.vs12 = Summ12;
                obj1.vs13 = Summ13;
                #endregion
                obj1.vs1 = Summ1;
                obj1.vs2 = Summ2;
                obj1.vs3 = Summ3;
                obj1.vs4 = Summ4;
                obj1.vs5 = Summ5;
                obj1.vs6 = Summ6;
                obj1.vs7 = Summ7;
                obj1.vs8 = Summ8;
                obj1.vs9 = Summ9;
                obj1.vs10 = Summ10;
                obj1.vs11 = Summ11;
                obj1.vs12 = Summ12;
                obj1.vs13 = Summ13;
            }

			#region L12/L14/L19线总计行处理


			#region L12
			decimal months = 0;
			decimal month1 = 0;
			decimal month2 = 0;
			decimal month3 = 0;
			decimal month4 = 0;
			decimal month5 = 0;
			decimal month6 = 0;
			decimal month7 = 0;
			decimal month8 = 0;
			decimal month9 = 0;
			decimal month10 = 0;
			decimal month11 = 0;
			decimal month12 = 0;
			decimal month13 = 0;
			decimal month14 = 0;
			decimal month15 = 0;
			decimal month16 = 0;
			decimal month17 = 0;
			decimal month18 = 0;
			decimal month19 = 0;
			decimal month20 = 0;
			decimal month21 = 0;
			decimal month22 = 0;
			decimal month23 = 0;
			decimal month24 = 0;
			decimal month25 = 0;
			//VS
			decimal month26 = 0;
			decimal month27 = 0;
			decimal month28 = 0;
			decimal month29 = 0;
			decimal month30 = 0;
			decimal month31 = 0;
			decimal month32 = 0;
			decimal month33 = 0;
			decimal month34 = 0;
			decimal month35 = 0;
			decimal month36 = 0;
			decimal month37 = 0;
			decimal month38 = 0;

			#endregion
			#region L14
			decimal months_1 = 0;
			decimal months_2 = 0;
			decimal months_3 = 0;
			decimal months_4 = 0;
			decimal months_5 = 0;
			decimal months_6 = 0;
			decimal months_7 = 0;
			decimal months_8 = 0;
			decimal months_9 = 0;
			decimal months_10 = 0;
			decimal months_11 = 0;
			decimal months_12 = 0;
			decimal months_13 = 0;
			decimal months_14 = 0;
			decimal months_15 = 0;
			decimal months_16 = 0;
			decimal months_17 = 0;
			decimal months_18 = 0;
			decimal months_19 = 0;
			decimal months_20 = 0;
			decimal months_21 = 0;
			decimal months_22 = 0;
			decimal months_23 = 0;
			decimal months_24 = 0;
			decimal months_25 = 0;
			decimal months_26 = 0;
			//VS
			decimal months_27 = 0;
			decimal months_28 = 0;
			decimal months_29 = 0;
			decimal months_30 = 0;
			decimal months_31 = 0;
			decimal months_32 = 0;
			decimal months_33 = 0;
			decimal months_34 = 0;
			decimal months_35 = 0;
			decimal months_36 = 0;
			decimal months_37 = 0;
			decimal months_38 = 0;
			decimal months_39 = 0;

			#endregion
			#region L19
			decimal monthss_1 = 0;
			decimal monthss_2 = 0;
			decimal monthss_3 = 0;
			decimal monthss_4 = 0;
			decimal monthss_5 = 0;
			decimal monthss_6 = 0;
			decimal monthss_7 = 0;
			decimal monthss_8 = 0;
			decimal monthss_9 = 0;
			decimal monthss_10 = 0;
			decimal monthss_11 = 0;
			decimal monthss_12 = 0;
			decimal monthss_13 = 0;
			decimal monthss_14 = 0;
			decimal monthss_15 = 0;
			decimal monthss_16 = 0;
			decimal monthss_17 = 0;
			decimal monthss_18 = 0;
			decimal monthss_19 = 0;
			decimal monthss_20 = 0;
			decimal monthss_21 = 0;
			decimal monthss_22 = 0;
			decimal monthss_23 = 0;
			decimal monthss_24 = 0;
			decimal monthss_25 = 0;
			decimal monthss_26 = 0;
			//VS
			decimal monthss_27 = 0;
			decimal monthss_28 = 0;
			decimal monthss_29 = 0;
			decimal monthss_30 = 0;
			decimal monthss_31 = 0;
			decimal monthss_32 = 0;
			decimal monthss_33 = 0;
			decimal monthss_34 = 0;
			decimal monthss_35 = 0;
			decimal monthss_36 = 0;
			decimal monthss_37 = 0;
			decimal monthss_38 = 0;
			decimal monthss_39 = 0;

			#endregion

			for (int i = 0; i < manHourModels.Count; i++)
			{
				var line = manHourModels[i].LineName;
                if (line.Contains("L12"))
                {
                    months += Convert.ToDecimal(manHourModels[i].m1);
                    month1 += Convert.ToDecimal(manHourModels[i].m2);
                    month2 += Convert.ToDecimal(manHourModels[i].m3);
                    month3 += Convert.ToDecimal(manHourModels[i].m4);
                    month4 += Convert.ToDecimal(manHourModels[i].m5);
                    month5 += Convert.ToDecimal(manHourModels[i].m6);
                    month6 += Convert.ToDecimal(manHourModels[i].m7);
                    month7 += Convert.ToDecimal(manHourModels[i].m8);
                    month8 += Convert.ToDecimal(manHourModels[i].m9);
                    month9 += Convert.ToDecimal(manHourModels[i].m10);
                    month10 += Convert.ToDecimal(manHourModels[i].m11);
                    month11 += Convert.ToDecimal(manHourModels[i].m12);
                    month12 += Convert.ToDecimal(manHourModels[i].m13);

                    month13 += Convert.ToDecimal(manHourModels[i].s1);
                    month14 += Convert.ToDecimal(manHourModels[i].s2);
                    month15 += Convert.ToDecimal(manHourModels[i].s3);
                    month16 += Convert.ToDecimal(manHourModels[i].s4);
                    month17 += Convert.ToDecimal(manHourModels[i].s5);
                    month18 += Convert.ToDecimal(manHourModels[i].s6);
                    month19 += Convert.ToDecimal(manHourModels[i].s7);
                    month20 += Convert.ToDecimal(manHourModels[i].s8);
                    month21 += Convert.ToDecimal(manHourModels[i].s9);
                    month22 += Convert.ToDecimal(manHourModels[i].s10);
                    month23 += Convert.ToDecimal(manHourModels[i].s11);
                    month24 += Convert.ToDecimal(manHourModels[i].s12);
                    month25 += Convert.ToDecimal(manHourModels[i].s13);
                }

                if (line.Contains("L14"))
                {
                    months_1 += Convert.ToDecimal(manHourModels[i].m1);
                    months_2 += Convert.ToDecimal(manHourModels[i].m2);
                    months_3 += Convert.ToDecimal(manHourModels[i].m3);
                    months_4 += Convert.ToDecimal(manHourModels[i].m4);
                    months_5 += Convert.ToDecimal(manHourModels[i].m5);
                    months_6 += Convert.ToDecimal(manHourModels[i].m6);
                    months_7 += Convert.ToDecimal(manHourModels[i].m7);
                    months_8 += Convert.ToDecimal(manHourModels[i].m8);
                    months_9 += Convert.ToDecimal(manHourModels[i].m9);
                    months_10 += Convert.ToDecimal(manHourModels[i].m10);
                    months_11 += Convert.ToDecimal(manHourModels[i].m11);
                    months_12 += Convert.ToDecimal(manHourModels[i].m12);
                    months_13 += Convert.ToDecimal(manHourModels[i].m13);

                    months_14 += Convert.ToDecimal(manHourModels[i].s1);
                    months_15 += Convert.ToDecimal(manHourModels[i].s2);
                    months_16 += Convert.ToDecimal(manHourModels[i].s3);
                    months_17 += Convert.ToDecimal(manHourModels[i].s4);
                    months_18 += Convert.ToDecimal(manHourModels[i].s5);
                    months_19 += Convert.ToDecimal(manHourModels[i].s6);
                    months_20 += Convert.ToDecimal(manHourModels[i].s7);
                    months_21 += Convert.ToDecimal(manHourModels[i].s8);
                    months_22 += Convert.ToDecimal(manHourModels[i].s9);
                    months_23 += Convert.ToDecimal(manHourModels[i].s10);
                    months_24 += Convert.ToDecimal(manHourModels[i].s11);
                    months_25 += Convert.ToDecimal(manHourModels[i].s12);
                    months_26 += Convert.ToDecimal(manHourModels[i].s13);
                }

                if (line.Contains("L19"))
                {
                    monthss_1 += Convert.ToDecimal(manHourModels[i].m1);
                    monthss_2 += Convert.ToDecimal(manHourModels[i].m2);
                    monthss_3 += Convert.ToDecimal(manHourModels[i].m3);
                    monthss_4 += Convert.ToDecimal(manHourModels[i].m4);
                    monthss_5 += Convert.ToDecimal(manHourModels[i].m5);
                    monthss_6 += Convert.ToDecimal(manHourModels[i].m6);
                    monthss_7 += Convert.ToDecimal(manHourModels[i].m7);
                    monthss_8 += Convert.ToDecimal(manHourModels[i].m8);
                    monthss_9 += Convert.ToDecimal(manHourModels[i].m9);
                    monthss_10 += Convert.ToDecimal(manHourModels[i].m10);
                    monthss_11 += Convert.ToDecimal(manHourModels[i].m11);
                    monthss_12 += Convert.ToDecimal(manHourModels[i].m12);
                    monthss_13 += Convert.ToDecimal(manHourModels[i].m13);

                    monthss_14 += Convert.ToDecimal(manHourModels[i].s1);
                    monthss_15 += Convert.ToDecimal(manHourModels[i].s2);
                    monthss_16 += Convert.ToDecimal(manHourModels[i].s3);
                    monthss_17 += Convert.ToDecimal(manHourModels[i].s4);
                    monthss_18 += Convert.ToDecimal(manHourModels[i].s5);
                    monthss_19 += Convert.ToDecimal(manHourModels[i].s6);
                    monthss_20 += Convert.ToDecimal(manHourModels[i].s7);
                    monthss_21 += Convert.ToDecimal(manHourModels[i].s8);
                    monthss_22 += Convert.ToDecimal(manHourModels[i].s9);
                    monthss_23 += Convert.ToDecimal(manHourModels[i].s10);
                    monthss_24 += Convert.ToDecimal(manHourModels[i].s11);
                    monthss_25 += Convert.ToDecimal(manHourModels[i].s12);
                    monthss_26 += Convert.ToDecimal(manHourModels[i].s13);
                }
            }
			#region L12/L14/L19线总计

			#region L12VS
			if (months != 0)
			{
				month26 = Math.Round(month13 / months - 1);
			}
			if (month1 != 0)
			{
				month27 = Math.Round(month14 / month1 - 1);
			}
			if (month2 != 0)
			{
				month28 = Math.Round(month15 / month2 - 1);
			}
			if (month3 != 0)
			{
				month29 = Math.Round(month16 / month3 - 1);
			}
			if (month4 != 0)
			{
				month30 = Math.Round(month17 / month4 - 1);
			}
			if (month5 != 0)
			{
				month31 = Math.Round(month18 / month5 - 1);
			}
			if (month6 != 0)
			{
				month32 = Math.Round(month19 / month6 - 1);
			}
			if (month7 != 0)
			{
				month33 = Math.Round(month20 / month7 - 1);
			}
			if (month8 != 0)
			{
				month34 = Math.Round(month21 / month8 - 1);
			}
			if (month9 != 0)
			{
				month35 = Math.Round(month22 / month9 - 1);
			}
			if (month10 != 0)
			{
				month36 = Math.Round(month23 / month10 - 1);
			}
			if (month11 != 0)
			{
				month37 = Math.Round(month24 / month11 - 1);
			}
			if (month12 != 0)
			{
				month38 = Math.Round(month25 / month12 - 1);
			}
			#endregion
			#region L14VS

			if (months_1 != 0)
			{
				months_27 = months_14 / months_1 - 1;
			}
			if (months_2 != 0)
			{
				months_28 = months_15 / months_2 - 1;
			}
			if (months_3 != 0)
			{
				months_29 = months_16 / months_3 - 1;
			}
			if (months_4 != 0)
			{
				months_30 = months_17 / months_4 - 1;
			}
			if (months_5 != 0)
			{
				months_31 = months_18 / months_5 - 1;
			}
			if (months_6 != 0)
			{
				months_32 = months_19 / months_6 - 1;
			}
			if (months_7 != 0)
			{
				months_33 = months_20 / months_7 - 1;
			}
			if (months_8 != 0)
			{
				months_34 = months_21 / months_8 - 1;
			}
			if (months_9 != 0)
			{
				months_35 = months_22 / months_9 - 1;
			}
			if (months_10 != 0)
			{
				months_36 = months_23 / months_10 - 1;
			}
			if (months_11 != 0)
			{
				months_37 = months_24 / months_11 - 1;
			}
			if (months_12 != 0)
			{
				months_38 = months_25 / months_12 - 1;
			}
			if (months_13 != 0)
			{
				months_39 = months_26 / months_13 - 1;
			}

			#endregion
			#region L19VS
			if (monthss_1 != 0)
			{
				monthss_27 = monthss_14 / monthss_1 - 1;
			}
			if (monthss_2 != 0)
			{
				monthss_28 = monthss_15 / monthss_2 - 1;
			}
			if (monthss_3 != 0)
			{
				monthss_29 = monthss_16 / monthss_3 - 1;
			}
			if (monthss_4 != 0)
			{
				monthss_30 = monthss_17 / monthss_4 - 1;
			}
			if (monthss_5 != 0)
			{
				monthss_31 = monthss_18 / monthss_5 - 1;
			}
			if (monthss_6 != 0)
			{
				monthss_32 = monthss_19 / monthss_6 - 1;
			}
			if (monthss_7 != 0)
			{
				monthss_33 = monthss_20 / monthss_7 - 1;
			}
			if (monthss_8 != 0)
			{
				monthss_34 = monthss_21 / monthss_8 - 1;
			}
			if (monthss_9 != 0)
			{
				monthss_35 = monthss_22 / monthss_9 - 1;
			}
			if (monthss_10 != 0)
			{
				monthss_36 = monthss_23 / monthss_10 - 1;
			}
			if (monthss_11 != 0)
			{
				monthss_37 = monthss_24 / monthss_11 - 1;
			}
			if (monthss_12 != 0)
			{
				monthss_38 = monthss_25 / monthss_12 - 1;
			}
			if (monthss_13 != 0)
			{
				monthss_39 = monthss_26 / monthss_13 - 1;
			}
            #endregion


            #region 组合L12合并
            ManHourModel obj2 = new ManHourModel { };
            manHourModels.Add(obj2);
            obj2.LineName = "L12总计";
            obj2.m1 = months;
            obj2.m2 = month1;
            obj2.m3 = month2;
            obj2.m4 = month3;
            obj2.m5 = month4;
            obj2.m6 = month5;
            obj2.m7 = month6;
            obj2.m8 = month7;
            obj2.m9 = month8;
            obj2.m10 = month9;
            obj2.m11 = month10;
            obj2.m12 = month11;
            obj2.m13 = month12;
            obj2.s1 = month13;
            obj2.s2 = month14;
            obj2.s3 = month15;
            obj2.s4 = month16;
            obj2.s5 = month17;
            obj2.s6 = month18;
            obj2.s7 = month19;
            obj2.s8 = month20;
            obj2.s9 = month21;
            obj2.s10 = month22;
            obj2.s11 = month23;
            obj2.s12 = month24;
            obj2.s13 = month25;
            obj2.vs1 = month26;
            obj2.vs2 = month27;
            obj2.vs3 = month28;
            obj2.vs4 = month29;
            obj2.vs5 = month30;
            obj2.vs6 = month31;
            obj2.vs7 = month32;
            obj2.vs8 = month33;
            obj2.vs9 = month34;
            obj2.vs10 = month35;
            obj2.vs11 = month36;
            obj2.vs12 = month37;
            obj2.vs13 = month38;
            #endregion
            #region 组合L14合并
            ManHourModel obj3 = new ManHourModel { };
            manHourModels.Add(obj3);
            obj3.LineName = "L14总计";
            obj3.m1 = months_1;
            obj3.m2 = months_2;
            obj3.m3 = months_3;
            obj3.m4 = months_4;
            obj3.m5 = months_5;
            obj3.m6 = months_6;
            obj3.m7 = months_7;
            obj3.m8 = months_8;
            obj3.m9 = months_9;
            obj3.m10 = months_10;
            obj3.m11 = months_11;
            obj3.m12 = months_12;
            obj3.m13 = months_13;

            obj3.s1 = months_14;
            obj3.s2 = months_15;
            obj3.s3 = months_16;
            obj3.s4 = months_17;
            obj3.s5 = months_18;
            obj3.s6 = months_19;
            obj3.s7 = months_20;
            obj3.s8 = months_21;
            obj3.s9 = months_22;
            obj3.s10 = months_23;
            obj3.s11 = months_24;
            obj3.s12 = months_25;
            obj3.s13 = months_26;

            obj3.vs1 = months_27;
            obj3.vs2 = months_28;
            obj3.vs3 = months_29;
            obj3.vs4 = months_30;
            obj3.vs5 = months_31;
            obj3.vs6 = months_32;
            obj3.vs7 = months_33;
            obj3.vs8 = months_34;
            obj3.vs9 = months_35;
            obj3.vs10 = months_36;
            obj3.vs11 = months_37;
            obj3.vs12 = months_38;
            obj3.vs13 = months_39;
            #endregion
            #region 组合L19合并
            ManHourModel obj4 = new ManHourModel { };
            manHourModels.Add(obj4);
            obj4.LineName = "L12总计";
            obj4.m1 = monthss_1;
            obj4.m2 = monthss_2;
            obj4.m3 = monthss_3;
            obj4.m4 = monthss_4;
            obj4.m5 = monthss_5;
            obj4.m6 = monthss_6;
            obj4.m7 = monthss_7;
            obj4.m8 = monthss_8;
            obj4.m9 = monthss_9;
            obj4.m10 = monthss_10;
            obj4.m11 = monthss_11;
            obj4.m12 = monthss_12;
            obj4.m13 = monthss_13;

            obj4.s1 = monthss_14;
            obj4.s2 = monthss_15;
            obj4.s3 = monthss_16;
            obj4.s4 = monthss_17;
            obj4.s5 = monthss_18;
            obj4.s6 = monthss_19;
            obj4.s7 = monthss_20;
            obj4.s8 = monthss_21;
            obj4.s9 = monthss_22;
            obj4.s10 = monthss_23;
            obj4.s11 = monthss_24;
            obj4.s12 = monthss_25;
            obj4.s13 = monthss_26;

            obj4.vs1 = monthss_27;
            obj4.vs2 = monthss_28;
            obj4.vs3 = monthss_29;
            obj4.vs4 = monthss_30;
            obj4.vs5 = monthss_31;
            obj4.vs6 = monthss_32;
            obj4.vs7 = monthss_33;
            obj4.vs8 = monthss_34;
            obj4.vs9 = monthss_35;
            obj4.vs10 = monthss_36;
            obj4.vs11 = monthss_37;
            obj4.vs12 = monthss_38;
            obj4.vs13 = monthss_39;
            #endregion
            #endregion
            #endregion


            return manHourModels;
		}
        #endregion

        #region 三厂每月产出率 （产量）
		public class YieIDModels
		{
			public string ProductionOrderId { get; set; }
			public string WorkCenter { get; set; }
			public string StartTime { get; set; }
			public string MaterialId { get; set; }
			public string Formula { get; set; }
			public string MaterialDecs { get; set; }
			public string FormulaType { get; set; }
			public string PlantDecs { get; set; }
			public string KgUnit { get; set; }
			public decimal? PlanQtyTon { get; set; }
			public decimal? ActualQtyKg { get; set; }
			public decimal? ActualConsumeQtyKg { get; set; }
			public decimal? Rate { get; set; }
			public string FW { get; set; }
			public decimal? LastYield { get; set; }
			public decimal? Difference { get; set; }
			public string LineId { get; set; }
        }

		/// <summary>
		/// 去年
		/// </summary>
        public class LastYieIDModel
        {
            public string WorkCenter { get; set; }
           
            public string Formula { get; set; }
            public decimal Rate { get; set; }
        }

        public static string GetYieldSql(DateTime startTime, DateTime endTime, string MaterialCode, string FormulaType, string LineName, string WorkCenter,int GroupByFormula ,int GroupByWorkCenter ,int GroupByMaterialCategory, int GroupByMaterial)
        {
            return string.Format("exec [dbo].[sp_Report_GetProductionYield] '{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}'", startTime, endTime, MaterialCode, FormulaType, LineName, WorkCenter, GroupByFormula, GroupByWorkCenter, GroupByMaterialCategory, GroupByMaterial);
        }
        /// <summary>
        /// NEW 12.3.6	三厂每月产出率 （产量）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<YieldModel>> GetYield(YieldRequestModel reqModel)
		{
		
			
            PageModel<YieldModel> result = new PageModel<YieldModel>();
            RefAsync<int> dataCount = 0;
            DateTime[] result1 = new DateTime[2];
			result1[0] = new DateTime(reqModel.Year,reqModel.Month, 1); //
			result1[1] = result1[0].AddMonths(1).AddDays(-1).AddSeconds(-1); // 年份结束时间：下一年的1月1日之前的最后一秒
			reqModel.startTime = result1[0];
			reqModel.endTime = result1[1];
			var sql = GetYieldSql(reqModel.startTime, reqModel.endTime, reqModel.MaterialCode, reqModel.FormulaType, reqModel.LineName, reqModel.WorkCenter, reqModel.GroupByFormula, reqModel.GroupByWorkCenter, reqModel.GroupByMaterialCategory, reqModel.GroupByMaterial);
			var Model = await Task.Run(() =>
			  _dal.Db.Ado.GetDataTable(sql)
			);
			List<YieldModel> yieldModels = new List<YieldModel>();
			List<YieIDModels> data = new List<YieIDModels>();//有数据的分组
            try
            {

				foreach (DataRow item in Model.Rows)
				{
					YieIDModels yieIDModels = new YieIDModels();
					yieIDModels.ProductionOrderId = item["PO_NO"].ToString();
					yieIDModels.WorkCenter = item["WorkCenter"].ToString();
					yieIDModels.StartTime = item["ActualStartTime"].ToString() == "" ? "" : item["ActualStartTime"].ToString();
					yieIDModels.MaterialId = item["MateiralCode"].ToString();
					yieIDModels.Formula = item["Formula"].ToString();
					yieIDModels.MaterialDecs = item["MaterialName"].ToString();
					yieIDModels.FormulaType = item["MaterialCate"].ToString();
					yieIDModels.PlantDecs = "三厂";//厂名
					yieIDModels.KgUnit = item["UNIT"].ToString();
					yieIDModels.PlanQtyTon = item["TargetQty"].ToString() == "" ? 0 : Convert.ToDecimal(item["TargetQty"]);
					yieIDModels.ActualQtyKg = item["ActualQty"].ToString() == "" ? 0 : Convert.ToDecimal(item["ActualQty"]);
					yieIDModels.ActualConsumeQtyKg = item["NetQty"].ToString() == "" ? 0 : Convert.ToDecimal(item["NetQty"]);
					var k = item["NetQty"].ToString();

					if (!string.IsNullOrEmpty(item["NetQty"].ToString()))
					{
						if (Convert.ToDecimal(item["NetQty"].ToString()) > 0)
						{
							yieIDModels.Rate = Math.Round((decimal)(yieIDModels.ActualQtyKg / Convert.ToDecimal(item["NetQty"]))*100, 3);
						}
						else
						{
							yieIDModels.Rate = 0;
						}
					}
					else
					{
						yieIDModels.Rate = 0;
					}
					yieIDModels.FW = item["Formula"].ToString() + "-" + item["WorkCenter"].ToString();
					yieIDModels.LineId = item["LineName"].ToString();
					data.Add(yieIDModels);
				}
			List<LastYieIDModel> lastYieIDs = new List<LastYieIDModel>();
            //result1[0] = new DateTime(reqModel.Year, reqModel.Month, 1); //
            //result1[1] = result1[0].AddMonths(1).AddDays(-1); // 年份结束时间：下一年的1月1日之前的最后一秒
			var lastYear = reqModel.Year - 1;//上年
            DateTime start = new DateTime(lastYear, reqModel.Month, 1); //
            DateTime endTime = start.AddMonths(1).AddDays(-1).AddSeconds(-1);
            var sql1 = GetYieldSql(start, endTime, reqModel.MaterialCode, reqModel.FormulaType, reqModel.LineName, reqModel.WorkCenter, 1, 1, 0, 0);
			var Model1 = await Task.Run(() =>
				 _dal.Db.Ado.GetDataTable(sql1)
			   );
				foreach (DataRow item in Model1.Rows)
				{
					LastYieIDModel lastYieIDModel = new LastYieIDModel();
					//var NetQty = item["NetQty"].ToString();
					var ActualQty = item["ActualQty"].ToString() == "" ? "0" : item["ActualQty"].ToString();

					if (!string.IsNullOrEmpty(item["NetQty"].ToString()))
					{
						if (Convert.ToDecimal(item["NetQty"].ToString()) > 0)
						{
							lastYieIDModel.Rate = Math.Round(Convert.ToDecimal(ActualQty) / Convert.ToDecimal(item["NetQty"])*100, 3);
						}
						else
						{
							lastYieIDModel.Rate = 0;
						}
					}
					else
					{
						lastYieIDModel.Rate = 0;

					}
					lastYieIDModel.Formula = item["Formula"].ToString();
					lastYieIDModel.WorkCenter = item["WorkCenter"].ToString();
					lastYieIDs.Add(lastYieIDModel);
				}

				foreach (var item1 in data)
				{
					YieldModel yield = new YieldModel();
					yield.ProductionOrderId = item1.ProductionOrderId;
					yield.WorkCenter = item1.WorkCenter;
					yield.StartTime = item1.StartTime.ToString();
					yield.MaterialId = item1.MaterialId;
					yield.Formula = item1.Formula;
					yield.MaterialDecs = item1.MaterialDecs;
					yield.FormulaType = item1.FormulaType;
					yield.PlantDecs = item1.PlantDecs;
					yield.KgUnit = item1.KgUnit;
					yield.PlanQtyTon = item1.PlanQtyTon;//计划生产重量
					yield.ActualQtyKg = item1.ActualQtyKg;
					yield.ActualConsumeQtyKg = item1.ActualConsumeQtyKg;
					yield.Rate = item1.Rate;
					yield.FW = item1.FW;
					var lastYie = lastYieIDs.Where(p => p.Formula == item1.Formula && p.WorkCenter == item1.WorkCenter).FirstOrDefault();
					if (lastYie != null)
					{
						yield.LastYield = lastYie.Rate;
						yield.Difference = item1.Rate - lastYie.Rate;//差异
					}
					else
					{
						yield.LastYield = 0;
						yield.Difference = item1.Rate - 0;//差异
					}

					yield.LineId = item1.LineId;
					yieldModels.Add(yield);
				}
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug($"三厂产出率{ex.StackTrace}不考虑两小时消耗","报表");
                throw;
            }
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = yieldModels.OrderBy(p => p.StartTime).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = yieldModels.Count;
            result.data = dataList;


            return result;
            //return yieldModels;

		}

        /*旧

        /// <summary>
        /// 三厂每月产出率 （产量）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<YieldModel>> GetYield(ImtableSapreportRequestModel reqModel)
        {
            List< YieldModel> yieldModels = new List<YieldModel>();
            var whereExpression = Expressionable.Create<MonthlyYielViewEntity>()
                .And(p => p.Year == reqModel.Year && p.Month == reqModel.Month && p.Sapordertype == "ZXH2")
                             .ToExpression();
            var data = await _MonthlyYielViewEntity.FindList(whereExpression);

            var whereExpression1 = Expressionable.Create<MonthlyYielViewEntity>()
                .And(p => p.Year == reqModel.Year - 1 && p.Month == reqModel.Month && p.Sapordertype == "ZXH2")
                             .ToExpression();
            var data1 = await _MonthlyYielViewEntity.FindList(whereExpression1);
            foreach (var item in data)
            {
                YieldModel yieldModel = new YieldModel();
                yieldModel.ProductionOrderId = item.ProductionOrderId;
                yieldModel.WorkCenter = item.WorkCenter;
                yieldModel.StartTime = item.StartTime;
                yieldModel.MaterialId = item.MaterialId;
                yieldModel.Formula = item.Formula;
                yieldModel.MaterialDecs = item.MaterialDecs;
                yieldModel.FormulaType = item.FormulaType;
                yieldModel.PlantDecs = item.PlantDecs;
                yieldModel.KgUnit = item.KgUnit;
                yieldModel.PlanQtyTon = item.PlanQtyTon;//计划生产重量
                yieldModel.ActualQtyKg = item.ActualQtyKg;
                yieldModel.ActualConsumeQtyKg = item.ActualConsumeQtyKg;
                if (item.ActualConsumeQtyKg != 0)
                {
                    yieldModel.Rate = item.ActualQtyKg / item.ActualConsumeQtyKg;
                }
                else
                {
                    yieldModel.Rate = (decimal?)0.00;
                }
                yieldModel.FW = item.Formula + item.WorkCenter;
                var sum1 = data1.Where(p => p.WorkCenter == item.WorkCenter && p.Formula == item.Formula).Sum(p => p.ActualQtyKg);

                var sum2 = data1.Where(p => p.WorkCenter == item.WorkCenter && p.Formula == item.Formula).Sum(p => p.ActualConsumeQtyKg);
                if (sum2 != 0)
                {
                    //上年平均产出率
                    yieldModel.LastYield = Math.Round((decimal)(sum1 / sum2), 4);
                }
                else
                {
                    yieldModel.LastYield = (decimal?)0.00;
                }

                //差异
                yieldModel.Difference = yieldModel.Rate - yieldModel.LastYield;
                yieldModel.LineId = item.LineId;
                yieldModels.Add(yieldModel);
            }
            var results = yieldModels.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.StartTime, OrderType.Asc);
            return results;
        }
		*/
        #endregion

        #region NEW 工单一次性完成率（产量）
		/// <summary>
		/// 
		/// </summary>
		/// <param name="startTime"></param>
		/// <param name="endTime"></param>
		/// <param name="MaterialCode"></param>
		/// <param name="FormulaType"></param>
		/// <param name="LineName"></param>
		/// <param name="WorkCenter"></param>
		/// <param name="GroupByFormula"></param>
		/// <param name="GroupByWorkCenter"></param>
		/// <param name="GroupByMaterialCategory"></param>
		/// <param name="GroupByMaterial"></param>
		/// <returns></returns>
        public static string GetCompletionRateSql(DateTime startTime, DateTime endTime, string MaterialCode, string FormulaType, string LineName, string WorkCenter, string MRP)
        {
            return string.Format("exec [dbo].[sp_Report_GetFillingPackOrdersCompletedInfo] '{0}','{1}','{2}','{3}','{4}','{5}','{6}'", startTime, endTime, MaterialCode, FormulaType, LineName, WorkCenter, MRP);
        }

        /// <summary>
        /// NEW 工单一次性完成率（产量）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<CompletionRateModel>> CompletionRate(CompletionRateReauestModel reqModel)
		{
            PageModel<CompletionRateModel> result = new PageModel<CompletionRateModel>();
            RefAsync<int> dataCount = 0;
            DateTime[] result1 = new DateTime[2];
            result1[0] = new DateTime(reqModel.Year, reqModel.Month, 1); // 年份开始时间：1月1日
            result1[1] = result1[0].AddMonths(1).AddDays(-1); // 
			var startTime= result1[0];
            var endTime=result1[1];
            var sql = GetCompletionRateSql(startTime,endTime, reqModel.MaterialCode, reqModel.FormulaType, reqModel.LineName, reqModel.WorkCenter, reqModel.MRP);
            var Model = await Task.Run(() =>
              _dal.Db.Ado.GetDataTable(sql)
            );
            List<YieldModel> yieldModels = new List<YieldModel>();
            List<CompletionRateModel> data = new List<CompletionRateModel>();//有数据的分组

            foreach (DataRow item in Model.Rows)
            {
                CompletionRateModel completion = new CompletionRateModel();
                completion.Month=Convert.ToDateTime( item["pStartTime"]).Month;
                completion.PlantDecs= item["LineName"].ToString();
				completion.PlanStartTime = item["pStartTime"].ToString();
				completion.PlanEndTime = item["pEndTime"].ToString();//==""?null: Convert.ToDateTime(item["pEndTime"]);
				completion.ProductionOrderId = item["PO_NO"].ToString();
				completion.MaterialId = item["MateiralCode"].ToString();
				completion.MaterialDecs= item["MaterialName"].ToString();
                completion.MrpCode= item["MRP"].ToString();
				completion.PlantId = "2010";
				completion.PlanQtyBox = item["TargetQty_CAR"].ToString()==""?0: Convert.ToDecimal(item["TargetQty_CAR"]);
				completion.ActualQtyBox = item["ActualQty_CAR"].ToString()==""?0: Convert.ToDecimal(item["ActualQty_CAR"]);
				completion.BoxUnit ="箱";
				completion.PlanQtyBottle = item["TargetQty_EA"].ToString()==""?0: Convert.ToDecimal(item["TargetQty_EA"]);
				completion.ActualQtyBottle = item["ActualQty_EA"].ToString()==""?0: Convert.ToDecimal(item["ActualQty_EA"]);
                completion.BottleUnit ="支";
				completion.OneTimeCompletion = completion.ActualQtyBox >= completion. PlanQtyBox ? 1 : 0;
				if (item["Reason"].ToString()!="" && item["Reason"].ToString() != null)
                {
                    switch (item["Reason"].ToString())
                    {
                        case "Defective products":
                            completion.Reason = "包装不良品多";
                            break;
                        case "Insufficient supply":
                            completion.Reason = "制造供料不足";
                            break;
                        case "Raw material issues":
                            completion.Reason = "原物料问题";
                            break;
                        case "Others":
                            completion.Reason = "其他不可抗力因素";
                            break;

                        case "Continuous production":
                            completion.Reason = "连续生产";
                            break;
                        case "PMC requirement":
                            completion.Reason = "PMC要求";
                            break;
                        case "Overfulfilled":
                            completion.Reason = "生产做多";

                            break;
                        case "工单一次性完成":
                            completion.Reason = "工单一次性完成";
                            break;
                        default:
							completion.Reason = "";
                            break;
                    }
                }
				//completion.Reason = item["Reason"].ToString();
                data.Add(completion);
            }
			if (!string.IsNullOrEmpty(reqModel.reason))
			{
                data = data.Where(p => p.Reason.Contains(reqModel.reason)).ToList();
            }
			
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引   
            var dataList = data.OrderBy(p => p.PlanStartTime).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data.Count;
            result.data = dataList;
            return result;
        }
		
		/*
		
		 /// <summary>
         /// 工单一次性完成率（产量）
         /// </summary>
         /// <param name="reqModel"></param>
         /// <returns></returns>
         public async Task<PageModel<ImtableSapreportEntity>> CompletionRate(ImtableSapreportRequestModel reqModel)
         {
             var whereExpression = Expressionable.Create<ImtableSapreportEntity>()
                 .And(p => p.Year == reqModel.Year && p.Month == reqModel.Month)
                              .ToExpression();
             //var data = await _dal.FindList(whereExpression);
             var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
             return data;
         }*/
        #endregion
       
    }
}