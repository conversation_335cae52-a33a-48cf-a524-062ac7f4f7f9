using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models;
using System;
using AutoMapper;
using SEFA.Base.IRepository.UnitOfWork;
using System.Linq;
using Abp.Extensions;
using Newtonsoft.Json;
using SEFA.DFM.Model.Models;
using System.Linq.Dynamic.Core;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.IServices;
using ORDCNFM2;
using SEFA.Base.ESB;
using SEFA.MKM.Model.Models.MKM;
using SEFA.Base.Common.Common;
using SEFA.PTM.Model.Models;
using SEFA.MKM.Model.Models;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using Microsoft.Data.SqlClient;
using NodaTime.TimeZones;
using static SEFA.PTM.Services.ConsumeViewServices;
using System.Xml.Linq;

namespace SEFA.PPM.Services.PTM
{
	public class PerformanceServices : BaseServices<PerformanceEntity>, IPerformanceServices
	{
		private readonly IBaseRepository<PerformanceEntity> _dal;
		private readonly IBaseRepository<DowntimeEntity> _dal2;
		private readonly IBaseRepository<DowntimeHistoryEntity> _dal3;
		private readonly IBaseRepository<DowntimeReasonMappingEntity> _dal4;
		private readonly IBaseRepository<DowntimeReasonEntity> _dal5;
		private readonly IBaseRepository<DowntimeGroupEntity> _dal6;
		private readonly IBaseRepository<DowntimeCategroyEntity> _dal7;
		private readonly IBaseRepository<ConfirmationEntity> _dal8;
		private readonly IBaseRepository<PoProducedExecutionEntity> _dal9;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _dal10;
		private readonly IBaseRepository<PoProducedActualEntity> _dal11;
		private readonly IBaseRepository<ProductionOrderEntity> _dal12;
		private readonly IBaseRepository<ConfirmationInterfaceEntity> _dal13;
		private readonly IBaseRepository<CookConfirmationEntity> _ccfdal;
		private readonly IBaseRepository<CookConfirmationListEntity> _ccfldal;
		private readonly IBaseRepository<PoExecutionHistroyViewEntity> _dal14;
		private readonly IBaseRepository<MaterialVersionEntity> _dal15;
		private readonly IBaseRepository<MaterialEntity> _dal16;
		private readonly IBaseRepository<PoProducedRequirementEntity> _dal17;
		private readonly IBaseRepository<EquipmentFunctionViewEntity> _dal18;
		private readonly IBaseRepository<SapSegmentEntity> _dal19;
		private readonly IBaseRepository<SapPoRoutingEntity> _dal20;
		private readonly IBaseRepository<SapSegmentEquipmentEntity> _dal21;
		private readonly IBaseRepository<ContainerHistoryEntity> _dal22;
		private readonly IBaseRepository<ParameterGroupEntity> _parameterGroupdal;
		private readonly IBaseRepository<LogsheetEntity> _logsheetdal;
		private readonly IBaseRepository<LogsheetDetailEntity> _logsheetDetaildal;
		private readonly IBaseRepository<DataItemEntity> _daldataItem;
		private readonly IBaseRepository<DataItemDetailEntity> _dalItemdatadetail;
		private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _dalequipment;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly IMapper _mapper;
		private readonly IPoExecutionHistroyServices _poExecutionHistroyServices;
		private readonly LKKESBHelper _esbHelper;

		public PerformanceServices(IBaseRepository<PerformanceEntity> dal,
			IBaseRepository<DowntimeEntity> dal2,
			IBaseRepository<DowntimeHistoryEntity> dal3,
			IBaseRepository<DowntimeReasonMappingEntity> dal4,
			IBaseRepository<DowntimeReasonEntity> dal5,
			IBaseRepository<DowntimeGroupEntity> dal6,
			IBaseRepository<DowntimeCategroyEntity> dal7,
			IBaseRepository<ConfirmationEntity> dal8,
			IBaseRepository<PoProducedExecutionEntity> dal9,
			IBaseRepository<PoSegmentRequirementEntity> dal10,
			IBaseRepository<PoProducedActualEntity> dal11,
			IBaseRepository<ProductionOrderEntity> dal12,
			IBaseRepository<ConfirmationInterfaceEntity> dal13,
			IBaseRepository<PoExecutionHistroyViewEntity> dal14,
			IPoExecutionHistroyServices poExecutionHistroyServices,
			IUnitOfWork unitOfWork,
			IUser user,
			IMapper mapper,
			LKKESBHelper esbHelper,
			IBaseRepository<MaterialVersionEntity> dal15,
			IBaseRepository<MaterialEntity> dal16,
			IBaseRepository<PoProducedRequirementEntity> dal17,
			IBaseRepository<EquipmentFunctionViewEntity> dal18,
			IBaseRepository<SapSegmentEntity> dal19,
			IBaseRepository<SapPoRoutingEntity> dal20,
			IBaseRepository<SapSegmentEquipmentEntity> dal21,
			IBaseRepository<ContainerHistoryEntity> dal22,
			IBaseRepository<ParameterGroupEntity> parameterGroupdal,
			IBaseRepository<LogsheetEntity> logsheetdal,
			IBaseRepository<LogsheetDetailEntity> logsheetDetaildal,
			IBaseRepository<DataItemEntity> daldataItem,
			IBaseRepository<DataItemDetailEntity> dalItemdatadetail,
			IBaseRepository<DFM.Model.Models.EquipmentEntity> dalequipment,
			IBaseRepository<CookConfirmationListEntity> ccfldal,
			IBaseRepository<CookConfirmationEntity> ccfdal)
		{
			_dal = dal;
			_dal2 = dal2;
			_dal3 = dal3;
			_dal4 = dal4;
			_dal5 = dal5;
			_dal6 = dal6;
			_dal7 = dal7;
			_dal8 = dal8;
			_dal9 = dal9;
			_dal10 = dal10;
			_dal11 = dal11;
			_dal12 = dal12;
			_dal13 = dal13;
			_dal14 = dal14;
			BaseDal = dal;
			_unitOfWork = unitOfWork;
			_poExecutionHistroyServices = poExecutionHistroyServices;
			_user = user;
			_mapper = mapper;
			_esbHelper = esbHelper;
			_dal15 = dal15;
			_dal16 = dal16;
			_dal17 = dal17;
			_dal18 = dal18;
			_dal19 = dal19;
			_dal20 = dal20;
			_dal21 = dal21;
			_dal22 = dal22;
			_parameterGroupdal = parameterGroupdal;
			_logsheetdal = logsheetdal;
			_logsheetDetaildal = logsheetDetaildal;
			_daldataItem = daldataItem;
			_dalItemdatadetail = dalItemdatadetail;
			_dalequipment = dalequipment;
			_ccfldal = ccfldal;
			_ccfdal = ccfdal;
		}

		public async Task<List<Select>> GetLineList(PerformanceRequestModel reqModel)
		{
			if (reqModel.EndTimeUtc.HasValue)
			{
				reqModel.EndTimeUtc = reqModel.EndTimeUtc.Value.Date.AddDays(1);
			}
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc < reqModel.EndTimeUtc)
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).Select(x => new Select() { key = x.LineId, value = x.LineName }).Distinct().ToListAsync();
			return data;
		}

		public async Task<List<Select>> GetEquipmentList(PerformanceRequestModel reqModel)
		{
			if (reqModel.EndTimeUtc.HasValue)
			{
				reqModel.EndTimeUtc = reqModel.EndTimeUtc.Value.Date.AddDays(1);
			}
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc < reqModel.EndTimeUtc)
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).Select(x => new Select() { key = x.EquipmentId, value = x.Machine }).Distinct().ToListAsync();
			return data;
		}

		public async Task<List<Select>> GetGroupList(PerformanceRequestModel reqModel)
		{
			if (reqModel.EndTimeUtc.HasValue)
			{
				reqModel.EndTimeUtc = reqModel.EndTimeUtc.Value.Date.AddDays(1);
			}
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc < reqModel.EndTimeUtc)
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).Select(x => new Select() { key = x.GroupId, value = x.Group }).Distinct().ToListAsync();
			return data;
		}

		public async Task<List<Select>> GetReasonList(PerformanceRequestModel reqModel)
		{
			if (reqModel.EndTimeUtc.HasValue)
			{
				reqModel.EndTimeUtc = reqModel.EndTimeUtc.Value.Date.AddDays(1);
			}
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc < reqModel.EndTimeUtc)
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).Select(x => new Select() { key = x.ReasonId, value = x.Reason }).Distinct().ToListAsync();
			return data;
		}

		public async Task<List<Select>> GetOrderList(PerformanceRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .And(x => !string.IsNullOrEmpty(x.ProductionOrderNo))
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc <= reqModel.EndTimeUtc)
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).Select(x => new Select() { key = x.ProductionOrderNo, value = x.ProductionOrderNo }).Distinct().ToListAsync();
			return data;
		}

		public async Task<List<PerformanceEntity>> GetList(PerformanceRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.LineId), a => a.LineId == reqModel.LineId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				  .AndIF(!string.IsNullOrEmpty(reqModel.ReasonId), a => a.ReasonId == reqModel.ReasonId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.CategoryId), a => a.CategoryId == reqModel.CategoryId)
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc <= reqModel.EndTimeUtc || (a.EndTimeUtc == null && a.StartTimeUtc <= reqModel.EndTimeUtc))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Search),
				  a =>
				  (a.Categroy != null && a.Categroy.Contains(reqModel.Search)) ||
				  (a.Group != null && a.Group.Contains(reqModel.Search)) ||
				  (a.Reason != null && a.Reason.Contains(reqModel.Search)) ||
				  (a.Machine != null && a.Machine.Contains(reqModel.Search)) ||
				  (a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.Search)) ||
				  (a.PlcCode != null && a.PlcCode.Contains(reqModel.Search)) ||
				  (a.Comment != null && a.Comment.Contains(reqModel.Search))
				  )
				  .ToExpression();
			var data = await _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression)
				.OrderByDescending(x => x.StartTimeUtc)
				.ToListAsync();
			return data;
		}

		/// <summary>
		/// 获取进度条数据
		/// </summary>
		/// <param name="reqModel">{"EquipmentId":"02405072-0372-8882-163e-0370f6000000","StartTimeUtc":"2024-07-09 00:00:00","EndTimeUtc":"2024-07-09 23:59:59"}</param>
		/// <returns></returns>
		public async Task<MessageModel<ProgressBar>> GetProgressBar(PerformanceRequestModel reqModel)
		{
			var result = new MessageModel<ProgressBar>
			{
				msg = "获取失败！",
				success = false,
				response = new ProgressBar()
				{
					Bar1 = new List<PerformanceEntity>(),
					Bar2 = new List<PoExecutionHistroyViewEntity>()
				}
			};
			if (reqModel.StartTimeUtc.Value.Date <= DateTime.Now.Date)
			{
				try
				{
					result.response.Bar1 = (await GetList(reqModel)).OrderBy(x => x.StartTimeUtc).ToList();
					result.response.Bar2 = (await _dal14.FindList(x => x.RunEquipmentId == reqModel.EquipmentId &&
					(
					(x.StartTime <= reqModel.EndTimeUtc && x.StartTime >= reqModel.StartTimeUtc) ||
					(x.EndTime <= reqModel.EndTimeUtc && x.EndTime >= reqModel.StartTimeUtc)
					))).OrderBy(x => x.StartTime).ToList();
					if (result.response.Bar1 != null && result.response.Bar1.LastOrDefault() != null && result.response.Bar1.LastOrDefault().EndTimeUtc == null)
					{
						if (reqModel.StartTimeUtc.Value.Date == DateTime.Now.Date)
						{
							result.response.Bar1.LastOrDefault().EndTimeUtc = DateTime.Now;
						}
						else
						{
							result.response.Bar1.LastOrDefault().EndTimeUtc = reqModel.EndTimeUtc;
						}
					}
					//if (result.response.Bar2 == null || result.response.Bar2.Count == 0)
					//{
					//result.response.Bar2 = (await _dal14.FindList(x => x.RunEquipmentId == reqModel.EquipmentId && x.Status == "1", x => x.StartTime, false)).Take(1).ToList();
					//}
					if (result.response.Bar2 != null && result.response.Bar2.LastOrDefault() != null && result.response.Bar2.LastOrDefault().EndTime == null)
					{
						result.response.Bar2.LastOrDefault().EndTime = reqModel.StartTimeUtc.Value.Date.AddHours(DateTime.Now.Hour).AddMinutes(DateTime.Now.Minute).AddSeconds(DateTime.Now.Second);
					}
					if (result.response.Bar2 != null)
					{
						foreach (var item in result.response.Bar2)
						{
							if (item.StartTime < reqModel.StartTimeUtc)
							{
								item.StartTime = reqModel.StartTimeUtc;
							}
							if (item.EndTime == null)
							{
								item.EndTime = reqModel.StartTimeUtc.Value.Date.AddHours(DateTime.Now.Hour).AddMinutes(DateTime.Now.Minute).AddSeconds(DateTime.Now.Second);
							}
							else if (item.EndTime > reqModel.EndTimeUtc)
							{
								item.EndTime = reqModel.EndTimeUtc;
							}
						}
					}
					if (result.response.Bar2 == null || result.response.Bar2.Count == 0 /*|| result.response.Bar2[0].StartTime > reqModel.StartTimeUtc*/)
					{
						PoExecutionHistroyViewEntity last = (await _dal14.FindList(x => x.RunEquipmentId == reqModel.EquipmentId && x.Status == "1" && x.EndTime == null)).OrderByDescending(x => x.StartTime).FirstOrDefault();
						if (last != null)
						{
							last.StartTime = reqModel.StartTimeUtc;
							//last.EndTime = reqModel.EndTimeUtc;
							//if (DateTime.Now < last.EndTime)
							//{
							last.EndTime = DateTime.Now;
							//}
							result.response.Bar2.Add(last);
						}
					}
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
				}
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		public async Task<PageModel<PerformanceEntity>> GetPageList(PerformanceRequestModel reqModel)
		{
			PageModel<PerformanceEntity> result = new PageModel<PerformanceEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.LineId), a => a.LineId == reqModel.LineId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderNo), a => a.ProductionOrderNo.Contains(reqModel.ProductionOrderNo))
				  .AndIF(!string.IsNullOrEmpty(reqModel.GroupId), a => a.GroupId == reqModel.GroupId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.ReasonId), a => a.ReasonId == reqModel.ReasonId)
				  .AndIF(!string.IsNullOrEmpty(reqModel.CategoryId), a => a.CategoryId == reqModel.CategoryId)
				  .AndIF(reqModel.StartTimeUtc.HasValue, a => a.StartTimeUtc >= reqModel.StartTimeUtc)
				  .AndIF(reqModel.EndTimeUtc.HasValue, a => a.EndTimeUtc <= reqModel.EndTimeUtc || (a.EndTimeUtc == null && a.StartTimeUtc <= reqModel.EndTimeUtc))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Search),
				 a =>
				 (a.Categroy != null && a.Categroy.Contains(reqModel.Search)) ||
				 (a.Group != null && a.Group.Contains(reqModel.Search)) ||
				 (a.Reason != null && a.Reason.Contains(reqModel.Search)) ||
				 (a.Machine != null && a.Machine.Contains(reqModel.Search)) ||
				 (a.ProductionOrderNo != null && a.ProductionOrderNo.Contains(reqModel.Search)) ||
				 (a.PlcCode != null && a.PlcCode.Contains(reqModel.Search)) ||
				 (a.Comment != null && a.Comment.Contains(reqModel.Search))
				 ).ToExpression();

			var query = _dal.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression);
			//.OrderByDescending(x => x.StartTimeUtc)

			// 动态排序
			if (!string.IsNullOrEmpty(reqModel.orderByFileds))
			{
				try
				{
					var arr = reqModel.orderByFileds.Split(' ');
					var filed = arr[0];
					OrderByType sort = OrderByType.Asc;
					if (arr.Length > 1 && arr[1].ToUpper().Contains("DESC"))
					{
						sort = OrderByType.Desc;
					}
					query = query.OrderByPropertyName(filed, sort);
				}
				catch (Exception)
				{
					query = query.OrderBy("START_TIME_UTC DESC");
				}
			}
			else
			{
				query = query.OrderBy("START_TIME_UTC DESC");
			}
			var data = await query.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		/// <summary>
		/// 获取时间有重叠的数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<DowntimeEntity>>> GetTimeOverlapList1(DowntimeEntity reqModel)
		{
			var result = new MessageModel<List<DowntimeEntity>>
			{
				msg = "查询失败！",
				success = false
			};
			if (reqModel.EquipmentId.IsNullOrEmpty())
			{
				result.msg = "EquipmentId不能为空";
				return result;
			}
			if (!reqModel.StartTimeUtc.HasValue)
			{
				result.msg = "StartTimeUtc不能为空";
				return result;
			}
			if (!reqModel.EndTimeUtc.HasValue)
			{
				result.msg = "StartTimeUtc不能为空";
				return result;
			}
			var whereExpression = Expressionable.Create<DowntimeEntity>()
				  .And(a => a.EquipmentId == reqModel.EquipmentId)
				  .And(a =>
				  //(reqModel.StartTimeUtc >= a.StartTimeUtc && a.EndTimeUtc == null) ||
				  //(reqModel.StartTimeUtc >= a.StartTimeUtc && reqModel.StartTimeUtc <= a.EndTimeUtc) ||
				  //(reqModel.EndTimeUtc >= a.StartTimeUtc && reqModel.EndTimeUtc <= a.EndTimeUtc) ||
				  //(reqModel.StartTimeUtc < a.StartTimeUtc && reqModel.EndTimeUtc > a.EndTimeUtc)
				  (reqModel.StartTimeUtc >= a.StartTimeUtc && a.EndTimeUtc == null) ||
				  (reqModel.StartTimeUtc >= a.StartTimeUtc && reqModel.StartTimeUtc < a.EndTimeUtc) ||
				  (reqModel.EndTimeUtc > a.StartTimeUtc && reqModel.EndTimeUtc <= a.EndTimeUtc) ||
				  (reqModel.StartTimeUtc <= a.StartTimeUtc && reqModel.EndTimeUtc >= a.EndTimeUtc)
				  )
				  .ToExpression();
			var data = await _dal2.Db.Queryable<DowntimeEntity>()
				.Where(whereExpression).ToListAsync();
			result.response = data;
			result.success = true;
			result.msg = "查询成功！";
			return result;
		}

		/// <summary>
		/// 获取时间有重叠的数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<PerformanceEntity>>> GetTimeOverlapList(DowntimeEntity reqModel)
		{
			var result = new MessageModel<List<PerformanceEntity>>
			{
				msg = "查询失败！",
				success = false
			};
			if (reqModel.EquipmentId.IsNullOrEmpty())
			{
				result.msg = "EquipmentId不能为空";
				return result;
			}
			if (!reqModel.StartTimeUtc.HasValue)
			{
				result.msg = "StartTimeUtc不能为空";
				return result;
			}
			if (!reqModel.EndTimeUtc.HasValue)
			{
				result.msg = "EndTimeUtc不能为空";
				return result;
			}
			var whereExpression = Expressionable.Create<PerformanceEntity>()
				  .And(a => a.EquipmentId == reqModel.EquipmentId)
				  .And(a =>
				  (reqModel.StartTimeUtc >= a.StartTimeUtc && a.EndTimeUtc == null) ||
				  (reqModel.StartTimeUtc >= a.StartTimeUtc && reqModel.StartTimeUtc < a.EndTimeUtc) ||
				  (reqModel.EndTimeUtc > a.StartTimeUtc && reqModel.EndTimeUtc <= a.EndTimeUtc) ||
				  (reqModel.StartTimeUtc <= a.StartTimeUtc && reqModel.EndTimeUtc >= a.EndTimeUtc)
				  )
				  .ToExpression();
			var data = await _dal2.Db.Queryable<PerformanceEntity>()
				.Where(whereExpression).OrderByDescending(x => x.StartTimeUtc).ToListAsync();
			result.response = data;
			result.success = true;
			result.msg = "查询成功！";
			return result;
		}

		/// <summary>
		/// SaveConfirmation
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveConfirmation(string poProducedExecutionId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作成功！",
				success = true
			};
			var poProducedExecution = await _dal9.FindEntity(poProducedExecutionId);
			if (poProducedExecution == null)
			{
				result.msg = "未找到poProducedExecution";
				return result;
			}
			var productionOrder = await _dal12.FindEntity(poProducedExecution.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			//制造工单
			if (productionOrder.SapOrderType == "ZXH2")
			{
				return result;
			}
			var sappackorder = await _dal12.Db.Queryable<SappackorderEntity>().Where(x => x.Aufnr == productionOrder.ProductionOrderNo)?.FirstAsync();
			if (sappackorder == null)
			{
				result.msg = "未找到sappackorder";
				return result;
			}
			var poSegmentRequirement = await _dal10.FindEntity(poProducedExecution.PoSegmentRequirementId);
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement";
				return result;
			}
			var sapSegment = await _dal19.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment";
				return result;
			}
			return await SaveConfirmation(poProducedExecution.ProductionOrderId, poSegmentRequirement.SegmentId);
		}

		public async Task<MessageModel<string>> SaveConfirmation(string orderId, string segmentId)
		{
			var result = new MessageModel<string>
			{
				msg = "操作成功！",
				success = true
			};
			var productionOrder = await _dal12.FindEntity(orderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				return result;
			}
			//制造工单
			if (productionOrder.SapOrderType == "ZXH2")
			{
				return result;
			}
			var sappackorder = await _dal12.Db.Queryable<SappackorderEntity>().Where(x => x.Aufnr == productionOrder.ProductionOrderNo)?.FirstAsync();
			if (sappackorder == null)
			{
				result.msg = "未找到sappackorder";
				return result;
			}
			var poSegmentRequirement = (await _dal10.FindList(x => x.ProductionOrderId == orderId && x.SegmentId == segmentId)).FirstOrDefault();
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement";
				return result;
			}
			var sapSegment = await _dal19.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment";
				return result;
			}
			var equipmentIds1 = await _dal.Db.Queryable<FunctionPropertyVEntity>()
								.Where(x => x.FunctionCode == "PerformanceEvents" &&
								x.PropertyCode == "IsBottleneckEquipment" &&
								(x.PropertyValue == "True" || (string.IsNullOrEmpty(x.PropertyValue) && x.DefaultValue == "True")))
								.Select(x => x.EquipmentId).ToListAsync() ?? new List<string>();
			var equipmentIds = (await _dal21.FindList(x => x.SapSegmentId == segmentId && equipmentIds1.Contains(x.EquipmentId)))?.Select(x => x.EquipmentId) ?? new List<string>();
			if (equipmentIds == null || equipmentIds.Count() == 0)
			{
				result.msg = "未找到工序关联设备";
				return result;
			}
			var materialVersion = await _dal15.FindEntity(productionOrder.MaterialVersionId);
			if (materialVersion == null)
			{
				result.msg = "未找到materialVersion";
				return result;
			}
			var downtimes = await _dal.FindList(x => x.ProductionOrderId == productionOrder.ID && equipmentIds.Contains(x.EquipmentId));
			var runtime = downtimes.FindAll(x => x.IsRuntime == 1 && x.EndTimeUtc != null).Sum(e => e.TimeDifferenceInSeconds);
			var plannedtime = downtimes.FindAll(x => (x.Categroy == "Planned Stoppages" || x.Categroy == "计划停机") && x.IsBreakdown == 1 && x.EndTimeUtc != null).Sum(e => e.TimeDifferenceInSeconds);
			var unplannedtime = downtimes.FindAll(x => (x.Categroy == "Unplanned Stoppages" || x.Categroy == "非计划停机") && x.EndTimeUtc != null).Sum(e => e.TimeDifferenceInSeconds);
			var crewHours = downtimes.FindAll(x => x.IsRuntime == 1 && x.EndTimeUtc != null && !string.IsNullOrEmpty(x.CrewSize)).Sum(e => e.TimeDifferenceInSeconds * (int.TryParse(e.CrewSize, out int c) ? c : 1));
			var unitIds = (await _dal17.FindList(x => x.PoSegmentRequirementId == poSegmentRequirement.ID))?.Select(x => x.UnitId) ?? new List<string>();
			var poProducedRequirementIds = (await _dal17.FindList(x => x.PoSegmentRequirementId == poSegmentRequirement.ID))?.Select(x => x.ID) ?? new List<string>();
			var goodCount = (await _dal11.FindList(x => unitIds.Contains(x.UnitId) && poProducedRequirementIds.Contains(x.PoProducedRequirementId)))?.Sum(x => x.Quantity) ?? 0;
			var wasteCount = (await _dal11.FindList(x => !unitIds.Contains(x.UnitId) && poProducedRequirementIds.Contains(x.PoProducedRequirementId)))?.Sum(x => x.Quantity) ?? 0;
			var confirmation = await _dal8.FindEntity(x => x.OrderId == productionOrder.ID && x.SapSegmentId == sapSegment.ID);
			bool flag = false;
			if (confirmation == null)
			{
				flag = true;
				confirmation = new ConfirmationEntity();
				confirmation.Status2 = 0;
				confirmation.RuntimeOverride = 0;
				confirmation.PlannedtimeOverride = 0;
				confirmation.UnplannedtimeOverride = 0;
				confirmation.CreateCustomGuid(_user.Name);
				confirmation.TotalCount = 0;
				confirmation.OrderCloseTime = DateTime.Now;
			}
			else
			{
				if (confirmation.Status == 1)
				{
					result.msg = "工时报工已同步SAP";
					return result;
				}
				confirmation.Modify(confirmation.ID, _user.Name);
			}
			confirmation.OrderId = productionOrder.ID;
			confirmation.SapSegmentId = sapSegment.ID;
			//confirmation.Runtime = runtime / 60 / 60;
			//confirmation.Plannedtime = plannedtime / 60 / 60;
			//confirmation.Unplannedtime = unplannedtime / 60 / 60;
			//confirmation.CrewHours = crewHours / 60 / 60;

			confirmation.Runtime = Math.Round(runtime / 3600m, 3);
			confirmation.Plannedtime = Math.Round(plannedtime / 3600m, 3);
			confirmation.Unplannedtime = Math.Round(unplannedtime / 3600m, 3);
			confirmation.CrewHours = Math.Round(crewHours / 3600m, 3);
			confirmation.OrderCloseTime = DateTime.Now;
			confirmation.GoodCount = goodCount;
			confirmation.WasteCount = wasteCount;
			confirmation.CovGoodCount = goodCount * sappackorder.MngPu + wasteCount;
			confirmation.CovUnit = string.IsNullOrEmpty(confirmation.CovUnit) ? "EA" : confirmation.CovUnit;
			var sapPoRouting = (await _dal20.FindList(x => x.Aufnr == productionOrder.ProductionOrderNo && x.Arbpl == sapSegment.SegmentName))?.FirstOrDefault();
			if (sapPoRouting != null)
			{
				if (sapPoRouting.Psmng != null && sappackorder.MngPu != 0 && sapPoRouting.Vgw01 != null && sapPoRouting.Vgw01 != 0)
				{
					confirmation.StdRuntime = (confirmation.CovGoodCount / sappackorder.MngPu / (sapPoRouting.Bmsch / sapPoRouting.Vgw01)) ?? 0;
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (flag == true)
				{
					await _dal8.Add(confirmation);
				}
				else
				{
					await _dal8.Update(confirmation);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取Resource工时
		/// </summary>
		/// <param name="poId"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<SapSegmentEntity>>> GetTimeList(string confirmationId)
		{
			var result = new MessageModel<List<SapSegmentEntity>>
			{
				msg = "查询失败！",
				success = false
			};
			var confirmation = await _dal8.FindEntity(confirmationId);
			if (confirmation == null)
			{
				result.msg = "未找到confirmationId";
				return result;
			}
			var sapSegment = await _dal19.FindEntity(confirmation.SapSegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment";
				return result;
			}
			var list = new List<SapSegmentEntity>();
			try
			{
				var runTime = 0.0;
				//获取工序关联设备
				MessageModel<List<SapSegmentEquipmentEntity>> apiResult_SapSegmentEquipment = await HttpHelper.PostAsync<List<SapSegmentEquipmentEntity>>("DFM", "api/SapSegmentEquipment/GetList", _user.GetToken(), new { SapSegmentId = sapSegment.ID });
				var sapSegmentEquipments = apiResult_SapSegmentEquipment.response;
				if (sapSegmentEquipments?.Count > 0)
				{
					var equipmentIds = sapSegmentEquipments.Select(x => x.EquipmentId);
					runTime += (await _dal.FindList(x => x.IsRuntime == 1 && equipmentIds.Contains(x.EquipmentId) && !string.IsNullOrEmpty(x.CrewSize))).Sum(x => x.TimeDifferenceInSeconds * (int.TryParse(x.CrewSize, out int c) ? c : 1));
				}
				sapSegment.Remark = (runTime / 60).ToString("f1");
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			list.Add(sapSegment);
			result.response = list;
			result.success = true;
			result.msg = "查询成功！";
			return result;
		}

		/// <summary>
		/// SaveEvent
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveEvent(DowntimeEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel.StartTimeUtc >= reqModel.EndTimeUtc)
			{
				result.msg = "开始时间不能大于结束时间！";
				return result;
			}
			if (reqModel.EndTimeUtc > DateTime.Now)
			{
				result.msg = "结束时间不能大于当前时间！";
				return result;
			}
			if (!string.IsNullOrEmpty(reqModel.PoExecutionId))
			{
				var poProducedExecution = await _dal9.FindEntity(reqModel.PoExecutionId);
				if (poProducedExecution == null)
				{
					result.msg = "未找到PoExecutionId！";
					return result;
				}
				var time = poProducedExecution.EndTime ?? DateTime.Now;
				if (reqModel.EndTimeUtc > time)
				{
					result.msg = "结束时间不能大于选中工单执行记录的结束时间！";
					return result;
				}
			}
			//获取班次id
			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = reqModel.EquipmentId, finddate = reqModel.StartTimeUtc.Value.ToString("yyyy-MM-dd HH:mm:ss") });
			//if (api_shifts.response == null || api_shifts.response.Count == 0)
			//{
			//	result.msg = "未找到Shifts";
			//	return result;
			//}
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= reqModel.StartTimeUtc && Convert.ToDateTime(x.EndTime) >= reqModel.StartTimeUtc);
			reqModel.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
			var downtimeReason = await _dal5.FindEntity(reqModel.ReasonId);
			if (downtimeReason == null)
			{
				result.msg = "未找到ReasonId";
				return result;
			}
			var downtimeGroup = await _dal6.FindEntity(downtimeReason.GroupId);
			if (downtimeGroup == null)
			{
				result.msg = "未找到GroupId";
				return result;
			}
			//var downtimeCategroy = await _dal7.FindEntity(downtimeGroup.CategoryId);
			var downtimeCategroy = GetCategoryByReason(reqModel.ReasonId);
			if (downtimeCategroy == null)
			{
				result.msg = "未找到CategoryId";
				return result;
			}

			reqModel.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
			reqModel.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
			reqModel.IsBottleneckEvent = "0";
			reqModel.SourceType = "MANUAL";
			reqModel.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> addDowntimes = new()
			{
				reqModel
			};
			var downtimeHistory1 = _mapper.Map<DowntimeHistoryEntity>(reqModel);
			downtimeHistory1.DowntimeId = reqModel.ID;
			downtimeHistory1.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> updateDowntimes = new List<DowntimeEntity>();
			List<string> deleteDowntimeIds = new List<string>();
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>()
			{
				downtimeHistory1
			};
			List<DowntimeHistoryEntity> updateDowntimeHistories = new List<DowntimeHistoryEntity>();
			List<DowntimeEntity> list = (await GetTimeOverlapList1(reqModel)).response;
			var ids = list.Select(x => x.ID);
			if (list?.Count > 0)
			{
				foreach (var downtime in list)
				{
					if (downtime.EndTimeUtc == null)
					{
						downtime.EndTimeUtc = DateTime.Now;
					}
					//新开始时间在旧开始时间前或相同
					if (reqModel.StartTimeUtc <= downtime.StartTimeUtc && reqModel.EndTimeUtc > downtime.StartTimeUtc)
					{
						//新结束时间在旧结束时间后或相同 相当于新时间包含旧时间
						if (reqModel.EndTimeUtc >= downtime.EndTimeUtc)
						{
							//删除旧数据
							deleteDowntimeIds.Add(downtime.ID);
						}
						else
						{
							//更新开始时间为新的结束时间，前半段时间被新事件覆盖
							downtime.StartTimeUtc = reqModel.EndTimeUtc.Value;
							downtime.Modify(downtime.ID, _user.Name);
							updateDowntimes.Add(downtime);
						}
					}
					else
					//新开始时间在旧开始时间后
					if (reqModel.StartTimeUtc > downtime.StartTimeUtc && reqModel.StartTimeUtc < downtime.EndTimeUtc)
					{
						//新结束时间在旧结束时间前 相当于旧时间包含新时间
						if (reqModel.EndTimeUtc < downtime.EndTimeUtc)
						{
							DowntimeEntity downtimesec = JsonConvert.DeserializeObject<DowntimeEntity>(JsonConvert.SerializeObject(reqModel));
							downtimesec.PoExecutionId = downtime.PoExecutionId;
							downtimesec.PlcCode = downtime.PlcCode;
							downtimesec.Comment = downtime.Comment;
							downtimesec.OrderId = downtime.OrderId;
							downtimesec.ShiftId = downtime.ShiftId;
							downtimesec.CrewSize = downtime.CrewSize;
							downtimesec.SourceType = downtime.SourceType;
							downtimesec.IsBottleneckEvent = downtime.IsBottleneckEvent;
							downtimesec.IsRuntime = downtime.IsRuntime;
							downtimesec.IsBreakdown = downtime.IsBreakdown;
							downtimesec.ReasonId = downtime.ReasonId;
							downtimesec.StartTimeUtc = reqModel.EndTimeUtc.Value;
							downtimesec.EndTimeUtc = downtime.EndTimeUtc.Value;
							downtimesec.CreateCustomGuid(_user.Name);
							addDowntimes.Add(downtimesec);
							var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(downtimesec);
							downtimeHistory.DowntimeId = downtimesec.ID;
							downtimeHistory.CreateCustomGuid(_user.Name);
							addDowntimeHistories.Add(downtimeHistory);
						}
						//新结束时间在旧结束时间后或相同
						//更新结束时间为新的开开始时间，后半段时间被新事件覆盖
						downtime.EndTimeUtc = reqModel.StartTimeUtc.Value;
						downtime.Modify(downtime.ID, _user.Name);
						updateDowntimes.Add(downtime);
					}
				}
			}
			var deleteDowntimeHistorieIds = (await _dal3.FindList(x => deleteDowntimeIds.Contains(x.DowntimeId))).Select(x => x.ID);
			_unitOfWork.BeginTran();
			try
			{
				if (deleteDowntimeIds.Count > 0)
				{
					await _dal2.DeleteByIds(deleteDowntimeIds.ToArray());
				}
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (addDowntimes.Count > 0)
				{
					await _dal2.Add(addDowntimes);
				}
				if (deleteDowntimeHistorieIds?.Count() > 0)
				{
					await _dal3.DeleteByIds(deleteDowntimeHistorieIds.ToArray());
				}
				if (addDowntimeHistories.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			//try
			//{
			//	var poProducedExecution = await _dal9.FindEntity(reqModel.PoExecutionId);
			//	await SaveConfirmation(poProducedExecution.ProductionOrderId);
			//}
			//catch (Exception ex)
			//{

			//}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// SaveEvent
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> EditEvent(DowntimeEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel.StartTimeUtc >= reqModel.EndTimeUtc)
			{
				result.msg = "开始时间不能大于结束时间！";
				return result;
			}
			if (reqModel.EndTimeUtc > DateTime.Now)
			{
				result.msg = "结束时间不能大于当前时间！";
				return result;
			}
			if (!string.IsNullOrEmpty(reqModel.PoExecutionId))
			{
				var poProducedExecution = await _dal9.FindEntity(reqModel.PoExecutionId);
				if (poProducedExecution == null)
				{
					result.msg = "未找到PoExecutionId！";
					return result;
				}
				var time = poProducedExecution.EndTime ?? DateTime.Now;
				if (reqModel.EndTimeUtc > time)
				{
					result.msg = "结束时间不能大于选中工单执行记录的结束时间！";
					return result;
				}
			}
			//获取班次id
			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = reqModel.EquipmentId, finddate = reqModel.StartTimeUtc.Value.ToString("yyyy-MM-dd HH:mm:ss") });
			//if (api_shifts.response == null || api_shifts.response.Count == 0)
			//{
			//	result.msg = "未找到Shifts";
			//	return result;
			//}
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= reqModel.StartTimeUtc && Convert.ToDateTime(x.EndTime) >= reqModel.StartTimeUtc);
			reqModel.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
			var downtimeReason = await _dal5.FindEntity(reqModel.ReasonId);
			if (downtimeReason == null)
			{
				result.msg = "未找到ReasonId";
				return result;
			}
			var downtimeGroup = await _dal6.FindEntity(downtimeReason.GroupId);
			if (downtimeGroup == null)
			{
				result.msg = "未找到GroupId";
				return result;
			}
			//var downtimeCategroy = await _dal7.FindEntity(downtimeGroup.CategoryId);
			var downtimeCategroy = GetCategoryByReason(reqModel.ReasonId);
			if (downtimeCategroy == null)
			{
				result.msg = "未找到CategoryId";
				return result;
			}
			string oldId = reqModel.ID;
			reqModel.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
			reqModel.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
			reqModel.IsBottleneckEvent = "0";
			//reqModel.SourceType = "MANUAL";
			reqModel.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> addDowntimes = new()
			{
				reqModel
			};
			var downtimeHistory1 = _mapper.Map<DowntimeHistoryEntity>(reqModel);
			downtimeHistory1.DowntimeId = reqModel.ID;
			downtimeHistory1.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> updateDowntimes = new List<DowntimeEntity>();
			List<string> deleteDowntimeIds = new List<string>();
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>()
			{
				downtimeHistory1
			};
			List<DowntimeHistoryEntity> updateDowntimeHistories = new List<DowntimeHistoryEntity>();
			List<DowntimeEntity> list = (await GetTimeOverlapList1(reqModel)).response;
			//var ids = list.Select(x => x.ID);
			if (list?.Count > 0)
			{
				foreach (var downtime in list)
				{
					//新开始时间在旧开始时间前或相同
					if (reqModel.StartTimeUtc <= downtime.StartTimeUtc && reqModel.EndTimeUtc > downtime.StartTimeUtc)
					{
						//新结束时间在旧结束时间后或相同 相当于新时间包含旧时间
						if (reqModel.EndTimeUtc >= downtime.EndTimeUtc)
						{
							//删除旧数据
							deleteDowntimeIds.Add(downtime.ID);
						}
						else
						{
							//更新开始时间为新的结束时间，前半段时间被新事件覆盖
							downtime.StartTimeUtc = reqModel.EndTimeUtc.Value;
							downtime.Modify(downtime.ID, _user.Name);
							updateDowntimes.Add(downtime);
						}
					}
					else
					//新开始时间在旧开始时间后
					if (reqModel.StartTimeUtc > downtime.StartTimeUtc && reqModel.StartTimeUtc < downtime.EndTimeUtc)
					{
						//新结束时间在旧结束时间前 相当于旧时间包含新时间
						if (reqModel.EndTimeUtc < downtime.EndTimeUtc)
						{
							DowntimeEntity downtimesec = JsonConvert.DeserializeObject<DowntimeEntity>(JsonConvert.SerializeObject(reqModel));
							downtimesec.PoExecutionId = downtime.PoExecutionId;
							downtimesec.PlcCode = downtime.PlcCode;
							downtimesec.Comment = downtime.Comment;
							downtimesec.OrderId = downtime.OrderId;
							downtimesec.ShiftId = downtime.ShiftId;
							downtimesec.CrewSize = downtime.CrewSize;
							downtimesec.SourceType = downtime.SourceType;
							downtimesec.IsBottleneckEvent = downtime.IsBottleneckEvent;
							downtimesec.IsRuntime = downtime.IsRuntime;
							downtimesec.IsBreakdown = downtime.IsBreakdown;
							downtimesec.ReasonId = downtime.ReasonId;
							downtimesec.StartTimeUtc = reqModel.EndTimeUtc.Value;
							downtimesec.EndTimeUtc = downtime.EndTimeUtc.Value;
							downtimesec.CreateCustomGuid(_user.Name);
							addDowntimes.Add(downtimesec);
							var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(downtimesec);
							downtimeHistory.DowntimeId = downtimesec.ID;
							downtimeHistory.CreateCustomGuid(_user.Name);
							addDowntimeHistories.Add(downtimeHistory);
						}
						//新结束时间在旧结束时间后或相同
						//更新结束时间为新的开始时间，后半段时间被新事件覆盖
						downtime.EndTimeUtc = reqModel.StartTimeUtc.Value;
						downtime.Modify(downtime.ID, _user.Name);
						updateDowntimes.Add(downtime);
					}
				}
			}
			var deleteDowntimeHistorieIds = (await _dal3.FindList(x => deleteDowntimeIds.Contains(x.DowntimeId))).Select(x => x.ID);
			if (addDowntimes.Count == 1 && updateDowntimes.Count == 0 && deleteDowntimeIds.Count == 0)
			{
				addDowntimes[0].ID = oldId;
				addDowntimes[0].Modify(addDowntimes[0].ID, _user.Name);
				updateDowntimes.Add(addDowntimes[0]);
				var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(addDowntimes[0]);
				downtimeHistory.DowntimeId = addDowntimes[0].ID;
				downtimeHistory.CreateCustomGuid(_user.Name);
				addDowntimeHistories.Add(downtimeHistory);
				addDowntimes.Remove(addDowntimes[0]);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (deleteDowntimeIds.Count > 0)
				{
					await _dal2.DeleteByIds(deleteDowntimeIds.ToArray());
				}
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (addDowntimes.Count > 0)
				{
					await _dal2.Add(addDowntimes);
				}
				if (deleteDowntimeHistorieIds?.Count() > 0)
				{
					await _dal3.DeleteByIds(deleteDowntimeHistorieIds.ToArray());
				}
				if (addDowntimeHistories.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			//try
			//{
			//	var poProducedExecution = await _dal9.FindEntity(reqModel.PoExecutionId);
			//	await SaveConfirmation(poProducedExecution.ProductionOrderId);
			//}
			//catch (Exception ex)
			//{


			//}

			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// CrewSize
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> CrewSize(DowntimeCrewSizeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel.Ids == null || reqModel.Ids.Count == 0)
			{
				result.msg = "至少选择一条数据进行操作！";
				return result;
			}
			List<DowntimeEntity> updateDowntimes = new List<DowntimeEntity>();
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>();
			var downtimes = await _dal2.FindList(x => reqModel.Ids.Contains(x.ID));
			if (downtimes == null || downtimes.Count == 0)
			{
				result.msg = "系统数据与当前界面显示数据存在差异，请刷新主界面后重新选择数据进行合并！";
				return result;
			}
			foreach (var id in reqModel.Ids)
			{
				var downtime = downtimes.Find(x => x.ID == id);
				if (downtime == null)
				{
					result.msg = "未找到downtimeEntityId";
					return result;
				}
				var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(downtime);
				downtimeHistory.DowntimeId = downtime.ID;
				downtimeHistory.CreateCustomGuid(_user.Name);
				downtime.CrewSize = reqModel.CrewSize.ToString();
				downtime.Modify(downtime.ID, _user.Name);
				updateDowntimes.Add(downtime);
				addDowntimeHistories.Add(downtimeHistory);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes?.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (addDowntimeHistories?.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// Reclassify
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Reclassify(DowntimeCrewSizeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel.EndTimeUtc > DateTime.Now)
			{
				result.msg = "结束时间不能大于当前时间！";
				return result;
			}
			if (reqModel.Ids == null || reqModel.Ids.Count == 0)
			{
				result.msg = "至少选择一条数据进行操作！";
				return result;
			}
			var downtimeReason = await _dal5.FindEntity(reqModel.ReasonId);
			if (downtimeReason == null)
			{
				result.msg = "未找到ReasonId";
				return result;
			}
			var downtimeGroup = await _dal6.FindEntity(downtimeReason.GroupId);
			if (downtimeGroup == null)
			{
				result.msg = "未找到GroupId";
				return result;
			}
			//var downtimeCategroy = await _dal7.FindEntity(downtimeGroup.CategoryId);
			var downtimeCategroy = GetCategoryByReason(reqModel.ReasonId);
			if (downtimeCategroy == null)
			{
				result.msg = "未找到CategoryId";
				return result;
			}
			List<DowntimeEntity> updateDowntimes = new List<DowntimeEntity>();
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>();
			var downtimes = await _dal2.FindList(x => reqModel.Ids.Contains(x.ID));
			if (downtimes == null || downtimes.Count == 0)
			{
				result.msg = "系统数据与当前界面显示数据存在差异，请刷新主界面后重新选择数据进行操作！";
				return result;
			}
			foreach (var id in reqModel.Ids)
			{
				var downtime = downtimes.Find(x => x.ID == id);
				if (downtime == null)
				{
					result.msg = "未找到downtimeEntityId";
					return result;
				}
				downtime.ReasonId = reqModel.ReasonId;
				downtime.Comment = reqModel.Comment;
				downtime.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
				downtime.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
				downtime.IsBottleneckEvent = "0";
				downtime.SourceType = "MANUAL";
				//downtime.OrderId = reqModel.ProductionOrderId;
				if (reqModel.Ids.Count == 1)
				{
					downtime.CrewSize = reqModel.CrewSize;
					downtime.StartTimeUtc = reqModel.StartTimeUtc;
					downtime.EndTimeUtc = reqModel.EndTimeUtc;
					return await EditEvent(downtimes[0]);
				}
				var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(downtime);
				downtimeHistory.DowntimeId = downtime.ID;
				downtimeHistory.CreateCustomGuid(_user.Name);
				addDowntimeHistories.Add(downtimeHistory);
				downtime.Modify(downtime.ID, _user.Name);
				updateDowntimes.Add(downtime);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes?.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (addDowntimeHistories?.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// Merge
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Merge(List<DowntimeMergeModel> reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reqModel == null || reqModel.Count < 2)
			{
				result.msg = "至少选择两条数据进行合并！";
				return result;
			}
			var masters = reqModel.FindAll(x => x.IsMaster == true);
			if (masters == null || masters.Count == 0 || masters.Count > 1)
			{
				result.msg = "请选择一条数据作为主数据！";
				return result;
			}
			var ids = reqModel.Select(x => x.ID);
			var downtimes = await _dal2.FindList(x => ids.Contains(x.ID), x => x.StartTimeUtc);
			if (downtimes.Count != ids.Count())
			{
				result.msg = "系统数据与当前界面显示数据存在差异，请刷新主界面后重新选择数据进行合并！";
				return result;
			}
			var master = downtimes.Find(x => x.ID == masters[0].ID);
			if (master == null)
			{
				result.msg = "未找到Master！";
				return result;
			}
			var deleteDowntimeIds = new List<string>();
			var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(master);
			downtimeHistory.CreateCustomGuid(_user.Name);
			downtimeHistory.DowntimeId = master.ID;
			for (int i = 0; i < downtimes.Count - 1; i++)
			{
				DowntimeEntity current = downtimes[i];
				DowntimeEntity next = downtimes[i + 1];
				if (current.EndTimeUtc == next.StartTimeUtc)
				{
					downtimes[i] = current;
					// Merge the ranges
					downtimes[i].StartTimeUtc = current.StartTimeUtc;
					downtimes[i].EndTimeUtc = (current.EndTimeUtc > next.EndTimeUtc) ? current.EndTimeUtc : next.EndTimeUtc;
					downtimes.RemoveAt(i + 1);
					if (next.ID != master.ID)
					{
						deleteDowntimeIds.Add(next.ID);
					}
					// Reset the loop because we've changed the list
					i = -1;
				}
			}
			var deleteDowntimeHistorieIds = (await _dal3.FindList(x => deleteDowntimeIds.Contains(x.DowntimeId))).Select(x => x.ID);
			if (downtimes.Count > 1)
			{
				result.msg = "请选择时间相邻的数据进行合并！";
				return result;
			}
			master.StartTimeUtc = downtimes[0].StartTimeUtc;
			master.EndTimeUtc = downtimes[0].EndTimeUtc;
			master.Modify(master.ID, _user.Name);

			_unitOfWork.BeginTran();
			try
			{
				if (deleteDowntimeIds.Count > 0)
				{
					await _dal2.DeleteByIds(deleteDowntimeIds.ToArray());
				}
				if (deleteDowntimeHistorieIds?.Count() > 0)
				{
					await _dal3.DeleteByIds(deleteDowntimeHistorieIds.ToArray());
				}
				await _dal2.Update(master);
				await _dal3.Add(downtimeHistory);
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// Split
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> Split(List<DowntimeEntity> reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var masters = reqModel.FindAll(x => !string.IsNullOrEmpty(x.ID));
			if (masters == null || masters.Count == 0 || masters.Count > 1)
			{
				result.msg = "数据异常，未找到存在ID的数据或存在ID的数据数量大于1";
				return result;
			}
			var master = await _dal2.FindEntity(masters[0].ID);
			if (master == null)
			{
				result.msg = "未找到ID";
				return result;
			}
			var updateDowntimes = new List<DowntimeEntity>();
			var addDowntimes = new List<DowntimeEntity>();
			var addDowntimeHistories = new List<DowntimeHistoryEntity>();
			var reasonIds = reqModel.Select(x => x.ReasonId).Distinct();
			var downtimeReasons = await _dal5.FindList(x => reasonIds.Contains(x.ID));
			var groupIds = downtimeReasons.Select(x => x.GroupId).Distinct();
			var downtimeGroups = await _dal6.FindList(x => groupIds.Contains(x.ID));
			var categoryIds = downtimeGroups.Select(x => x.CategoryId).Distinct();
			var downtimeCategroys = await _dal7.FindList(x => categoryIds.Contains(x.ID));
			foreach (var downtime in reqModel)
			{
				var downtimeReason = downtimeReasons.Find(x => x.ID == downtime.ReasonId);
				if (downtimeReason == null)
				{
					result.msg = "未找到ReasonId";
					return result;
				}
				var downtimeGroup = downtimeGroups.Find(x => x.ID == downtimeReason.GroupId);
				if (downtimeGroup == null)
				{
					result.msg = "未找到GroupId";
					return result;
				}
				var downtimeCategroy = downtimeCategroys.Find(x => x.ID == downtimeGroup.CategoryId);
				if (downtimeCategroy == null)
				{
					result.msg = "未找到CategoryId";
					return result;
				}
				DowntimeEntity downtimeItem = JsonConvert.DeserializeObject<DowntimeEntity>(JsonConvert.SerializeObject(master));
				DowntimeHistoryEntity downtimeHistory = null;
				downtimeItem.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
				downtimeItem.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
				downtimeItem.IsBottleneckEvent = "0";
				downtimeItem.ReasonId = downtime.ReasonId;
				downtimeItem.Comment = downtime.Comment;
				downtimeItem.StartTimeUtc = downtime.StartTimeUtc;
				downtimeItem.EndTimeUtc = downtime.EndTimeUtc;
				downtimeItem.PoExecutionId = downtime.PoExecutionId;
				downtimeItem.OrderId = downtime.OrderId;
				if (downtime.ID == master.ID)
				{
					downtimeItem.Modify(downtimeItem.ID, _user.Name);
					updateDowntimes.Add(downtimeItem);
					downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(master);
				}
				else
				{
					downtimeItem.CreateCustomGuid(_user.Name);
					addDowntimes.Add(downtimeItem);
					downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(downtimeItem);
				}
				downtimeHistory.DowntimeId = downtimeItem.ID;
				downtimeHistory.CreateCustomGuid(_user.Name);
				addDowntimeHistories.Add(downtimeHistory);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (addDowntimes.Count > 0)
				{
					await _dal2.Add(addDowntimes);
				}
				if (addDowntimeHistories.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 合并相邻的时间
		/// </summary>
		/// <param name="timeRanges"></param>
		/// <returns></returns>
		public List<DowntimeEntity> MergeOverlappingTimeRanges(List<DowntimeEntity> timeRanges)
		{
			for (int i = 0; i < timeRanges.Count - 1; i++)
			{
				DowntimeEntity current = timeRanges[i];
				DowntimeEntity next = timeRanges[i + 1];

				if (current.EndTimeUtc >= next.StartTimeUtc)
				{
					timeRanges[i] = current;
					// Merge the ranges
					timeRanges[i].StartTimeUtc = current.StartTimeUtc;
					timeRanges[i].EndTimeUtc = (current.EndTimeUtc > next.EndTimeUtc) ? current.EndTimeUtc : next.EndTimeUtc;
					timeRanges.RemoveAt(i + 1);
					// Reset the loop because we've changed the list
					i = -1;
				}
			}

			return timeRanges;
		}

		/// <summary>
		/// 根据设备id获取手动事件列表
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<List<TreeNode>> GetReasonTreeByEquipmentId(string equipmentId)
		{
			List<TreeNode> treeList = new();
			var downtimeReasonMappings = await _dal4.QueryTwoTables<DowntimeReasonMappingEntity, DowntimeReasonEntity, TreeNode>((rm, r) =>
				new object[] {
					JoinType.Left,
					r.ID == rm.ReasonId,
				},
				(rm, r) => new TreeNode
				{
					Id = rm.ReasonId,
					ParentId = r.GroupId,
					Type = "Reason",
					Name = r.Description,
				},
				(rm, r) => rm.MachineId == equipmentId && !string.IsNullOrEmpty(r.GroupId)
				);
			if (downtimeReasonMappings?.Count > 0)
			{
				treeList.AddRange(downtimeReasonMappings);
			}
			var downtimeGroups = (await _dal6.Query()).Select(x => new TreeNode
			{
				Id = x.ID,
				ParentId = x.ParentGroupId,
				Type = "Group",
				Name = x.Description,
			});
			foreach (var item in downtimeReasonMappings)
			{
				var list = GetParents(downtimeGroups.ToList(), new List<TreeNode>(), item.ParentId);
				if (list?.Count > 0)
				{
					treeList.AddRange(list);
				}
			}
			treeList = treeList.DistinctBy(x => x.Id).ToList();
			return BulidTreeByRecursive(treeList, new List<TreeNode>(), null);
		}

		/// <summary>
		/// 获取事件列表
		/// </summary>
		/// <returns></returns>
		public async Task<List<TreeNode>> GetReasonTree()
		{
			List<TreeNode> treeList = new();
			var downtimeGroups = (await _dal6.Query()).Select(x => new TreeNode
			{
				Id = x.ID,
				ParentId = x.ParentGroupId,
				Type = "Group",
				Name = x.Description,
			});
			if (downtimeGroups?.Count() > 0)
			{
				treeList.AddRange(downtimeGroups);
			}
			var downtimeReasons = (await _dal5.Query()).Select(x => new TreeNode
			{
				Id = x.ID,
				ParentId = x.GroupId,
				Type = "Reason",
				Name = x.Description,
			});
			if (downtimeReasons?.Count() > 0)
			{
				treeList.AddRange(downtimeReasons);
			}
			return BulidTreeByRecursive(treeList, new List<TreeNode>(), null);
		}

		public async Task<bool> SaveForm(PerformanceEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}

		/// <summary>
		/// 使用递归方法建树
		/// </summary>
		public static List<TreeNode> BulidTreeByRecursive(List<TreeNode> treeNodes, List<TreeNode> resps, string pID)
		{
			resps = new List<TreeNode>();
			List<TreeNode> tempList = treeNodes.Where(c => string.IsNullOrWhiteSpace(pID) ? string.IsNullOrWhiteSpace(c.ParentId) : c.ParentId == pID).ToList();
			for (int i = 0; i < tempList.Count; i++)
			{
				TreeNode node = new TreeNode();
				node.Id = tempList[i].Id;
				node.ParentId = tempList[i].ParentId;
				node.Type = tempList[i].Type;
				node.Name = tempList[i].Name;
				node.Children = BulidTreeByRecursive(treeNodes, resps, node.Id);
				resps.Add(node);
			}
			return resps;
		}

		/// <summary>
		/// 获取父级节点
		/// </summary>
		/// <param name="treeNodes"></param>
		/// <param name="resps"></param>
		/// <param name="pID"></param>
		/// <returns></returns>
		public static List<TreeNode> GetParents(List<TreeNode> treeNodes, List<TreeNode> resps, string pID)
		{
			var parent = treeNodes.Find(c => c.Id == pID);
			if (parent != null)
			{
				resps.Add(parent);
				return GetParents(treeNodes, resps, parent.ParentId);
			}
			else
			{
				return resps;
			}
		}

		/// <summary>
		/// 工单确认
		/// </summary>
		/// <param name="factoryId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> OrderCONF(List<string> ids)
		{
			var result = new MessageModel<string>()
			{
				msg = "操作成功！",
				success = true
			};
			var confirmationInterfaces = await _dal13.FindList(x => ids.Contains(x.ID));
			if (confirmationInterfaces == null || confirmationInterfaces.Count == 0)
			{
				result.msg = "未找到符合条件的数据";
				if (ids != null)
				{
					result.success = false;
				}
				return result;
			}
			var confData = await _dal8.FindList(x => ids.Contains(x.ID));
			var list1 = new List<ZPP_MES_ORDCNFM2>();
			var list2 = new List<BAPI_CORU_RETURN>();
			var updateConfirmations = new List<ConfirmationEntity>();
			foreach (var item in confirmationInterfaces)
			{
				//item.ProductionOrderNo = item.ProductionOrderNo.PadLeft(12, '0');
				//判断是机时(0010)还是人时(0020)
				var a = item.SegmentCode == "0010" ? item.Runtime : item.CrewHours;
				list1.Add(new ZPP_MES_ORDCNFM2()
				{
					PRCTYP = "F",
					AUFNR = item.ProductionOrderNo,
					VORNR = item.SegmentCode ?? "",
					ORDTM = Math.Round((decimal)(a/* / 60*/), 3),
					AMEIN = "HR",
					LMNGA = item.GoodCount,
					MEINH = "EA",//item.Unit1,
					AUERU = "X",
				});
			}
			var request = new ZRFC_PP_MES_ORDCNFM2()
			{
				BUDAT = DateTime.Now.ToString("yyyy-MM-dd"),
				IT_AUFNR = list1.ToArray(),
				IT_LOG = Array.Empty<BAPI_CORU_RETURN>(),
			};
			var xml1 = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ORDCNFM2>(request);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				messageId = "SAP_ORDCOFN",
				postData = xml1.Replace("\\\"", "'"),
				tranNo = Guid.NewGuid().ToString()
			};
			var data1 = await _esbHelper.PostXMLString(lKKESBRequest);
			result.msgDev = FAJsonConvert.ToJson(lKKESBRequest);
			result.response = FAJsonConvert.ToJson(data1);
			result.success = data1.successed;
			if (!string.IsNullOrEmpty(data1.msg))
			{
				result.msg = data1.msg;
			}
			ZRFC_PP_MES_ORDCNFM2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDCNFM2Response>(data1.Response);
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (data1.successed && reponse != null && reponse.IT_LOG?.Length > 0)
			{
				for (int i = 0; i < reponse.IT_LOG.Length; i++)
				{
					if (i < confirmationInterfaces.Count)
					{
						var confirmationInterface = confirmationInterfaces[i];
						var confirmation = confData?.Find(x => x.ID == confirmationInterface.ID);
						if (confirmation != null)
						{
							if (confirmation.SendTime == null)
							{
								confirmation.SendTime = DateTime.Now;
							}
							if (reponse.IT_LOG[i].TYPE == "S")
							{
								confirmation.Status2 = 0;
							}
							confirmation.SendTime2 = DateTime.Now;
							confirmation.Status = reponse.IT_LOG[i].TYPE == "S" ? 1 : 2;
							confirmation.Msg = reponse.IT_LOG[i].MESSAGE;
							confirmation.CONF_NO = reponse.IT_LOG[i].CONF_NO;
							confirmation.CONF_CNT = reponse.IT_LOG[i].CONF_CNT;
							confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
							updateConfirmations.Add(confirmation);
						}
					}
				}
			}
			else
			{
				foreach (var confirmation in confData)
				{
					if (confirmation.SendTime == null)
					{
						confirmation.SendTime = DateTime.Now;
					}
					confirmation.SendTime2 = DateTime.Now;
					confirmation.Status = 2;
					confirmation.Msg = data1.msg;
					confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
					updateConfirmations.Add(confirmation);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateConfirmations.Count > 0)
				{
					await _dal8.Update(updateConfirmations);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				result.success = false;
			}
			return result;
		}

		/// <summary>
		/// 工单确认
		/// </summary>
		/// <param name="factoryId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> OrderCONF_Cook(List<string> ids)
		{
			var result = new MessageModel<string>()
			{
				msg = "操作成功！",
				success = true
			};
			var confirmationInterfaces = await _ccfldal.FindList(x => ids.Contains(x.ID));
			if (confirmationInterfaces == null || confirmationInterfaces.Count == 0)
			{
				result.msg = "未找到符合条件的数据";
				if (ids != null)
				{
					result.success = false;
				}
				return result;
			}
			var confData = await _ccfdal.FindList(x => ids.Contains(x.ID));
			var list1 = new List<ZPP_MES_ORDCNFM2>();
			var list2 = new List<BAPI_CORU_RETURN>();
			var updateConfirmations = new List<CookConfirmationEntity>();
			foreach (var item in confirmationInterfaces)
			{
				//item.ProductionOrderNo = item.ProductionOrderNo.PadLeft(12, '0');

				list1.Add(new ZPP_MES_ORDCNFM2()
				{
					PRCTYP = "F",
					AUFNR = item.ProductionOrderNo,
					VORNR = item.SegmentCode ?? "",
					ORDTM = Math.Round((decimal)(item.RuntimeHours), 3),
					AMEIN = "HR",
					LMNGA = item.ActualQty,
					MEINH = "EA",//item.Unit,
					AUERU = "X",
				}); ;
			}
			var request = new ZRFC_PP_MES_ORDCNFM2()
			{
				BUDAT = DateTime.Now.ToString("yyyy-MM-dd"),
				IT_AUFNR = list1.ToArray(),
				IT_LOG = Array.Empty<BAPI_CORU_RETURN>(),
			};
			var xml1 = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ORDCNFM2>(request);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				messageId = "SAP_ORDCOFN",
				postData = xml1.Replace("\\\"", "'"),
				tranNo = Guid.NewGuid().ToString()
			};
			var data1 = await _esbHelper.PostXMLString(lKKESBRequest);
			result.msgDev = FAJsonConvert.ToJson(lKKESBRequest);
			result.response = FAJsonConvert.ToJson(data1);
			result.success = data1.successed;
			if (!string.IsNullOrEmpty(data1.msg))
			{
				result.msg = data1.msg;
			}
			ZRFC_PP_MES_ORDCNFM2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDCNFM2Response>(data1.Response);
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (data1.successed && reponse != null && reponse.IT_LOG?.Length > 0)
			{
				for (int i = 0; i < reponse.IT_LOG.Length; i++)
				{
					if (i < confirmationInterfaces.Count)
					{
						var confirmationInterface = confirmationInterfaces[i];
						var confirmation = confData?.Find(x => x.ID == confirmationInterface.ID);
						if (confirmation != null)
						{
							if (confirmation.SendTime == null)
							{
								confirmation.SendTime = DateTime.Now;
							}
							if (reponse.IT_LOG[i].TYPE == "S")
							{
								confirmation.Status2 = 0;
							}
							confirmation.SendTime2 = DateTime.Now;
							confirmation.Status = reponse.IT_LOG[i].TYPE == "S" ? 1 : 2;
							confirmation.Msg = reponse.IT_LOG[i].MESSAGE;
							confirmation.CONF_NO = reponse.IT_LOG[i].CONF_NO;
							confirmation.CONF_CNT = reponse.IT_LOG[i].CONF_CNT;
							confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
							updateConfirmations.Add(confirmation);
						}
					}
				}
			}
			else
			{
				foreach (var confirmation in confData)
				{
					if (confirmation.SendTime == null)
					{
						confirmation.SendTime = DateTime.Now;
					}
					confirmation.SendTime2 = DateTime.Now;
					confirmation.Status = 2;
					confirmation.Msg = data1.msg;
					confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
					updateConfirmations.Add(confirmation);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateConfirmations.Count > 0)
				{
					await _ccfdal.Update(updateConfirmations);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				result.success = false;
			}
			return result;
		}

		/// <summary>
		/// 工单确认
		/// </summary>
		/// <param name="factoryId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> OrderCONFReverse(OrderConfReverseModel reqModel)
		{
			var result = new MessageModel<string>()
			{
				msg = "操作成功！",
				success = true
			};
			var confirmationInterfaces = await _dal13.FindList(x => reqModel.Ids.Contains(x.ID));
			if (confirmationInterfaces == null || confirmationInterfaces.Count == 0)
			{
				result.msg = "未找到符合条件的数据";
				if (reqModel.Ids != null)
				{
					result.success = false;
				}
				return result;
			}
			var confData = await _dal8.FindList(x => reqModel.Ids.Contains(x.ID));
			var list1 = new List<ZPP_MES_ORDCNFM2>();
			var list2 = new List<BAPI_CORU_RETURN>();
			var updateConfirmations = new List<ConfirmationEntity>();
			foreach (var item in confirmationInterfaces)
			{
				list1.Add(new ZPP_MES_ORDCNFM2()
				{
					PRCTYP = "C",
					CONF_NO = item.CONF_NO,
					CONF_CNT = item.CONF_CNT,
					CONF_TEXT = reqModel.ConfText,
				});
			}
			var request = new ZRFC_PP_MES_ORDCNFM2()
			{
				BUDAT = DateTime.Now.ToString("yyyy-MM-dd"),
				IT_AUFNR = list1.ToArray(),
				IT_LOG = Array.Empty<BAPI_CORU_RETURN>(),
			};
			var xml1 = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ORDCNFM2>(request);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				messageId = "SAP_ORDCOFN",
				postData = xml1.Replace("\\\"", "'"),
				tranNo = Guid.NewGuid().ToString()
			};
			var data1 = await _esbHelper.PostXMLString(lKKESBRequest);
			result.msgDev = FAJsonConvert.ToJson(lKKESBRequest);
			result.response = FAJsonConvert.ToJson(data1);
			result.success = data1.successed;
			if (!string.IsNullOrEmpty(data1.msg))
			{
				result.msg = data1.msg;
			}
			ZRFC_PP_MES_ORDCNFM2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDCNFM2Response>(data1.Response);
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (data1.successed && reponse != null && reponse.IT_LOG?.Length > 0)
			{
				for (int i = 0; i < reponse.IT_LOG.Length; i++)
				{
					if (i < confirmationInterfaces.Count)
					{
						var confirmationInterface = confirmationInterfaces[i];
						var confirmation = confData?.Find(x => x.ID == confirmationInterface.ID);
						if (confirmation != null)
						{
							if (reponse.IT_LOG[i].TYPE == "S")
							{
								confirmation.Status = 0;
							}
							confirmation.SendTime2 = DateTime.Now;
							confirmation.Status2 = reponse.IT_LOG[i].TYPE == "S" ? 1 : 2;
							confirmation.Msg2 = reponse.IT_LOG[i].MESSAGE;
							confirmation.CONF_TEXT = reqModel.ConfText;
							confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
							updateConfirmations.Add(confirmation);
						}
					}
				}
			}
			else
			{
				foreach (var confirmation in confData)
				{
					confirmation.SendTime2 = DateTime.Now;
					confirmation.Status2 = 2;
					confirmation.Msg2 = data1.msg;
					confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
					updateConfirmations.Add(confirmation);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateConfirmations.Count > 0)
				{
					await _dal8.Update(updateConfirmations);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				result.success = false;
			}
			return result;
		}

		/// <summary>
		/// 工单确认
		/// </summary>
		/// <param name="factoryId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> OrderCONFReverse_Cook(OrderConfReverseModel reqModel)
		{
			var result = new MessageModel<string>()
			{
				msg = "操作成功！",
				success = true
			};
			var confirmationInterfaces = await _ccfldal.FindList(x => reqModel.Ids.Contains(x.ID));
			if (confirmationInterfaces == null || confirmationInterfaces.Count == 0)
			{
				result.msg = "未找到符合条件的数据";
				if (reqModel.Ids != null)
				{
					result.success = false;
				}
				return result;
			}
			var confData = await _ccfdal.FindList(x => reqModel.Ids.Contains(x.ID));
			var list1 = new List<ZPP_MES_ORDCNFM2>();
			var list2 = new List<BAPI_CORU_RETURN>();
			var updateConfirmations = new List<CookConfirmationEntity>();
			foreach (var item in confirmationInterfaces)
			{
				list1.Add(new ZPP_MES_ORDCNFM2()
				{
					PRCTYP = "C",
					CONF_NO = item.CONF_NO,
					CONF_CNT = item.CONF_CNT,
					CONF_TEXT = reqModel.ConfText,
				}); ;
			}
			var request = new ZRFC_PP_MES_ORDCNFM2()
			{
				BUDAT = DateTime.Now.ToString("yyyy-MM-dd"),
				IT_AUFNR = list1.ToArray(),
				IT_LOG = Array.Empty<BAPI_CORU_RETURN>(),
			};
			var xml1 = InterfaceHelper.GetSapRoutingRequestXML<ZRFC_PP_MES_ORDCNFM2>(request);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				messageId = "SAP_ORDCOFN",
				postData = xml1.Replace("\\\"", "'"),
				tranNo = Guid.NewGuid().ToString()
			};
			var data1 = await _esbHelper.PostXMLString(lKKESBRequest);
			result.msgDev = FAJsonConvert.ToJson(lKKESBRequest);
			result.response = FAJsonConvert.ToJson(data1);
			result.success = data1.successed;
			if (!string.IsNullOrEmpty(data1.msg))
			{
				result.msg = data1.msg;
			}
			ZRFC_PP_MES_ORDCNFM2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDCNFM2Response>(data1.Response);
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (data1.successed && reponse != null && reponse.IT_LOG?.Length > 0)
			{
				for (int i = 0; i < reponse.IT_LOG.Length; i++)
				{
					if (i < confirmationInterfaces.Count)
					{
						var confirmationInterface = confirmationInterfaces[i];
						var confirmation = confData?.Find(x => x.ID == confirmationInterface.ID);
						if (confirmation != null)
						{
							if (reponse.IT_LOG[i].TYPE == "S")
							{
								confirmation.Status = 0;
							}
							confirmation.SendTime2 = DateTime.Now;
							confirmation.Status2 = reponse.IT_LOG[i].TYPE == "S" ? 1 : 2;
							confirmation.Msg2 = reponse.IT_LOG[i].MESSAGE;
							confirmation.CONF_TEXT = reqModel.ConfText;
							confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
							updateConfirmations.Add(confirmation);
						}
					}
				}
			}
			else
			{
				foreach (var confirmation in confData)
				{
					confirmation.SendTime2 = DateTime.Now;
					confirmation.Status2 = 2;
					confirmation.Msg2 = data1.msg;
					confirmation.Modify(confirmation.ID, lKKESBRequest.messageId);
					updateConfirmations.Add(confirmation);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateConfirmations.Count > 0)
				{
					await _ccfdal.Update(updateConfirmations);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				result.success = false;
			}
			return result;
		}


		/// <summary>
		/// 获取快捷按钮列表
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<Link>> GetLinkList(DowntimeReasonMappingRequestModel reqModel)
		{
			var links = await _dal.QueryTwoTables<DowntimeReasonMappingEntity, DowntimeReasonEntity, Link>((rm, r) =>
			new object[] {
					JoinType.Left,
					r.ID == rm.ReasonId,
			},
			(rm, r) => new Link()
			{
				ReasonId = rm.ReasonId,
				Description = r.Description
			},
			(rm, r) => rm.MachineId == reqModel.MachineId && rm.IsDefault == reqModel.IsDefault
			);
			return links;
		}

		/// <summary>
		/// 获取equipment关联的reason
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<List<Link>> GetLinkList2(DowntimeReasonMappingRequestModel reqModel)
		{
			var links = await _dal.QueryTwoTables<DowntimeReasonMappingEntity, DowntimeReasonEntity, Link>((rm, r) =>
			new object[] {
					JoinType.Inner,
					r.ID == rm.ReasonId,
			},
			(rm, r) => new Link()
			{
				ReasonId = rm.ReasonId,
				Description = r.Description
			},
			(rm, r) => rm.MachineId == reqModel.MachineId && !string.IsNullOrEmpty(r.Description)
			);
			return links;
		}

		/// <summary>
		/// SaveLinkEvent
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveLinkEvent(DowntimeEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			reqModel.StartTimeUtc = DateTime.Now;
			var lastRunOrder = (await _dal14.FindList(x => x.RunEquipmentId == reqModel.EquipmentId && x.Status == "1", x => x.StartTime, false))?.FirstOrDefault();
			if (lastRunOrder != null)
			{
				reqModel.PoExecutionId = lastRunOrder.ID;
				reqModel.OrderId = lastRunOrder.ProductionOrderId;
			}
			var updateDowntimes = new List<DowntimeEntity>();
			var updateDowntimeHistories = new List<DowntimeHistoryEntity>();
			var lastNotEndEvent = (await _dal2.FindList(x => x.EquipmentId == reqModel.EquipmentId && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
			if (lastNotEndEvent != null)
			{
				//if (lastNotEndEvent.ReasonId != reqModel.ReasonId)
				//{
				lastNotEndEvent.EndTimeUtc = reqModel.StartTimeUtc.Value;
				lastNotEndEvent.Modify(lastNotEndEvent.ID, _user.Name);
				updateDowntimes.Add(lastNotEndEvent);
				var downtimeHistory1 = (await _dal3.FindList(x => x.DowntimeId == lastNotEndEvent.ID && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
				if (downtimeHistory1 != null)
				{
					downtimeHistory1.EndTimeUtc = lastNotEndEvent.EndTimeUtc;
					downtimeHistory1.Modify(downtimeHistory1.ID, _user.Name);
					updateDowntimeHistories.Add(downtimeHistory1);
				}
				//}
			}
			//获取班次id
			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = reqModel.EquipmentId, finddate = reqModel.StartTimeUtc.Value.ToString("yyyy-MM-dd HH:mm:ss") });
			//if (api_shifts.response == null || api_shifts.response.Count == 0)
			//{
			//	result.msg = "未找到Shifts";
			//	return result;
			//}
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= reqModel.StartTimeUtc && Convert.ToDateTime(x.EndTime) >= reqModel.StartTimeUtc);
			reqModel.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
			var downtimeReason = await _dal5.FindEntity(reqModel.ReasonId);
			if (downtimeReason == null)
			{
				result.msg = "未找到ReasonId";
				return result;
			}
			var downtimeGroup = await _dal6.FindEntity(downtimeReason.GroupId);
			if (downtimeGroup == null)
			{
				result.msg = "未找到GroupId";
				return result;
			}
			//var downtimeCategroy = await _dal7.FindEntity(downtimeGroup.CategoryId);
			var downtimeCategroy = GetCategoryByReason(reqModel.ReasonId);
			if (downtimeCategroy == null)
			{
				result.msg = "未找到CategoryId";
				return result;
			}
			reqModel.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
			reqModel.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
			reqModel.IsBottleneckEvent = "0";
			reqModel.SourceType = "MANUAL";
			reqModel.CrewSize = "";
			reqModel.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> addDowntimes = new()
			{
				reqModel
			};
			var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(reqModel);
			downtimeHistory.DowntimeId = reqModel.ID;
			downtimeHistory.CreateCustomGuid(_user.Name);
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>()
			{
				downtimeHistory
			};
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (updateDowntimeHistories?.Count() > 0)
				{
					await _dal3.Update(updateDowntimeHistories);
				}
				if (addDowntimes.Count > 0)
				{
					await _dal2.Add(addDowntimes);
				}
				if (addDowntimeHistories.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			//关闭自动收集事件
			await UpdateEventCollection(reqModel.EquipmentId, "OFF");
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 启动或停止工单时调用
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SaveStartOrStopEvent(DowntimeEntity reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var equipmentFunction = await _dal18.FindEntity(x => x.ID == reqModel.EquipmentId && x.FunctionCodes != null && x.FunctionCodes.Contains("PerformanceEvents"));
			if (equipmentFunction == null)
			{
				result.msg = "Function不包含PerformanceEvents";
				result.success = true;
				return result;
			}
			if (reqModel.StartTimeUtc == null)
			{
				reqModel.StartTimeUtc = DateTime.Now;
			}
			var updateDowntimes = new List<DowntimeEntity>();
			var updateDowntimeHistories = new List<DowntimeHistoryEntity>();
			var lastNotEndEvent = (await _dal2.FindList(x => x.EquipmentId == reqModel.EquipmentId && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
			if (lastNotEndEvent != null)
			{
				lastNotEndEvent.EndTimeUtc = reqModel.StartTimeUtc.Value;
				lastNotEndEvent.Modify(lastNotEndEvent.ID, _user.Name);
				updateDowntimes.Add(lastNotEndEvent);
				var downtimeHistory1 = (await _dal3.FindList(x => x.DowntimeId == lastNotEndEvent.ID && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
				if (downtimeHistory1 != null)
				{
					downtimeHistory1.EndTimeUtc = lastNotEndEvent.EndTimeUtc;
					downtimeHistory1.Modify(downtimeHistory1.ID, _user.Name);
					updateDowntimeHistories.Add(downtimeHistory1);
				}
			}
			//获取班次id
			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = reqModel.EquipmentId, finddate = reqModel.StartTimeUtc.Value.ToString("yyyy-MM-dd HH:mm:ss") });
			//if (api_shifts.response == null || api_shifts.response.Count == 0)
			//{
			//	result.msg = "未找到Shifts";
			//	return result;
			//}
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= reqModel.StartTimeUtc && Convert.ToDateTime(x.EndTime) >= reqModel.StartTimeUtc);
			reqModel.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
			var downtimeReason = await _dal5.FindEntity(x => x.Description == reqModel.ReasonId);
			//var downtimeReason = await _dal5.FindEntity(reqModel.ReasonId);
			if (downtimeReason == null)
			{
				result.msg = "未找到Reason";
				return result;
			}
			reqModel.ReasonId = downtimeReason.ID;
			var downtimeGroup = await _dal6.FindEntity(downtimeReason.GroupId);
			if (downtimeGroup == null)
			{
				result.msg = "未找到GroupId";
				return result;
			}
			//var downtimeCategroy = await _dal7.FindEntity(downtimeGroup.CategoryId);
			var downtimeCategroy = GetCategoryByReason(reqModel.ReasonId);
			if (downtimeCategroy == null)
			{
				result.msg = "未找到CategoryId";
				return result;
			}
			reqModel.IsRuntime = downtimeCategroy.Description == "Production Time" || downtimeCategroy.Description == "生产运行" ? 1 : 0;
			reqModel.IsBreakdown = downtimeCategroy.Description.Contains("Stoppages") || downtimeCategroy.Description.Contains("停机") ? 1 : 0;
			reqModel.IsBottleneckEvent = "0";
			reqModel.SourceType = "MANUAL";
			reqModel.CrewSize = reqModel.CrewSize ?? "";
			reqModel.CreateCustomGuid(_user.Name);
			List<DowntimeEntity> addDowntimes = new()
			{
				reqModel
			};
			var downtimeHistory = _mapper.Map<DowntimeHistoryEntity>(reqModel);
			downtimeHistory.DowntimeId = reqModel.ID;
			downtimeHistory.CreateCustomGuid(_user.Name);
			List<DowntimeHistoryEntity> addDowntimeHistories = new List<DowntimeHistoryEntity>()
			{
				downtimeHistory
			};
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (updateDowntimeHistories?.Count() > 0)
				{
					await _dal3.Update(updateDowntimeHistories);
				}
				if (addDowntimes.Count > 0)
				{
					await _dal2.Add(addDowntimes);
				}
				if (addDowntimeHistories.Count > 0)
				{
					await _dal3.Add(addDowntimeHistories);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取自动收集事开关状态
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetEventCollection(string equipmentId)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = false
			};
			var equipment = await _dalequipment.FindEntity(equipmentId);
			if (equipment == null)
			{
				result.msg = "未找到equipment";
				return result;
			}
			//获取自动收集事件开关
			var dataItemEntity = await _daldataItem.FindEntity(x => x.ItemCode == "PerformanceEventSwitch");
			if (dataItemEntity == null)
			{
				result.msg = "未找到PerformanceEventSwitch";
				return result;
			}
			var dataItemDetailEntity = await _dalItemdatadetail.FindEntity(x => x.ItemId == dataItemEntity.ID && x.ItemName == equipment.EquipmentCode);
			if (dataItemDetailEntity == null)
			{
				result.msg = $"未找到设备{equipment.EquipmentCode}";
				return result;
			}
			result.response = dataItemDetailEntity.ItemValue;
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 修改自动收集事件开关
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <param name="status"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateEventCollection(string equipmentId, string status)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			var updateDowntimes = new List<DowntimeEntity>();
			var addList = new List<DataItemDetailEntity>();
			var updateList = new List<DataItemDetailEntity>();
			var updateDowntimeHistories = new List<DowntimeHistoryEntity>();
			if (status == "ON")
			{
				var lastNotEndEvent = (await _dal2.FindList(x => x.EquipmentId == equipmentId && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
				if (lastNotEndEvent != null)
				{
					lastNotEndEvent.EndTimeUtc = DateTime.Now;
					lastNotEndEvent.Modify(lastNotEndEvent.ID, _user.Name);
					updateDowntimes.Add(lastNotEndEvent);
					var downtimeHistory = (await _dal3.FindList(x => x.DowntimeId == lastNotEndEvent.ID && x.EndTimeUtc == null, x => x.StartTimeUtc, false))?.FirstOrDefault();
					if (downtimeHistory != null)
					{
						downtimeHistory.EndTimeUtc = lastNotEndEvent.EndTimeUtc;
						downtimeHistory.Modify(downtimeHistory.ID, _user.Name);
						updateDowntimeHistories.Add(downtimeHistory);
					}
				}
			}
			//更新状态
			var equipment = await _dalequipment.FindEntity(equipmentId);
			if (equipment == null)
			{
				result.msg = "未找到equipment";
				return result;
			}
			//获取自动收集事件开关
			var dataItemEntity = await _daldataItem.FindEntity(x => x.ItemCode == "PerformanceEventSwitch");
			if (dataItemEntity == null)
			{
				result.msg = "未找到PerformanceEventSwitch";
				return result;
			}
			var dataItemDetailEntity = await _dalItemdatadetail.FindEntity(x => x.ItemId == dataItemEntity.ID && x.ItemName == equipment.EquipmentCode);
			if (dataItemDetailEntity == null)
			{
				dataItemDetailEntity = new DataItemDetailEntity()
				{
					ItemId = dataItemEntity.ID,
					Lang = "cn",
					Enable = 1,
					ItemName = equipment.EquipmentCode,
					SortCode = 1,
					ItemValue = "OFF"
				};
				dataItemDetailEntity.CreateCustomGuid(_user.Name);
				addList.Add(dataItemDetailEntity);
			}
			else
			{
				dataItemDetailEntity.ItemValue = status;
				dataItemDetailEntity.Modify(dataItemDetailEntity.ID, _user.Name);
				updateList.Add(dataItemDetailEntity);
			};
			_unitOfWork.BeginTran();
			try
			{
				if (updateDowntimes.Count > 0)
				{
					await _dal2.Update(updateDowntimes);
				}
				if (updateDowntimeHistories.Count() > 0)
				{
					await _dal3.Update(updateDowntimeHistories);
				}
				//提交更新状态
				if (updateList.Count > 0)
				{
					await _dalItemdatadetail.Update(updateList);
				}
				else if (addList.Count > 0)
				{
					await _dalItemdatadetail.Add(addList);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public DowntimeCategroyEntity GetCategoryByReason(string reasonId)
		{
			// 获取所有 reason 和 group 数据
			var reasons = _dal.Db.Queryable<DowntimeReasonEntity>().ToList();
			var groups = _dal.Db.Queryable<DowntimeGroupEntity>().ToList();

			// 获取 reason 对应的 group
			var reason = reasons.First(r => r.ID == reasonId);
			var group = groups.First(g => g.ID == reason.GroupId);

			// 递归找到最顶层的 group
			group = FindTopLevelGroup(group, groups);

			// 获取最顶层 group 对应的 category
			if (group.CategoryId != null)
			{
				var category = _dal.Db.Queryable<DowntimeCategroyEntity>().First(c => c.ID == group.CategoryId);
				return category;
			}

			return null; // 如果没有找到 category，返回 null
		}

		private DowntimeGroupEntity FindTopLevelGroup(DowntimeGroupEntity group, List<DowntimeGroupEntity> groups)
		{
			while (group.ParentGroupId != null)
			{
				group = groups.First(g => g.ID == group.ParentGroupId);
			}
			return group;
		}

	}
}

