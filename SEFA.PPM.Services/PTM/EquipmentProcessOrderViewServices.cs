
using SEFA.PTM.IServices;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.PTM;
using SEFA.DFM.Model.Models;
using FunctionPropertyVEntity = SEFA.PPM.Model.Models.PTM.FunctionPropertyVEntity;
using System.Linq;

namespace SEFA.PTM.Services
{
	public class EquipmentProcessOrderViewServices : BaseServices<EquipmentProcessOrderViewEntity>, IEquipmentProcessOrderViewServices
	{
		private readonly IBaseRepository<EquipmentProcessOrderViewEntity> _dal;
		private readonly IBaseRepository<PoProducedExecutionEntity> _exedal;
		private readonly IBaseRepository<FunctionPropertyVEntity> _funpdal;
		private readonly IBaseRepository<EquipmentEntity> _equipdal;
		private readonly IBaseRepository<BProductionOrderListViewEntity> _produdal;

		public EquipmentProcessOrderViewServices(IBaseRepository<EquipmentProcessOrderViewEntity> dal, IBaseRepository<PoProducedExecutionEntity> exedal, IBaseRepository<FunctionPropertyVEntity> funpdal, IBaseRepository<EquipmentEntity> equipdal, IBaseRepository<BProductionOrderListViewEntity> produdal)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_exedal = exedal;
			_funpdal = funpdal;
			_equipdal = equipdal;
			_produdal = produdal;
		}

		public async Task<List<EquipmentProcessOrderViewEntity>> GetList(EquipmentProcessOrderViewRequestModel reqModel)
		{
			List<EquipmentProcessOrderViewEntity> result = new List<EquipmentProcessOrderViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<EquipmentProcessOrderViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentGroupRowId), a => a.EquipmentGroupRowId.Equals(reqModel.EquipmentGroupRowId))
				.AndIF(!string.IsNullOrEmpty(reqModel.RunEquipmentId), a => a.RunEquipmentId.Equals(reqModel.RunEquipmentId))
				.ToExpression();
			result = await _dal.Db.Queryable<EquipmentProcessOrderViewEntity>()
				.Where(whereExpression).OrderBy(x => x.SortOrder).ToListAsync();
			return result;
		}

		public async Task<PageModel<EquipmentProcessOrderViewEntity>> GetPageList(EquipmentProcessOrderViewRequestModel reqModel)
		{
			PageModel<EquipmentProcessOrderViewEntity> result = new PageModel<EquipmentProcessOrderViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<EquipmentProcessOrderViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentGroupRowId), a => a.EquipmentGroupRowId.Equals(reqModel.EquipmentGroupRowId))
				.AndIF(!string.IsNullOrEmpty(reqModel.RunEquipmentId), a => a.RunEquipmentId.Equals(reqModel.RunEquipmentId))
				.ToExpression();
			var data = await _dal.Db.Queryable<EquipmentProcessOrderViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.SortOrder)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			foreach (var item in data)
			{
				if (string.IsNullOrEmpty(item.RunEquipmentId))
				{
					continue;
				}
				var pv = (await _funpdal.FindList(x => x.EquipmentId == item.RunEquipmentId && x.FunctionCode == "Consume" && x.PropertyCode == "MasterStorageTank")).FirstOrDefault();
				if (pv == null)
				{
					continue;
				}
				var masterStorageTank = !string.IsNullOrEmpty(pv.PropertyValue) ? pv.PropertyValue : pv.DefaultValue;
				if (!string.IsNullOrEmpty(masterStorageTank))
				{
					item.StorageTank = masterStorageTank;
					var equipment = await _equipdal.FindEntity(x => x.EquipmentCode == masterStorageTank);
					if (equipment != null)
					{
						var runOrder = (await _exedal.FindList(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null, 1, "START_TIME DESC")).FirstOrDefault();
						if (runOrder != null)
						{
							var order = await _produdal.FindEntity(runOrder.ProductionOrderId);
							if (order != null)
							{
								//item.StorageTankOrderGc = $"({order.Sequence}/{order.Count})";
							}
						}
					}
				}
			}

			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<bool> SaveForm(EquipmentProcessOrderViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}
}