using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.PPM.Services.PTM
{
	public class InfluxOpcServerServices : BaseServices<InfluxOpcServerEntity>, IInfluxOpcServerServices
	{
		private readonly IBaseRepository<InfluxOpcServerEntity> _dal;
		private readonly IUser _user;

		public InfluxOpcServerServices(IBaseRepository<InfluxOpcServerEntity> dal, IUser user)
		{
			_dal = dal;
			_user = user;
			BaseDal = dal;
		}

		public async Task<List<InfluxOpcServerEntity>> GetList(InfluxOpcServerRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<InfluxOpcServerEntity>()
				  .AndIF(reqModel.Type != null, a => a.Type == reqModel.Type)
				  .AndIF(!string.IsNullOrEmpty(reqModel.Code), a => a.Code != null && a.Code.Contains(reqModel.Code))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name != null && a.Name.Contains(reqModel.Name))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Url), a => a.Url != null && a.Url.Contains(reqModel.Url))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Search),
				  a =>
				  (a.Code != null && a.Code.Contains(reqModel.Search)) ||
				  (a.Name != null && a.Name.Contains(reqModel.Search)) ||
				  (a.Url != null && a.Url.Contains(reqModel.Search)) ||
				  (a.Remarks != null && a.Remarks.Contains(reqModel.Search))
				  )
				  .ToExpression();
			var data = await _dal.Db.Queryable<InfluxOpcServerEntity>()
				.Where(whereExpression).OrderByDescending(x => x.CreateDate).ToListAsync();
			return data;
		}

		public async Task<PageModel<InfluxOpcServerEntity>> GetPageList(InfluxOpcServerRequestModel reqModel)
		{
			PageModel<InfluxOpcServerEntity> result = new PageModel<InfluxOpcServerEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<InfluxOpcServerEntity>()
				  .AndIF(reqModel.Type != null, a => a.Type == reqModel.Type)
				  .AndIF(!string.IsNullOrEmpty(reqModel.Code), a => a.Code != null && a.Code.Contains(reqModel.Code))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name != null && a.Name.Contains(reqModel.Name))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Url), a => a.Url != null && a.Url.Contains(reqModel.Url))
				  .AndIF(!string.IsNullOrEmpty(reqModel.Search),
				  a =>
				  (a.Code != null && a.Code.Contains(reqModel.Search)) ||
				  (a.Name != null && a.Name.Contains(reqModel.Search)) ||
				  (a.Url != null && a.Url.Contains(reqModel.Search)) ||
				  (a.Remarks != null && a.Remarks.Contains(reqModel.Search))
				  )
				  .ToExpression();
			var data = await _dal.Db.Queryable<InfluxOpcServerEntity>()
				.Where(whereExpression)
				.OrderByDescending(x => x.CreateDate)
				.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			result.dataCount = dataCount;
			result.data = data;
			return result;
		}

		public async Task<bool> SaveForm(InfluxOpcServerEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				var influxOpcServer = await _dal.FindEntity(x => x.Code == entity.Code);
				if (influxOpcServer != null)
				{
					return false;
				}
				entity.CreateCustomGuid(_user.Name);
				return await Add(entity) > 0;
			}
			else
			{
				return await Update(entity);
			}
		}
	}
}