using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Services.PTM
{
    public class PoEquipmentServices : BaseServices<PoEquipmentEntity>, IPoEquipmentServices
    {
        private readonly IBaseRepository<PoEquipmentEntity> _dal;
        public PoEquipmentServices(IBaseRepository<PoEquipmentEntity> dal)
        {
            _dal = dal;
            BaseDal = dal;
        }

        public async Task<List<PoEquipmentEntity>> GetList(PoEquipmentRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoEquipmentEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<PoEquipmentEntity>> GetPageList(PoEquipmentRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PoEquipmentEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(PoEquipmentEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await Add(entity) > 0;
            }
            else
            {
                return await Update(entity);
            }
        }
    }
}