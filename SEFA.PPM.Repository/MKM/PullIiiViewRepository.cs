using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PullIiiViewRepository
	/// </summary>
    public class PullIiiViewRepository : BaseRepository<PullIiiViewEntity>, IPullIiiViewRepository
    {
        public PullIiiViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}