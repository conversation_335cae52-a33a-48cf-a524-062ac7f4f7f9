using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackCurrentViewRepository
	/// </summary>
    public class PackCurrentViewRepository : BaseRepository<PackCurrentViewEntity>, IPackCurrentViewRepository
    {
        public PackCurrentViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}