using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// DayPwoProgressViewRepository
	/// </summary>
    public class DayPwoProgressViewRepository : BaseRepository<DayPwoProgressViewEntity>, IDayPwoProgressViewRepository
    {
        public DayPwoProgressViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}