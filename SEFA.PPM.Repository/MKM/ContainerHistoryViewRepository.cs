using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// ContainerHistoryViewRepository
	/// </summary>
    public class ContainerHistoryViewRepository : BaseRepository<ContainerHistoryViewEntity>, IContainerHistoryViewRepository
    {
        public ContainerHistoryViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}