using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PullDqtyViewRepository
	/// </summary>
    public class PullDqtyViewRepository : BaseRepository<PullDqtyViewEntity>, IPullDqtyViewRepository
    {
        public PullDqtyViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}