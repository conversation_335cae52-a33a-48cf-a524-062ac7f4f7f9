using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// RequestDetailRepository
	/// </summary>
    public class RequestDetailRepository : BaseRepository<RequestDetailEntity>, IRequestDetailRepository
    {
        public RequestDetailRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}