using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// MaterialGroupsViewRepository
	/// </summary>
    public class MaterialGroupsViewRepository : BaseRepository<MaterialGroupsViewEntity>, IMaterialGroupsViewRepository
    {
        public MaterialGroupsViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}