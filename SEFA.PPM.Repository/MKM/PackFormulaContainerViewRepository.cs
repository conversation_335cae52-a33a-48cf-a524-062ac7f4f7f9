using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PackFormulaContainerViewRepository
	/// </summary>
    public class PackFormulaContainerViewRepository : BaseRepository<PackFormulaContainerViewEntity>, IPackFormulaContainerViewRepository
    {
        public PackFormulaContainerViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}