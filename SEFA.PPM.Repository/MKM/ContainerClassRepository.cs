using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// ContainerClassRepository
	/// </summary>
    public class ContainerClassRepository : BaseRepository<ContainerClassEntity>, IContainerClassRepository
    {
        public ContainerClassRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}