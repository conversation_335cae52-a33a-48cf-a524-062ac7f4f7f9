using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// InventorylistingViewRepository
	/// </summary>
    public class InventorylistingViewRepository : BaseRepository<InventorylistingViewEntity>, IInventorylistingViewRepository
    {
        public InventorylistingViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}