using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// BaseUniqueNumberRepository
	/// </summary>
    public class BaseUniqueNumberRepository : BaseRepository<BaseUniqueNumberEntity>, IBaseUniqueNumberRepository
    {
        public BaseUniqueNumberRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}