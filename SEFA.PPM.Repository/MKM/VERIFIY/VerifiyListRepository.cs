using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// VerifiyListRepository
	/// </summary>
    public class VerifiyListRepository : BaseRepository<VerifiyListEntity>, IVerifiyListRepository
    {
        public VerifiyListRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}