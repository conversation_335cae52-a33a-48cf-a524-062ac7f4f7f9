using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PPM;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BoardNumberViewRepository
	/// </summary>
    public class BoardNumberViewRepository : BaseRepository<BoardNumberViewEntity>, IBoardNumberViewRepository
    {
        public BoardNumberViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}