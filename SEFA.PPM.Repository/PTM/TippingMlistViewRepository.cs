using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// TipingMlistViewRepository
	/// </summary>
    public class TippingMlistViewRepository : BaseRepository<TippingMlistViewEntity>, ITippingMlistViewRepository
    {
        public TippingMlistViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}