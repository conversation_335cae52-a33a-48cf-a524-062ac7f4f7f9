using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// TippingPrecheckViewRepository
    /// </summary>
    public class TippingPrecheckViewRepository : BaseRepository<TippingPrecheckViewEntity>, ITippingPrecheckViewRepository
    {
        public TippingPrecheckViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}