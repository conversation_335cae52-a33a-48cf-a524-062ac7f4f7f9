using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.Repository.PTM
{
    /// <summary>
    /// SegmentMaterialRepository
    /// </summary>
    public class SegmentMaterialRepository : BaseRepository<SegmentMaterialEntity>, ISegmentMaterialRepository
    {
        public SegmentMaterialRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}