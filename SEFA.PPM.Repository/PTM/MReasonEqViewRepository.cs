using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// MReasonEqViewRepository
	/// </summary>
    public class MReasonEqViewRepository : BaseRepository<MReasonEqViewEntity>, IMReasonEqViewRepository
    {
        public MReasonEqViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}