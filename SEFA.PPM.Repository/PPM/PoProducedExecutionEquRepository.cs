using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PoProducedExecutionEquRepository
	/// </summary>
    public class PoProducedExecutionEquRepository : BaseRepository<PoProducedExecutionEquEntity>, IPoProducedExecutionEquRepository
    {
        public PoProducedExecutionEquRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}