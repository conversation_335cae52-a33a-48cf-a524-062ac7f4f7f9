using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// ProductionOrderPropertyRepository
	/// </summary>
    public class ProductionOrderPropertyRepository : BaseRepository<ProductionOrderPropertyEntity>, IProductionOrderPropertyRepository
    {
        public ProductionOrderPropertyRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}