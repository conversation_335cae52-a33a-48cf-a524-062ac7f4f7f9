using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.PPM;

namespace SEFA.PPM.Repository.PPM
{
    /// <summary>
    /// PhaseSalescontainerRepository
    /// </summary>
    public class PhaseSalescontainerViewRepository : BaseRepository<PhaseSalescontainerViewEntity>, IPhaseSalescontainerViewRepository
    {
        public PhaseSalescontainerViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}