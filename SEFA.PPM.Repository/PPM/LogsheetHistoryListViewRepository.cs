using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.ViewModels.PPM;

namespace SEFA.PPM.Repository
{
    /// <summary>
    /// LogsheetHistoryListViewRepository
    /// </summary>
    public class LogsheetHistoryListViewRepository : BaseRepository<LogsheetHistoryListViewRequireEntity>, ILogsheetHistoryListViewRepository
    {
        public LogsheetHistoryListViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}