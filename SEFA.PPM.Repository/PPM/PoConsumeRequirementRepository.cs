using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// PoConsumeRequirementRepository
	/// </summary>
    public class PoConsumeRequirementRepository : BaseRepository<PoConsumeRequirementEntity>, IPoConsumeRequirementRepository
    {
        public PoConsumeRequirementRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}