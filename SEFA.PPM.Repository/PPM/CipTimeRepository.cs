using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// CipTimeRepository
	/// </summary>
    public class CipTimeRepository : BaseRepository<CipTimeEntity>, ICipTimeRepository
    {
        public CipTimeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}