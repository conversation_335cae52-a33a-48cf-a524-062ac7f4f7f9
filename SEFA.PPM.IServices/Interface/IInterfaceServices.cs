using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models.Interface;
using SEFA.Base.Common.Helper;
using SEFA.PPM.Model.Models.InfluxDB;
using SEFA.DFM.Model.Models;
using System;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// IInterfaceServices
	/// </summary>	
	public interface IInterfaceServices
	{
		Task<MessageModel<string>> SyncUnitConvertFromSap();

		Task<MessageModel<string>> SyncMaterialFromSap();

		Task<MessageModel<string>> ApiByType(string type);

		Task<MessageModel<string>> SendOrderInfoToSS(string productionOrderId, int actionType, string lotCode = "", decimal quantity = 0);

		Task<SS_Response> SS_ScanInfo(SS_Scan_Info reqModel);

		Task<FWS_WorkOrderInfo> GetWorkOrderInfo(FWS_WorkOrderInfo_Res reqModel);

		Task<FWS_NewStdPallet_Res> NewStdPallet(FWS_NewStdPallet reqModel);

		Task<FWS_CompletedPallet_Res> CompletedPallet(FWS_CompletedPallet reqModel);

		Task<FWS_DeletePallet_Res> DeletePallet(FWS_DeletePallet reqModel);

		Task<MessageModel<string>> SendWorkOrderInfoToColos(string equipmentId, string poId);

		Task<COLOS_CreateLabel_Res> GetLabel(COLOS_CreateLabel reqModel);

		Task<COLOS_LabelStatus_Res> UpdateLabelStatus(COLOS_LabelStatus reqModel);

		Task<MessageModel<string>> GetEnergyDataByDay(string type);

		Task<MessageModel<string>> GetEnergyDataByOrder(PoProducedExecutionEntity executionEntity);

		Task<MessageModel<List<ENERGY_InstrumentList>>> GetEnergyInstrumentList(ENERGY_InstrumentList_Req reqModel);

		Task<MessageModel<AutoReportMessageModel>> AutoReport(string reportType, string equipmentCode, string tag);

		Task<MessageModel<MMI_Res>> FeedingCompleted(string execId, string equipmentId, int tippingStatus, string productionOrderId, string number);

		Task<MessageModel<MMI_Res>> QAStatusSync(string execId, string equipmentId, int qAStatus, string productionOrderId, string number);

		Task<MMI_Res> ConsumeFeedback(List<MMI_ConsumeFeedback> reqModel);

		Task<MMI_Res> ProductionFeedback(List<MMI_ProductionFeedback> reqModel);

		Task<MMI_Res> RequestFeeding(List<MMI_RequestFeeding> reqModel);

		Task<MMI_Res> CookingCompleted(List<MMI_CookingCompleted> reqModel);

		Task<MMI_Res> StorageToFilling(List<MMI_StorageToFilling> reqModel);

		Task<MessageModel<string>> SendLabelPrintToColos(COLOS_LabelPrint_r reqModel);
		Task<List<DFM.Model.Models.UnitmanageEntity>> GetUnits();
		Task<MessageModel<string>> GetQrCode(string productionOrderId, string batchCode);
		Task<string> GetTokenFromEMS(bool remove = false);
		Task<MessageModel<string>> GetFunctionPropertyValue(string equipmentId, string functionCode, string propertyCode);
		Task<PageModel<ENERGY_InstrumentList>> GetEnergyInstrumentPageList(ENERGY_InstrumentList_ReqPage reqModel);
		Task<MessageModel<EquipmentEntity>> GetEquipmentFromProperty(string functionCode, string propertyCode, string propertyValue);
		Task<MessageModel<EquipmentEntity>> GetEquipmentFromMMIEquipmentCode(string mMIEquipmentCode);
		Task<MessageModel<ParameterGroupEntity>> GetParameterGroup(string productionOrderId, string equipmentId);
		Task<MMI_Res> ReceiveCIPRecords(List<MMI_CIP> reqModels);
		Task<string> GetTokenFromHRS();
		Task<MessageModel<HRSResModel>> GetDataFromHRS(Paras reqModel);
		//Task<MessageModel<MMI_POStart_Res>> POStart(string batchId, string equipmentId, int status, bool isKx = false);
		Task<MessageModel<AutoReportMessageModel>> AutoReport2(string reportType, string equipmentCode, string tag);
		Task<MessageModel<MMI_POStart_Res>> POStart(string execId, string batchId, string equipmentId, int status, bool isKx = false);
		Task<MessageModel<string>> UpdateAutoReportDataItemData(string equipmentId, string reportType = "Consume");
		Task<MessageModel<ENERGY_Data>> TestGetEnergyDataByDay(DateTime start, DateTime end);
		Task<MessageModel<AutoReportMessageModel>> AutoReport_v2(string reportType, string equipmentCode, string tag);
	}
}