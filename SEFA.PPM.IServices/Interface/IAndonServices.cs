using SEFA.Base.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// IAndonServices
	/// </summary>	
	public interface IAndonServices
	{
		Task<MessageModel<dynamic>> CheckAfterCIPResumeTime(string equipmentId, DateTime cipTime);
		Task<MessageModel<string>> CheckCompletenessOfPkg(int hour);
		Task<MessageModel<dynamic>> CheckFeedingTime(string batchId, string equipmentId, DateTime applicationTime);
		Task<MessageModel<string>> CheckInspectionResult(string logsheetId);
		Task<MessageModel<dynamic>> CheckInspectionTime(string logsheetId);
		Task<MessageModel<string>> CheckMaterialTimeliness(int days, string locations = null);
		Task<MessageModel<string>> CheckNextOrderMaterialPrep(string poExecutionId);
		Task<MessageModel<string>> CheckOrderExecutionStatus(string areaCode);
		Task<MessageModel<string>> CheckPackagingInventory(int days);
		Task<MessageModel<string>> CheckRawMaterialInventory(int days);
		Task<MessageModel<string>> CheckRawWaterLevel();
		Task<MessageModel<string>> CheckResubmissionDuration(string logsheetId, DateTime inspectionTime);
		Task<MessageModel<string>> EditOrder(string productionOrderNo, string productLine, List<dynamic> editInfo);
		Task<MessageModel<bool>> SendBatchIdToAndon(string batchId, string equipmentId);
		Task<MessageModel<bool>> SendCipEquipmentIdToAndon(string equipmentId, DateTime cipEndTime);
		Task<MessageModel<bool>> SendLogsheetIdToAndon(string logsheetId);
		Task<MessageModel<string>> ExecuteAndon(dynamic request);
		Task<MessageModel<string>> CheckQuantityOfRejects(string logsheetId, int hour, int quantity);
		Task<MessageModel<bool>> SendNotuptostandardLogsheetIdToAndon(string logsheetId, DateTime inspectionTime);
		Task<MessageModel<bool>> SendQATimeToAndon(string logsheetId, DateTime qaTime);
		Task<MessageModel<dynamic>> CheckSamplingOnTime(string logsheetId, DateTime qaTime);
        Task<MessageModel<string>> PackOrderChange (string aufnr, string lineCode,DateTime plandate, string changInfo);
		Task<MessageModel<string>> CheckWeight(string recordId);
	}

	public class AndonModel
	{
		public string MainAlarmType { get; set; }
		public string SubAlarmType { get; set; }
		public string EquipmentCode { get; set; }
		public string AreaCode { get; set; }
		public string AlarmContent { get; set; }
		public DateTime CreateDate { get; set; }
	}
}