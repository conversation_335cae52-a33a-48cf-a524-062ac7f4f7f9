using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.DFM.Model.Models;
using System;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// ISegmentMaterialServices
    /// </summary>	
    public interface ISegmentMaterialServices : IBaseServices<SegmentMaterialEntity>
    {
        Task<PageModel<SegmentMaterialEntity>> GetPageList(SegmentMaterialRequestModel reqModel);

        Task<List<SegmentMaterialEntity>> GetList(SegmentMaterialRequestModel reqModel);

        Task<PageModel<MaterialProcessDataEntity>> GetProcessDataList(MaterialProcessDataRequestModel reqModel);

		Task<MessageModel<string>> SaveProcessData(MaterialProcessDataEntity entity);

        Task<MessageModel<string>> Release(string id);

		Task<MessageModel<string>> UpdatePO(string key);
	}
}