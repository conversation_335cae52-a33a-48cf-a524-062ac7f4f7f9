using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// ILogsheetImageServices
    /// </summary>	
    public interface ILogsheetImageServices : IBaseServices<LogsheetImageEntity>
    {
        Task<PageModel<LogsheetImageEntity>> GetPageList(LogsheetImageRequestModel reqModel);

        Task<List<LogsheetImageEntity>> GetList(LogsheetImageRequestModel reqModel);

        Task<bool> SaveForm(LogsheetImageEntity entity);
    }
}