using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// ITippingSclistServices
    /// </summary>	
    public interface ITippingSclistServices : IBaseServices<TippingSclistEntity>
    {
        Task<PageModel<TippingSclistEntity>> GetPageList(TippingSclistRequestModel reqModel);

        Task<List<TippingSclistEntity>> GetList(TippingSclistRequestModel reqModel);

        Task<bool> SaveForm(TippingSclistEntity entity);
    }
}