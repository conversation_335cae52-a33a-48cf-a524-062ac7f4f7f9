using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PPM.IServices.PTM
{
    /// <summary>
    /// IConsumeSelectServices
    /// </summary>	
    public interface IConsumeSelectServices : IBaseServices<ConsumeSelectEntity>
    {
        Task<PageModel<ConsumeSelectEntity>> GetPageList(ConsumeSelectRequestModel reqModel);

        Task<List<ConsumeSelectEntity>> GetList(ConsumeSelectRequestModel reqModel);

        Task<bool> SaveForm(ConsumeSelectEntity entity);
    }
}