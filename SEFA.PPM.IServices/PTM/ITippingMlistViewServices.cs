using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PTM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.MKM.Model.Models.MKM;
using SEFA.PPM.Model.Models.Interface;

namespace SEFA.PPM.IServices
{
	/// <summary>
	/// ITipingMlistViewServices
	/// </summary>	
	public interface ITippingMlistViewServices : IBaseServices<TippingMlistViewEntity>
	{
		Task<PageModel<TippingMlistViewEntity>> GetPageList(TippingMlistViewRequestModel reqModel);

		Task<List<TippingMlistViewEntity>> GetList(TippingMlistViewRequestModel reqModel);

		Task<MessageModel<string>> GetTippingCount(TippingSclistRequestModel reqModel);

		Task<bool> SaveForm(TippingMlistViewEntity entity);

		Task<MessageModel<string>> StartTipping(TippingMlistViewModel reqModel);

		Task<MessageModel<string>> Tipping(TippingSclistRequestModel reqModel);

		Task<MessageModel<string>> CheckTippingType(string equipmentId);

		Task<MessageModel<string>> DockScan(DockScanModel reqModel);

        Task<MessageModel<List<DestinationSelect>>> GetTippingTransferSelectList(string equipmentId);

		Task<MessageModel<DestinationSelect>> GetTippingTransferSelect(string equipmentId);

		Task<MessageModel<string>> SaveSelect(DestinationSelect reqModel);

		Task<MessageModel<string>> GetFunctionPropertyValue(string equipmentId, string functionCode, string propertyName);
		Task<MessageModel<string>> ComplateTipping(TippingMlistViewModel reqModel);
		Task<MessageModel<SortModel>> GetTippingStatus(TippingMlistViewRequestModel reqModel);
		Task<MessageModel<PoProducedExecutionModel>> GetRunOrderFromSampleEquipment(string sampleEquipment);
	}
}