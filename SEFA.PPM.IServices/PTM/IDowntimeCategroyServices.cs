using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IDowntimeCategroyServices
	/// </summary>	
    public interface IDowntimeCategroyServices :IBaseServices<DowntimeCategroyEntity>
	{
		Task<PageModel<DowntimeCategroyEntity>> GetPageList(DowntimeCategroyRequestModel reqModel);

        Task<List<DowntimeCategroyEntity>> GetList(DowntimeCategroyRequestModel reqModel);

		Task<bool> SaveForm(DowntimeCategroyEntity entity);
		Task<List<DownTimeReasonTree>> GetDownTimeReasonTree(DowntimeCategroyRequestModel reqModel);
	}
}