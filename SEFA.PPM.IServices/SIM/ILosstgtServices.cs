using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ILosstgtServices
	/// </summary>	
    public interface ILosstgtServices :IBaseServices<LosstgtEntity>
	{
		Task<PageModel<LosstgtViewEntity>> GetPageList(LosstgtViewRequestModel reqModel);

        Task<List<string>> GetItem();

        Task<List<LosstgtModel>> GetList(LosstgtRequestModel reqModel);

		Task<bool> SaveForm(LosstgtEntity entity);
		Task<ResultString> ImportData([FromForm] FileImportDto input);
    }
}