using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.ViewModels.SIM;
using SEFA.PPM.Model.Models.SIM;

namespace SEFA.PPM.IServices.SIM
{
    /// <summary>
    /// IWorkingHourServices
    /// </summary>	
    public interface IWorkingHourServices : IBaseServices<WorkingHourEntity>
    {
        Task<PageModel<WorkingHourEntity>> GetPageList(WorkingHourRequestModel reqModel);

        Task<List<WorkingHourEntity>> GetList(WorkingHourRequestModel reqModel);

        Task<bool> SaveForm(WorkingHourEntity entity);
    }
}