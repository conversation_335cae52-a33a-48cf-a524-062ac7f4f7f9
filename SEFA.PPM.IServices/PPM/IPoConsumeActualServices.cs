using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPoConsumeActualServices
	/// </summary>	
    public interface IPoConsumeActualServices :IBaseServices<PoConsumeActualEntity>
	{
		Task<PageModel<PoConsumeActualEntity>> GetPageList(PoConsumeActualRequestModel reqModel);

        Task<List<PoConsumeActualEntity>> GetList(PoConsumeActualRequestModel reqModel);

		Task<bool> SaveForm(PoConsumeActualEntity entity);
    }
}