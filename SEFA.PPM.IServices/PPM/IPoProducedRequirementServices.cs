using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPoProducedRequirementServices
	/// </summary>	
    public interface IPoProducedRequirementServices :IBaseServices<PoProducedRequirementEntity>
	{
		Task<PageModel<PoProducedRequirementEntity>> GetPageList(PoProducedRequirementRequestModel reqModel);

        Task<List<PoProducedRequirementEntity>> GetList(PoProducedRequirementRequestModel reqModel);

		Task<bool> SaveForm(PoProducedRequirementEntity entity);
    }
}