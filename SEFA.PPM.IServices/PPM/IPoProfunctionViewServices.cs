using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPoProfunctionViewServices
	/// </summary>	
    public interface IPoProfunctionViewServices :IBaseServices<PoProfunctionViewEntity>
	{
		Task<PageModel<PoProfunctionViewEntity>> GetPageList(PoProfunctionViewRequestModel reqModel);

        Task<List<PoProfunctionViewEntity>> GetList(PoProfunctionViewRequestModel reqModel);

		Task<bool> SaveForm(PoProfunctionViewEntity entity);
    }
}