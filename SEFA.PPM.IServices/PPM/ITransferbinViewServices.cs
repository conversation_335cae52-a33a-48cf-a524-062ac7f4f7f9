using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ITransferbinViewServices
	/// </summary>	
    public interface ITransferbinViewServices :IBaseServices<TransferbinViewEntity>
	{
		Task<PageModel<TransferbinViewEntity>> GetPageList(TransferbinViewRequestModel reqModel);

        Task<List<TransferbinViewEntity>> GetList(TransferbinViewRequestModel reqModel);

		Task<bool> SaveForm(TransferbinViewEntity entity);
		Task<List<TransferbinViewEntity>> GetListALL();
    }
}