using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// ISalescontainerServices
    /// </summary>	
    public interface ISalescontainerServices : IBaseServices<SalescontainerEntity>
    {
        Task<PageModel<SalescontainerEntity>> GetPageList(SalescontainerRequestModel reqModel);

        Task<List<SalescontainerEntity>> GetList(SalescontainerRequestModel reqModel);
        Task<List<object>> GetSalesContainer();
        Task<List<object>> GetSalesContainerGrp();
        Task<List<SalescontainerCheckEntity>> GetCheckList(SalescontainerRequestModel reqModel);

        Task<bool> SaveForm(SalescontainerEntity entity);
        Task<MessageModel<string>> GetSapSalesContainer();

        Task<MessageModel<string>> GetSapSalesContainerAsync();
    }
}