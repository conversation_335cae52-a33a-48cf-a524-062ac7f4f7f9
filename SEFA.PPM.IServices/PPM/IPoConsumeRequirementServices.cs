using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IPoConsumeRequirementServices
    /// </summary>	
    public interface IPoConsumeRequirementServices : IBaseServices<PoConsumeRequirementEntity>
    {
        Task<PageModel<PoConsumeRequirementEntity>> GetPageList(PoConsumeRequirementRequestModel reqModel);

        Task<List<PoConsumeRequirementEntity>> GetList(PoConsumeRequirementRequestModel reqModel);

        Task<bool> SaveForm(PoConsumeRequirementEntity entity);

        Task<MessageModel<string>> GeneratePoConsumeRequirement(PoConsumeRequirementViewModel request);
    }
}