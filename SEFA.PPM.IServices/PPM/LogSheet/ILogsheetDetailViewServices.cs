using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ILogsheetDetailViewServices
	/// </summary>	
    public interface ILogsheetDetailViewServices :IBaseServices<LogsheetDetailViewEntity>
	{
		Task<PageModel<LogsheetDetailViewEntity>> GetPageList(LogsheetDetailViewRequestModel reqModel);

        Task<List<LogsheetDetailViewEntity>> GetList(LogsheetDetailViewRequestModel reqModel);

		Task<bool> SaveForm(LogsheetDetailViewEntity entity);
    }
}