using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IPackFormulaContainerViewServices
	/// </summary>	
    public interface IPackFormulaContainerViewServices :IBaseServices<PackFormulaContainerViewEntity>
	{
		Task<PageModel<PackFormulaContainerViewEntity>> GetPageList(PackFormulaContainerViewRequestModel reqModel);

        Task<List<PackFormulaContainerViewEntity>> GetList(PackFormulaContainerViewRequestModel reqModel);

		Task<bool> SaveForm(PackFormulaContainerViewEntity entity);
    }
}