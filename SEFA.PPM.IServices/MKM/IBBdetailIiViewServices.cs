using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IBBdetailIiViewServices
	/// </summary>	
    public interface IBBdetailIiViewServices :IBaseServices<BBdetailIiViewEntity>
	{
		Task<PageModel<BBdetailIiViewEntity>> GetPageList(BBdetailIiViewRequestModel reqModel);

        Task<List<BBdetailIiViewEntity>> GetList(BBdetailIiViewRequestModel reqModel);

		Task<bool> SaveForm(BBdetailIiViewEntity entity);
    }
}