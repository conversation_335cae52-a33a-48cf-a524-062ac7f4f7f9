using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IDicBinBpalletServices
	/// </summary>	
    public interface IDicBinBpalletServices :IBaseServices<DicBinBpalletEntity>
	{
		Task<PageModel<DicBinBpalletEntity>> GetPageList(DicBinBpalletRequestModel reqModel);

        Task<List<DicBinBpalletEntity>> GetList(DicBinBpalletRequestModel reqModel);

		Task<bool> SaveForm(DicBinBpalletEntity entity);
    }
}