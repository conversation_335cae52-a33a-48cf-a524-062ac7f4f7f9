using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMlabelCreateViewServices
	/// </summary>	
    public interface IMlabelCreateViewServices :IBaseServices<MlabelCreateViewEntity>
	{
		Task<PageModel<MlabelCreateViewEntity>> GetPageList(MlabelCreateViewRequestModel reqModel);

        Task<List<MlabelCreateViewEntity>> GetList(MlabelCreateViewRequestModel reqModel);

		Task<bool> SaveForm(MlabelCreateViewEntity entity);
    }
}