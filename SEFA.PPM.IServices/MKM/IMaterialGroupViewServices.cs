using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IMaterialGroupViewServices
	/// </summary>	
    public interface IMaterialGroupViewServices :IBaseServices<MaterialGroupViewEntity>
	{
		Task<PageModel<MaterialGroupViewEntity>> GetPageList(MaterialGroupViewRequestModel reqModel);

        Task<List<MaterialGroupViewEntity>> GetList(MaterialGroupViewRequestModel reqModel);

		Task<bool> SaveForm(MaterialGroupViewEntity entity);
    }
}