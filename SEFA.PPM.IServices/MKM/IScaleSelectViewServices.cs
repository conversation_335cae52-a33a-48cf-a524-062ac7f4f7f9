using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IScaleSelectViewServices
	/// </summary>	
    public interface IScaleSelectViewServices :IBaseServices<ScaleSelectViewEntity>
	{
		Task<PageModel<ScaleSelectViewEntity>> GetPageList(ScaleSelectViewRequestModel reqModel);

        Task<List<ScaleSelectViewEntity>> GetList(ScaleSelectViewRequestModel reqModel);

		Task<bool> SaveForm(ScaleSelectViewEntity entity);
    }
}