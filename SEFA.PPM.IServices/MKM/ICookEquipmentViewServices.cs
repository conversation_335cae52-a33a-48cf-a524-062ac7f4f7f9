using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ICookEquipmentViewServices
	/// </summary>	
    public interface ICookEquipmentViewServices :IBaseServices<CookEquipmentViewEntity>
	{
		Task<PageModel<CookEquipmentViewEntity>> GetPageList(CookEquipmentViewRequestModel reqModel);

        Task<List<CookEquipmentViewEntity>> GetList(CookEquipmentViewRequestModel reqModel);

		Task<bool> SaveForm(CookEquipmentViewEntity entity);
    }
}