using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// ITimeAnalysisIiViewServices
	/// </summary>	
    public interface ITimeAnalysisIiViewServices :IBaseServices<TimeAnalysisIiViewEntity>
	{
		Task<PageModel<TimeAnalysisIiViewEntity>> GetPageList(TimeAnalysisIiViewRequestModel reqModel);

        Task<List<TimeAnalysisIiViewEntity>> GetList(TimeAnalysisIiViewRequestModel reqModel);

		Task<bool> SaveForm(TimeAnalysisIiViewEntity entity);
    }
}