using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IInventoryRequestServices
	/// </summary>	
    public interface IInventoryRequestServices :IBaseServices<InventoryRequestEntity>
	{
		Task<PageModel<InventoryRequestEntity>> GetPageList(InventoryRequestRequestModel reqModel);

        Task<List<InventoryRequestEntity>> GetList(InventoryRequestRequestModel reqModel);

		Task<bool> SaveForm(InventoryRequestEntity entity);
    }
}