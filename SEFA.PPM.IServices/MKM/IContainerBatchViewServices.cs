using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IContainerBatchViewServices
	/// </summary>	
    public interface IContainerBatchViewServices :IBaseServices<ContainerBatchViewEntity>
	{
		Task<PageModel<ContainerBatchViewEntity>> GetPageList(ContainerBatchViewRequestModel reqModel);

        Task<List<ContainerBatchViewEntity>> GetList(ContainerBatchViewRequestModel reqModel);

		Task<bool> SaveForm(ContainerBatchViewEntity entity);
    }
}