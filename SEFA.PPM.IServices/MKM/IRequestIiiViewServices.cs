using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IRequestIiiViewServices
	/// </summary>	
    public interface IRequestIiiViewServices :IBaseServices<RequestIiiViewEntity>
	{
		Task<PageModel<RequestIiiViewEntity>> GetPageList(RequestIiiViewRequestModel reqModel);

        Task<List<RequestIiiViewEntity>> GetList(RequestIiiViewRequestModel reqModel);

		Task<bool> SaveForm(RequestIiiViewEntity entity);
    }
}