using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IProductionSummaryViewServices
	/// </summary>	
    public interface IProductionSummaryViewServices :IBaseServices<ProductionSummaryViewEntity>
	{
		Task<PageModel<ProductionSummaryViewEntity>> GetPageList(ProductionSummaryViewRequestModel reqModel);

        Task<List<ProductionSummaryViewEntity>> GetList(ProductionSummaryViewRequestModel reqModel);

		Task<bool> SaveForm(ProductionSummaryViewEntity entity);
    }
}