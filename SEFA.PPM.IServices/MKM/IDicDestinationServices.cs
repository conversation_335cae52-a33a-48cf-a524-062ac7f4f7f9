using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IDicDestinationServices
	/// </summary>	
    public interface IDicDestinationServices :IBaseServices<DicDestinationEntity>
	{
		Task<PageModel<DicDestinationEntity>> GetPageList(DicDestinationRequestModel reqModel);

        Task<List<DicDestinationEntity>> GetList(DicDestinationRequestModel reqModel);

		Task<bool> SaveForm(DicDestinationEntity entity);
    }
}