2025-06-10 09:56:06.331 +08:00 [DBG] 调用DFM接口
2025-06-10 09:56:07.518 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 09:56:07.527 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 09:57:22.811 +08:00 [DBG] 调用DFM接口
2025-06-10 09:57:23.796 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 09:57:23.808 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:10:36.965 +08:00 [DBG] 调用DFM接口
2025-06-10 10:10:36.965 +08:00 [DBG] 调用DFM接口
2025-06-10 10:10:37.976 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:10:37.984 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:10:38.021 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:10:38.031 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:10:43.134 +08:00 [DBG] 调用DFM接口
2025-06-10 10:10:43.487 +08:00 [DBG] 调用DFM接口
2025-06-10 10:10:44.198 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:10:44.208 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:10:44.372 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:10:44.382 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:22:04.558 +08:00 [DBG] 调用DFM接口
2025-06-10 10:22:05.159 +08:00 [DBG] 调用DFM接口
2025-06-10 10:22:05.618 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:22:05.628 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:22:06.081 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:22:06.093 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:22:29.710 +08:00 [DBG] 调用DFM接口
2025-06-10 10:22:30.086 +08:00 [DBG] 调用DFM接口
2025-06-10 10:22:30.780 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:22:30.791 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 10:22:31.083 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 10:22:31.094 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:09.227 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:09.237 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:10.815 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:10.825 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:11.243 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:11.253 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:14.966 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:15.313 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:16.409 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:16.420 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:16.594 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:16.605 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:33.312 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:33.622 +08:00 [DBG] 调用DFM接口
2025-06-10 12:34:34.660 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:34.672 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:34:34.985 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:34:34.997 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:36:29.548 +08:00 [DBG] 调用DFM接口
2025-06-10 12:36:30.752 +08:00 [DBG] 调用DFM接口
2025-06-10 12:36:31.551 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:36:31.571 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:36:32.672 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:36:32.691 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 12:36:38.729 +08:00 [DBG] 调用DFM接口
2025-06-10 12:36:40.319 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 12:36:40.337 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:03:08.880 +08:00 [DBG] 调用DFM接口
2025-06-10 13:03:08.885 +08:00 [DBG] 调用DFM接口
2025-06-10 13:03:10.549 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:03:10.559 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:03:10.657 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:03:10.669 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:04:01.902 +08:00 [DBG] 调用DFM接口
2025-06-10 13:04:02.308 +08:00 [DBG] 调用DFM接口
2025-06-10 13:04:03.537 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:04:03.553 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:04:03.741 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:04:03.755 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:04:10.809 +08:00 [DBG] 调用DFM接口
2025-06-10 13:04:11.175 +08:00 [DBG] 调用DFM接口
2025-06-10 13:04:12.431 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:04:12.443 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:04:12.603 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:04:12.615 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:12:58.538 +08:00 [DBG] 调用DFM接口
2025-06-10 13:12:59.161 +08:00 [DBG] 调用DFM接口
2025-06-10 13:12:59.752 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:12:59.764 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:13:00.335 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:13:00.347 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:27:12.852 +08:00 [DBG] 调用DFM接口
2025-06-10 13:27:13.720 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:27:13.734 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 13:27:14.158 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 13:27:14.173 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 14:31:18.661 +08:00 [DBG] 调用DFM接口
2025-06-10 14:31:19.776 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 14:31:19.791 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 14:36:26.405 +08:00 [DBG] 调用DFM接口
2025-06-10 14:36:27.554 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 14:36:27.564 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 14:36:32.307 +08:00 [DBG] 调用DFM接口
2025-06-10 14:36:33.243 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 14:36:33.252 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 14:58:39.206 +08:00 [DBG] 调用DFM接口
2025-06-10 14:58:40.179 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 14:58:40.187 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:00:08.535 +08:00 [DBG] 调用DFM接口
2025-06-10 15:00:09.041 +08:00 [DBG] 调用DFM接口
2025-06-10 15:00:09.507 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:00:09.517 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:00:09.933 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:00:09.943 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:54:52.862 +08:00 [DBG] 调用DFM接口
2025-06-10 15:54:53.297 +08:00 [DBG] 调用DFM接口
2025-06-10 15:54:53.821 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:54:53.833 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:54:54.112 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:54:54.123 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:55:20.536 +08:00 [DBG] 调用DFM接口
2025-06-10 15:55:21.056 +08:00 [DBG] 调用DFM接口
2025-06-10 15:55:21.653 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:55:21.664 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 15:55:22.048 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 15:55:22.060 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 16:41:55.995 +08:00 [DBG] 调用DFM接口
2025-06-10 16:41:56.398 +08:00 [DBG] 调用DFM接口
2025-06-10 16:41:57.046 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 16:41:57.058 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 16:41:57.375 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 16:41:57.396 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 16:47:32.642 +08:00 [DBG] 调用DFM接口
2025-06-10 16:47:33.131 +08:00 [DBG] 调用DFM接口
2025-06-10 16:47:33.720 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 16:47:33.732 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 16:47:34.190 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 16:47:34.202 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:18:27.341 +08:00 [DBG] 调用DFM接口
2025-06-10 17:18:27.756 +08:00 [DBG] 调用DFM接口
2025-06-10 17:18:28.913 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:18:28.926 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:18:29.177 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:18:29.190 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:25:27.977 +08:00 [DBG] 调用DFM接口
2025-06-10 17:25:28.592 +08:00 [DBG] 调用DFM接口
2025-06-10 17:25:29.529 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:25:29.542 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:25:29.981 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:25:29.995 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:28:33.146 +08:00 [DBG] 调用DFM接口
2025-06-10 17:28:33.850 +08:00 [DBG] 调用DFM接口
2025-06-10 17:28:34.929 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:28:34.947 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:28:35.353 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:28:35.370 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:28:40.958 +08:00 [DBG] 调用DFM接口
2025-06-10 17:28:40.958 +08:00 [DBG] 本地访问：/api/materialpreparationview/getpagelistmaterialpretop
2025-06-10 17:28:41.317 +08:00 [DBG] 调用DFM接口
2025-06-10 17:28:42.508 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:28:42.524 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:28:42.781 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:28:42.796 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:30:11.342 +08:00 [DBG] 调用DFM接口
2025-06-10 17:30:11.826 +08:00 [DBG] 调用DFM接口
2025-06-10 17:30:12.989 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:30:13.006 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:30:13.333 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:30:13.351 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:31:00.241 +08:00 [DBG] 调用DFM接口
2025-06-10 17:31:00.781 +08:00 [DBG] 调用DFM接口
2025-06-10 17:31:02.019 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:31:02.040 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:31:02.310 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:31:02.328 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:32:11.603 +08:00 [DBG] 调用DFM接口
2025-06-10 17:32:12.770 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:32:12.789 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:32:13.122 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:32:13.141 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:35:32.459 +08:00 [DBG] 调用DFM接口
2025-06-10 17:35:33.490 +08:00 [DBG] 调用DFM接口
2025-06-10 17:35:34.336 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:35:34.360 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:35:35.044 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:35:35.065 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:39:43.644 +08:00 [DBG] 调用DFM接口
2025-06-10 17:39:44.872 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:39:44.893 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:39:45.313 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:39:45.335 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:41:03.161 +08:00 [DBG] 调用DFM接口
2025-06-10 17:41:04.513 +08:00 [DBG] 调用DFM接口
2025-06-10 17:41:06.068 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:41:06.090 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:41:06.309 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:41:06.331 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:46:20.085 +08:00 [DBG] 调用DFM接口
2025-06-10 17:46:20.697 +08:00 [DBG] 调用DFM接口
2025-06-10 17:46:21.815 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:46:21.838 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:46:22.882 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:46:22.904 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:47:19.626 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:47:19.651 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:47:20.000 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:47:20.024 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:50:32.484 +08:00 [DBG] 调用DFM接口
2025-06-10 17:50:33.984 +08:00 [DBG] 调用DFM接口
2025-06-10 17:50:34.608 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:50:34.632 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:50:35.513 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:50:35.537 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:53:09.539 +08:00 [DBG] 调用DFM接口
2025-06-10 17:53:10.449 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:53:10.476 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:53:11.185 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:53:11.213 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:55:42.303 +08:00 [DBG] 调用DFM接口
2025-06-10 17:55:43.944 +08:00 [DBG] 调用DFM接口
2025-06-10 17:55:45.367 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:55:45.396 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:55:45.591 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:55:45.619 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:59:22.313 +08:00 [DBG] 调用DFM接口
2025-06-10 17:59:23.478 +08:00 [DBG] 调用DFM接口
2025-06-10 17:59:24.685 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:59:24.713 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 17:59:25.023 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 17:59:25.050 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:03:49.242 +08:00 [DBG] 调用DFM接口
2025-06-10 18:03:50.512 +08:00 [DBG] 调用DFM接口
2025-06-10 18:03:51.620 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:03:51.653 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:03:52.138 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:03:52.168 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:05:13.097 +08:00 [DBG] 调用DFM接口
2025-06-10 18:05:14.285 +08:00 [DBG] 调用DFM接口
2025-06-10 18:05:15.636 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:05:15.666 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:05:16.050 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:05:16.080 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:52:52.126 +08:00 [DBG] 调用DFM接口
2025-06-10 18:52:53.598 +08:00 [DBG] 调用DFM接口
2025-06-10 18:52:54.682 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:52:54.711 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:52:55.258 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:52:55.289 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:53:16.388 +08:00 [DBG] 调用DFM接口
2025-06-10 18:53:17.790 +08:00 [DBG] 调用DFM接口
2025-06-10 18:53:17.790 +08:00 [INF] --------------------------------
2025/6/10 18:53:17|
RequestID:fb9def74-58f1-4313-aecf-328a130ee899
Request Data:
 Api:/api/MaterialPreparationView/GetDown
 BodyData:

2025-06-10 18:53:19.019 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:53:19.054 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:53:19.660 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:53:19.692 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:56:53.789 +08:00 [DBG] 调用DFM接口
2025-06-10 18:56:55.612 +08:00 [DBG] 调用DFM接口
2025-06-10 18:56:55.612 +08:00 [DBG] 本地访问：/api/materialpreparationview/getdown
2025-06-10 18:56:56.890 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:56:56.928 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:56:57.292 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:56:57.327 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:57:51.800 +08:00 [DBG] 本地访问：/api/materialpreparationview/getnewdown
2025-06-10 18:57:51.800 +08:00 [DBG] 调用DFM接口
2025-06-10 18:57:53.413 +08:00 [DBG] 调用DFM接口
2025-06-10 18:57:54.883 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:57:54.919 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:57:55.210 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:57:55.245 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:58:57.917 +08:00 [DBG] 调用DFM接口
2025-06-10 18:58:59.283 +08:00 [DBG] 调用DFM接口
2025-06-10 18:59:00.868 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:59:00.904 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:59:01.261 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:59:01.303 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:59:40.217 +08:00 [DBG] 调用DFM接口
2025-06-10 18:59:41.822 +08:00 [DBG] 调用DFM接口
2025-06-10 18:59:43.309 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:59:43.351 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 18:59:44.543 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 18:59:44.584 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:29:02.661 +08:00 [DBG] 调用DFM接口
2025-06-10 19:29:04.861 +08:00 [DBG] 调用DFM接口
2025-06-10 19:29:05.842 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:29:05.886 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:29:06.218 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:29:06.259 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:33:27.523 +08:00 [DBG] 调用DFM接口
2025-06-10 19:33:30.006 +08:00 [DBG] 调用DFM接口
2025-06-10 19:33:31.111 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:33:31.159 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:33:31.543 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:33:31.584 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:34:38.264 +08:00 [DBG] 调用DFM接口
2025-06-10 19:34:39.910 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:34:39.953 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:35:25.110 +08:00 [DBG] 调用DFM接口
2025-06-10 19:35:26.661 +08:00 [DBG] 调用DFM接口
2025-06-10 19:35:27.958 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:35:28.000 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:35:28.339 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:35:28.391 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:35:32.900 +08:00 [DBG] 调用DFM接口
2025-06-10 19:35:34.745 +08:00 [DBG] 调用DFM接口
2025-06-10 19:35:36.143 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:35:36.212 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:35:36.538 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:35:36.605 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:08.793 +08:00 [DBG] 调用DFM接口
2025-06-10 19:36:10.676 +08:00 [DBG] 调用DFM接口
2025-06-10 19:36:11.688 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:11.734 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:12.187 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:12.231 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:14.726 +08:00 [DBG] 调用DFM接口
2025-06-10 19:36:15.738 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:15.786 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:25.982 +08:00 [DBG] 调用DFM接口
2025-06-10 19:36:27.061 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:27.108 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:57.641 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:57.691 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-10 19:36:58.131 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-10 19:36:58.178 +08:00 [DBG] 直接返回[]数据[0]条
