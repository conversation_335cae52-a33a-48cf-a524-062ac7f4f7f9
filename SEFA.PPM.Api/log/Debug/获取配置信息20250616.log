2025-06-16 19:43:22.595 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:22.602 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:22.602 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:23.919 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:23.925 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:23.939 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:23.939 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:24.015 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:24.025 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:31.258 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:32.393 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:32.406 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:43.564 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:44.872 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:44.884 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:53.084 +08:00 [DBG] 本地访问：/api/materialpreparationview/getpagelistmaterialpretop
2025-06-16 19:43:53.084 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:53.426 +08:00 [DBG] 调用DFM接口
2025-06-16 19:43:57.823 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:57.841 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:43:58.079 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:43:58.097 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:53:47.382 +08:00 [DBG] 调用DFM接口
2025-06-16 19:53:48.708 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:53:48.716 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:54:00.294 +08:00 [DBG] 调用DFM接口
2025-06-16 19:54:01.326 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:54:01.341 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:54:10.300 +08:00 [DBG] 调用DFM接口
2025-06-16 19:54:11.258 +08:00 [DBG] 调用DFM接口
2025-06-16 19:54:15.253 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:54:15.321 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:54:16.806 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-16 19:54:16.826 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-16 19:58:41.767 +08:00 [DBG] 调用DFM接口
2025-06-16 19:58:41.992 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][1]
2025-06-16 19:58:42.003 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][PrintBagTemplete]值[LKK_BAG_LABEL]
2025-06-16 19:58:42.090 +08:00 [DBG] 查询设备ID[02505082-0081-3664-163e-0370f6000000],返回数据[0]条
