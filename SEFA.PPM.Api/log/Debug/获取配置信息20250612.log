2025-06-12 08:56:03.245 +08:00 [DBG] 调用DFM接口
2025-06-12 08:56:03.245 +08:00 [DBG] 调用DFM接口
2025-06-12 08:56:04.540 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 08:56:04.550 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 08:56:04.575 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 08:56:04.587 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:39:52.686 +08:00 [DBG] 调用DFM接口
2025-06-12 14:39:54.851 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:39:54.862 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:39:57.546 +08:00 [DBG] 调用DFM接口
2025-06-12 14:39:59.115 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:39:59.129 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:42:34.472 +08:00 [DBG] 调用DFM接口
2025-06-12 14:42:35.141 +08:00 [DBG] 调用DFM接口
2025-06-12 14:42:38.089 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:42:38.105 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:42:42.293 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:42:42.308 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:43:01.515 +08:00 [DBG] 调用DFM接口
2025-06-12 14:43:12.435 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:43:12.454 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:43:46.692 +08:00 [DBG] 调用DFM接口
2025-06-12 14:43:50.024 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:43:50.039 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:58:43.164 +08:00 [DBG] 调用DFM接口
2025-06-12 14:58:44.954 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:58:44.971 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:58:45.284 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:58:45.303 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:58:47.582 +08:00 [DBG] 调用DFM接口
2025-06-12 14:58:47.831 +08:00 [DBG] 调用DFM接口
2025-06-12 14:58:50.202 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:58:50.221 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:58:50.929 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:58:50.947 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:59:18.452 +08:00 [DBG] 调用DFM接口
2025-06-12 14:59:19.005 +08:00 [DBG] 调用DFM接口
2025-06-12 14:59:21.060 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:59:21.078 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:59:22.375 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:59:22.393 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:59:25.163 +08:00 [DBG] 调用DFM接口
2025-06-12 14:59:25.568 +08:00 [DBG] 调用DFM接口
2025-06-12 14:59:27.376 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:59:27.395 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 14:59:28.654 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 14:59:28.674 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:06:55.655 +08:00 [DBG] 调用DFM接口
2025-06-12 15:06:59.176 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:06:59.195 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:07:14.622 +08:00 [DBG] 调用DFM接口
2025-06-12 15:07:16.751 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:07:16.771 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:07:18.880 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:07:18.901 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:16:53.215 +08:00 [DBG] 调用DFM接口
2025-06-12 15:16:54.247 +08:00 [DBG] 调用DFM接口
2025-06-12 15:16:55.917 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:16:55.938 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:16:56.361 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:16:56.382 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:19:08.284 +08:00 [DBG] 调用DFM接口
2025-06-12 15:19:09.750 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:19:09.773 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:19:16.612 +08:00 [DBG] 调用DFM接口
2025-06-12 15:19:20.676 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:19:20.697 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:19:27.584 +08:00 [DBG] 调用DFM接口
2025-06-12 15:19:27.958 +08:00 [DBG] 调用DFM接口
2025-06-12 15:19:29.474 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:19:29.497 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:19:29.637 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:19:29.658 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:23:10.282 +08:00 [DBG] 调用DFM接口
2025-06-12 15:23:13.447 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:23:13.472 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:23:13.724 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:23:13.750 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:29:45.251 +08:00 [DBG] 调用DFM接口
2025-06-12 15:29:45.763 +08:00 [DBG] 调用DFM接口
2025-06-12 15:29:47.284 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:29:47.308 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:29:47.549 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:29:47.574 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:29:50.301 +08:00 [DBG] 调用DFM接口
2025-06-12 15:29:50.667 +08:00 [DBG] 调用DFM接口
2025-06-12 15:29:52.087 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:29:52.111 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:29:52.303 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:29:52.331 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:33:49.488 +08:00 [DBG] 调用DFM接口
2025-06-12 15:33:52.010 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:33:52.037 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:33:52.179 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:33:52.208 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:34:38.541 +08:00 [DBG] 调用DFM接口
2025-06-12 15:34:39.491 +08:00 [DBG] 调用DFM接口
2025-06-12 15:34:41.152 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:34:41.186 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 15:34:41.825 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 15:34:41.850 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 17:18:02.030 +08:00 [DBG] 调用DFM接口
2025-06-12 17:18:03.056 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 17:18:03.084 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 17:18:13.158 +08:00 [DBG] 调用DFM接口
2025-06-12 17:18:14.167 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 17:18:14.197 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 17:18:22.670 +08:00 [DBG] 调用DFM接口
2025-06-12 17:18:23.605 +08:00 [DBG] 调用DFM接口
2025-06-12 17:18:24.483 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 17:18:24.514 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-12 17:18:24.645 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-12 17:18:24.674 +08:00 [DBG] 直接返回[]数据[0]条
