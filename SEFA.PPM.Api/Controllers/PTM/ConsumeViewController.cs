using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PTM.IServices;
using SEFA.PTM.Model.Models;

namespace SEFA.PTMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class ConsumeViewController : BaseApiController
    {
        /// <summary>
        /// ConsumeView
        /// </summary>
        private readonly IConsumeViewServices _consumeViewServices;
        private readonly IInterfaceServices _interfaceServices;
        private readonly IDFMServices _iDFMServices;


        public ConsumeViewController(IConsumeViewServices ConsumeViewServices, IInterfaceServices InterfaceServices, IDFMServices iDFMServices)
        {
            _consumeViewServices = ConsumeViewServices;
            _interfaceServices = InterfaceServices;
            _iDFMServices = iDFMServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ConsumeViewEntity>>> GetList([FromBody] ConsumeViewRequestModel reqModel)
        {
            var data = await _consumeViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ConsumeViewEntity>>> GetPageList([FromBody] ConsumeViewRequestModel reqModel)
        {
            var data = await _consumeViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ConsumeViewEntity>> GetEntity(string id)
        {
            var data = await _consumeViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<InventorylistingViewEntity>> Check([FromBody] string subLotId, string materialId)
        {
            var data = await _consumeViewServices.Check(subLotId, materialId);
            if (data.StatusS == "1" || data.StatusS == "2")
            {
                return Failed(data, "库存为限制状态");
            }
            return Success(data, "验证成功");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpPost("{type}")]
        public async Task<MessageModel<string>> ApiByType(string type)
        {
            return await _interfaceServices.ApiByType(type);
        }

        /// <summary>
        /// 自动报工接口
        /// </summary>
        /// <param name="reportType"></param>
        /// <param name="equipmentCode"></param>
        /// <param name="tag"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AutoReportMessageModel>> AutoReport(string reportType, string equipmentCode, string tag)
        {
            var result = await _interfaceServices.AutoReport(reportType, equipmentCode, tag);
            SerilogServer.LogDebug($"【ConsolAutoReportLog】{FAJsonConvert.ToJson(result)}", "AutoReport");
            return result;
        }

        /// <summary>
        /// 自动报工接口
        /// </summary>
        /// <param name="reportType"></param>
        /// <param name="equipmentCode"></param>
        /// <param name="tag"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AutoReportMessageModel>> AutoReport2(string reportType, string equipmentCode, string tag)
        {
            return await _interfaceServices.AutoReport(reportType, equipmentCode, tag);
        }

        /// <summary>
        /// 自动报工接口-增加油自动报工逻辑
        /// </summary>
        /// <param name="reportType"></param>
        /// <param name="equipmentCode"></param>
        /// <param name="tag"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AutoReportMessageModel>> AutoReport_v2(string reportType, string equipmentCode, string tag)
        {
            return await _interfaceServices.AutoReport_v2(reportType, equipmentCode, tag);
        }

        [HttpPost]
        public async Task<MessageModel<SacnSSCCModel>> ScanSSCC([FromBody] SacnSSCCModel reqModel)
        {
            return await _consumeViewServices.ScanSSCC(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Save([FromBody] SacnSSCCModel reqModel)
        {
            return await _consumeViewServices.Save(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> ConsumeReport()
        {
            return await _consumeViewServices.ConsumeReport();
        }

        [HttpPost]
        public async Task<MessageModel<string>> ConsumeReverseReport()
        {
            return await _consumeViewServices.ConsumeReverseReport();
        }

        [HttpPost]
        public async Task<MessageModel<string>> Report2()
        {
            return await _consumeViewServices.Report2();
        }
        [HttpPost]
        public async Task<MessageModel<List<EquipmentInterLockModel>>> GetInterlocks([FromBody] GetInterlockModel reqModel)
        {
            return await _consumeViewServices.GetInterlocks(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdateDataItemDetail([FromBody] DataItemDetailEntity reqModel)
        {
            return await _iDFMServices.UpdateDataItemDetail(reqModel);
        }
    }
    //public class ConsumeViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}