using SEFA.Base.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.IServices.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TippingPrecheckController : BaseApiController
    {
        /// <summary>
        /// TippingPrecheck
        /// </summary>
        private readonly ITippingPrecheckServices _tippingPrecheckServices;

        public TippingPrecheckController(ITippingPrecheckServices TippingPrecheckServices)
        {
            _tippingPrecheckServices = TippingPrecheckServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<TippingPrecheckEntity>>> GetList([FromBody] TippingPrecheckRequestModel reqModel)
        {
            var data = await _tippingPrecheckServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<TippingPrecheckEntity>>> GetPageList([FromBody] TippingPrecheckRequestModel reqModel)
        {
            var data = await _tippingPrecheckServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<TippingPrecheckEntity>> GetEntity(string id)
        {
            var data = await _tippingPrecheckServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> ScanContainerCode([FromBody] string containerCode)
        {
			return await _tippingPrecheckServices.ScanContainerCode(containerCode);
		}

    }
    //public class TippingPrecheckRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}