using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProduceActualViewController : BaseApiController
    {
        /// <summary>
        /// ProduceActualView
        /// </summary>
        private readonly IProduceActualViewServices _produceActualViewServices;
    
        public ProduceActualViewController(IProduceActualViewServices ProduceActualViewServices)
        {
            _produceActualViewServices = ProduceActualViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProduceActualViewEntity>>> GetList([FromBody] ProduceActualViewRequestModel reqModel)
        {
            var data = await _produceActualViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProduceActualViewEntity>>> GetPageList([FromBody] ProduceActualViewRequestModel reqModel)
        {
            var data = await _produceActualViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProduceActualViewEntity>> GetEntity(string id)
        {
            var data = await _produceActualViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] ProduceActualViewEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceActualViewServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceActualViewServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class ProduceActualViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}