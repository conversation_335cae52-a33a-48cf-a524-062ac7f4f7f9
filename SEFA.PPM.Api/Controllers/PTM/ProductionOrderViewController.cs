using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProductionOrderViewController : BaseApiController
    {
        /// <summary>
        /// ProductionOrderView
        /// </summary>
        private readonly IProductionOrderViewServices _productionOrderViewServices;
    
        public ProductionOrderViewController(IProductionOrderViewServices ProductionOrderViewServices)
        {
            _productionOrderViewServices = ProductionOrderViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProductionOrderViewEntity>>> GetList([FromBody] ProductionOrderViewRequestModel reqModel)
        {
            var data = await _productionOrderViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionOrderViewEntity>>> GetPageList([FromBody] ProductionOrderViewRequestModel reqModel)
        {
            var data = await _productionOrderViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionOrderViewEntity>> GetEntity(string id)
        {
            var data = await _productionOrderViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProductionOrderViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _productionOrderViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _productionOrderViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] ProductionOrderViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _productionOrderViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _productionOrderViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class ProductionOrderViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}