using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.MKM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProduceViewController : BaseApiController
    {
        /// <summary>
        /// ProduceView
        /// </summary>
        private readonly IProduceViewServices _produceViewServices;
    
        public ProduceViewController(IProduceViewServices ProduceViewServices)
        {
            _produceViewServices = ProduceViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProduceViewEntity>>> GetList([FromBody] ProduceViewRequestModel reqModel)
        {
            var data = await _produceViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProduceViewEntity>>> GetPageList([FromBody] ProduceViewRequestModel reqModel)
        {
            var data = await _produceViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

		[HttpPost]
		public async Task<MessageModel<string>> Produce([FromBody] ProduceModel reqModel)
		{
			var data = await _produceViewServices.Produce(reqModel);
			return data;
		}

		[HttpPost]
		public async Task<MessageModel<string>> ProduceReport()
		{
			var data = await _produceViewServices.ProduceReport();
			return data;
		}

		[HttpPost]
		public async Task<MessageModel<string>> ProduceReverseReport()
		{
			var data = await _produceViewServices.ProduceReverseReport();
			return data;
		}

		[HttpPost]
		public async Task<MessageModel<string>> Print(string key = "")
		{
			var data = await _produceViewServices.Print(key);
			return data;
		}

		[HttpGet("{id}")]
        public async Task<MessageModel<ProduceViewEntity>> GetEntity(string id)
        {
            var data = await _produceViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] ProduceViewEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceViewServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _produceViewServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class ProduceViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}