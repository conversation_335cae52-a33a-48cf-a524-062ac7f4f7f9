using SEFA.Base.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.IServices.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ConsumeSelectController : BaseApiController
    {
        /// <summary>
        /// ConsumeSelect
        /// </summary>
        private readonly IConsumeSelectServices _consumeSelectServices;

        public ConsumeSelectController(IConsumeSelectServices ConsumeSelectServices)
        {
            _consumeSelectServices = ConsumeSelectServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ConsumeSelectEntity>>> GetList([FromBody] ConsumeSelectRequestModel reqModel)
        {
            var data = await _consumeSelectServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ConsumeSelectEntity>>> GetPageList([FromBody] ConsumeSelectRequestModel reqModel)
        {
            var data = await _consumeSelectServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ConsumeSelectEntity>> GetEntity(string id)
        {
            var data = await _consumeSelectServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ConsumeSelectEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _consumeSelectServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] ConsumeSelectEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _consumeSelectServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _consumeSelectServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class ConsumeSelectRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}