using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class DowntimeReasonMappingController : BaseApiController
    {
        /// <summary>
        /// DowntimeReasonMapping
        /// </summary>
        private readonly IDowntimeReasonMappingServices _downtimeReasonMappingServices;
        public DowntimeReasonMappingController(IDowntimeReasonMappingServices DowntimeReasonMappingServices)
        {
            _downtimeReasonMappingServices = DowntimeReasonMappingServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<DowntimeReasonMappingEntity>>> GetList([FromBody] DowntimeReasonMappingRequestModel reqModel)
        {
            var data = await _downtimeReasonMappingServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 返回eqMentId的reasonList
        /// </summary>
        /// <param name="sort"></param>
        /// <param name="eqMentId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DowntimeReasonMappingEntity>>> GetListByEqMentId([FromBody] string eqMentId)
        {
            string sort = "up";
            var data = await _downtimeReasonMappingServices.GetListBySortOrder(sort, eqMentId);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<DowntimeReasonMappingEntity>>> GetPageList([FromBody] DowntimeReasonMappingRequestModel reqModel)
        {
            var data = await _downtimeReasonMappingServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<DowntimeReasonMappingEntity>> GetEntity(string id)
        {
            var data = await _downtimeReasonMappingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DowntimeReasonMappingEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _downtimeReasonMappingServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _downtimeReasonMappingServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> SaveFormWithReasonList([FromBody] DowntimeReasonMappingSaveListEntity model)
        {
			return await _downtimeReasonMappingServices.SaveFormWithReasonList(model);
		}
        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] DowntimeReasonMappingEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _downtimeReasonMappingServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _downtimeReasonMappingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class DowntimeReasonMappingRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}