using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class DowntimeAutoViewController : BaseApiController
    {
        /// <summary>
        /// DowntimeAutoView
        /// </summary>
        private readonly IDowntimeAutoViewServices _downtimeAutoViewServices;
    
        public DowntimeAutoViewController(IDowntimeAutoViewServices DowntimeAutoViewServices)
        {
            _downtimeAutoViewServices = DowntimeAutoViewServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<DowntimeAutoViewEntity>>> GetList([FromBody] DowntimeAutoViewRequestModel reqModel)
        {
            var data = await _downtimeAutoViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<List<DowntimeAutoViewEntity>>> GetListByEqmentid([FromBody] string eqmentId)
        {
            var data = await _downtimeAutoViewServices.GetListByEqmentid(eqmentId);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<DowntimeAutoViewEntity>>> GetPageList([FromBody] DowntimeAutoViewRequestModel reqModel)
        {
            var data = await _downtimeAutoViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<DowntimeAutoViewEntity>> GetEntity(string id)
        {
            var data = await _downtimeAutoViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DowntimeAutoViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _downtimeAutoViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _downtimeAutoViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] DowntimeAutoViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _downtimeAutoViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _downtimeAutoViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class DowntimeAutoViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}