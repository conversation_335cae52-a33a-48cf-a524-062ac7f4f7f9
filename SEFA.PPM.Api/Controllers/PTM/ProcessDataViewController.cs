using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices.PTM;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;

namespace SEFA.PPM.Api.Controllers.PTM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class ProcessDataViewController : BaseApiController
    {
        /// <summary>
        /// ProcessData
        /// </summary>
        private readonly IProcessDataViewServices _processDataViewServices;

        public ProcessDataViewController(IProcessDataViewServices ProcessDataViewServices)
        {
            _processDataViewServices = ProcessDataViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcessDataViewEntity>>> GetList([FromBody] ProcessDataViewRequestModel reqModel)
        {
            var data = await _processDataViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcessDataViewEntity>>> GetPageList([FromBody] ProcessDataViewRequestModel reqModel)
        {
            var data = await _processDataViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcessDataViewEntity>> GetEntity(string id)
        {
            var data = await _processDataViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据materialVersionId和状态获取最新版本的工艺长文本
        /// </summary>
        /// <param name="materialVersionId"></param>
        /// <param name="status">状态 3 Pending_Release,2 Released, 1 Disable</param>
        /// <returns></returns>
        [HttpGet("{materialVersionId}/{status}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessData(string materialVersionId, string status)
        {
            return await _processDataViewServices.GetLastProcessData(materialVersionId, status);
        }

        /// <summary>
        /// 根据materialVersionId和状态获取最新版本的工艺长文本
        /// </summary>
        /// <param name="materialVersionId"></param>
        /// <param name="status">状态 3 Pending_Release,2 Released, 1 Disable</param>
        /// <returns></returns>
        [HttpGet("{materialVersionId}/{status}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastNewProcessData(string materialVersionId, string status)
        {
            return await _processDataViewServices.GetLastNewProcessData(materialVersionId, status);
        }

        /// <summary>
        /// 保存预处理长文本
        /// </summary>
        /// <param name="materialProcessDataEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SavePreprocessingData([FromBody] MaterialProcessDataEntity materialProcessDataEntity)
        {
            return await _processDataViewServices.SavePreprocessingData(materialProcessDataEntity);
        }

        /// <summary>
        /// 更新预处理长文本
        /// </summary>
        /// <param name="materialProcessDataEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> EditPreprocessingData([FromBody] MaterialProcessDataEntity materialProcessDataEntity)
        {
            return await _processDataViewServices.EditPreprocessingData(materialProcessDataEntity);
        }

        /// <summary>
        /// 根据contextVersionId获取最新的工艺长文本
        /// </summary>
        /// <param name="contextVersionId"></param>
        /// <returns></returns>
        [HttpGet("{contextVersionId}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByContextVersion(string contextVersionId)
        {
            return await _processDataViewServices.GetLastProcessDataByContextVersion(contextVersionId);
        }

        /// <summary>
        /// 根据productionOrderId获取最新的工艺长文本
        /// </summary>
        /// <param name="productionOrderId"></param>
        /// <returns></returns>
        [HttpGet("{productionOrderId}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByOrder(string productionOrderId)
        {
            return await _processDataViewServices.GetLastProcessData(productionOrderId);
        }

        /// <summary>
        /// 根据productionOrderId获取最新的工艺长文本
        /// </summary>
        /// <param name="productionOrderId"></param>
        /// <returns></returns>
        [HttpGet("{productionOrderId}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByPoId(string productionOrderId)
        {
            var result = await _processDataViewServices.GetLastProcessDataByPoId(productionOrderId);
            return result;
        }

        /// <summary>
        /// 保存修改后的工艺长文本
        /// </summary>
        /// <param name="materialProcessDataEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveMaterialProcessData([FromBody] MaterialProcessDataEntity materialProcessDataEntity)
        {
            return await _processDataViewServices.SaveMaterialProcessData(materialProcessDataEntity);
        }

        /// <summary>
        /// 更新工艺长文本状态
        /// </summary>
        /// <param name="materialProcessDataEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateMaterialProcessDataStatus([FromBody] MaterialProcessDataEntity materialProcessDataEntity)
        {
            return await _processDataViewServices.UpdateMaterialProcessDataStatus(materialProcessDataEntity);
        }
    }
    //public class ProcessDataRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}