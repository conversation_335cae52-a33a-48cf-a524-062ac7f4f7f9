using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ContainerHistoryController : BaseApiController
    {
        /// <summary>
        /// ContainerHistory
        /// </summary>
        private readonly IContainerHistoryServices _containerHistoryServices;

        public ContainerHistoryController(IContainerHistoryServices ContainerHistoryServices)
        {
            _containerHistoryServices = ContainerHistoryServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ContainerHistoryEntity>>> GetList([FromBody] ContainerHistoryRequestModel reqModel)
        {
            var data = await _containerHistoryServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ContainerHistoryEntity>>> GetPageList([FromBody] ContainerHistoryRequestModel reqModel)
        {
            Expression<Func<ContainerHistoryEntity, bool>> whereExpression = a => true;
            var data = await _containerHistoryServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ContainerHistoryEntity>> GetEntity(string id)
        {
            var data = await _containerHistoryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ContainerHistoryEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _containerHistoryServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _containerHistoryServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class ContainerHistoryRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}