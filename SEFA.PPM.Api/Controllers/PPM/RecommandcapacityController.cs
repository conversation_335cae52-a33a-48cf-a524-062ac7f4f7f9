using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    /// <summary>
    /// 产线配方及缸容量推荐
    /// </summary>
	[Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RecommandcapacityController : BaseApiController
    {
        /// <summary>
        /// Recommandcapacity
        /// </summary>
        private readonly IRecommandcapacityServices _recommandcapacityServices;

        public RecommandcapacityController(IRecommandcapacityServices RecommandcapacityServices)
        {
            _recommandcapacityServices = RecommandcapacityServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<RecommandcapacityEntity>>> GetList([FromBody] RecommandcapacityRequestModel reqModel)
        {
            var data = await _recommandcapacityServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RecommandcapacityEntity>>> GetPageList([FromBody] RecommandcapacityRequestModel reqModel)
        {
            var data = await _recommandcapacityServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RecommandcapacityEntity>> GetEntity(string id)
        {
            var data = await _recommandcapacityServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RecommandcapacityEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _recommandcapacityServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "操作成功");
            }
            else
            {
                return Failed("操作失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _recommandcapacityServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class RecommandcapacityRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}