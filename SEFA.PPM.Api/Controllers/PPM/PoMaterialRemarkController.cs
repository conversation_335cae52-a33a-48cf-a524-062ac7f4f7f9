using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    /// <summary>
    /// 工单BOM物料备注信息
    /// </summary>
	[Route("ppm/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class PoMaterialRemarkController : BaseApiController
    {
        /// <summary>
        /// PoMaterialRemark
        /// </summary>
        private readonly IPoMaterialRemarkServices _poMaterialRemarkServices;

        public PoMaterialRemarkController(IPoMaterialRemarkServices PoMaterialRemarkServices)
        {
            _poMaterialRemarkServices = PoMaterialRemarkServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoMaterialRemarkEntity>>> GetList([FromBody] PoMaterialRemarkRequestModel reqModel)
        {
            var data = await _poMaterialRemarkServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoMaterialRemarkEntity>>> GetPageList([FromBody] PoMaterialRemarkRequestModel reqModel)
        {
            var data = await _poMaterialRemarkServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 保存工单备注信息
        /// </summary>
        /// <param name="reqModel">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveRemarkList([FromBody] List<PoMaterialRemarkRequestModel> reqModel)
        {
            var result = await _poMaterialRemarkServices.SaveRemarkList(reqModel);
            return result;
        }
        /// <summary>
        /// 获取工单BOM物料备注信息
        /// </summary>
        /// <param name="orderId">查询ID列表</param>param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PoMaterialRemarkRequestModel>>> GetOrderBomMatList([FromBody] string[] orderId)
        {
            var data = await _poMaterialRemarkServices.GetOrderBomMatList(orderId);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoMaterialRemarkEntity>> GetEntity(string id)
        {
            var data = await _poMaterialRemarkServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoMaterialRemarkEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _poMaterialRemarkServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _poMaterialRemarkServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] PoMaterialRemarkEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poMaterialRemarkServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poMaterialRemarkServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoMaterialRemarkRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}