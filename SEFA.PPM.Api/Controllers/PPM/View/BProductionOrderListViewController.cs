using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PTM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class BProductionOrderListViewController : BaseApiController
    {
        /// <summary>
        /// BProductionOrderListView
        /// </summary>
        private readonly IBProductionOrderListViewServices _bProductionOrderListViewServices;
        private readonly IInterfaceServices _iinterfaceServices;
        public BProductionOrderListViewController(IBProductionOrderListViewServices BProductionOrderListViewServices, IInterfaceServices iinterfaceServices)
        {
            _bProductionOrderListViewServices = BProductionOrderListViewServices;
            _iinterfaceServices = iinterfaceServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BProductionOrderListViewEntity>>> GetList([FromBody] BProductionOrderListViewRequestModel reqModel)
        {
            var data = await _bProductionOrderListViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BProductionOrderListViewEntity>>> GetPageList([FromBody] BProductionOrderListViewRequestModel reqModel)
        {
            var data = await _bProductionOrderListViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BProductionOrderListViewEntity>>> GetPackPageList([FromBody] BProductionOrderListViewRequestModel reqModel)
        {
            var data = await _bProductionOrderListViewServices.GetPackPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<BProductionOrderListViewEntity>>> GetQAList([FromBody] BProductionOrderListViewRequestModel reqModel)
        {
            var data = await _bProductionOrderListViewServices.GetQAList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BProductionOrderListViewEntity>>> GetQAPageList([FromBody] BProductionOrderListViewRequestModel reqModel)
        {
            var data = await _bProductionOrderListViewServices.GetQAPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BProductionOrderListViewEntity>> GetEntity(string id)
        {
            var data = await _bProductionOrderListViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BProductionOrderListViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _bProductionOrderListViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _bProductionOrderListViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> OperationPo([FromBody] ConsolPoRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.OperationPo(reqModel);
        }

        /// <summary>
        /// 修改工单状态
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdatePoStatus([FromBody] UpdateStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.UpdatePoStatus(reqModel);
        }

        /// <summary>
        /// 更新批次状态
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateShift([FromBody] UpdateStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.UpdateShift(reqModel);
        }

        /// <summary>
        /// 获取班次信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DFM.Model.Models.ShiftEntity>>> GetShiftSelect()
        {
            var data = await _bProductionOrderListViewServices.GetShiftSelect();
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<dynamic>>> CreateBatch([FromBody] List<string> productionOrderIds)
        {
            return await _bProductionOrderListViewServices.CreateBatch(productionOrderIds);
        }

        [HttpPost]
        public async Task<MessageModel<string>> AddPoConsumeRequirement([FromBody] PoConsumeRequirementRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.AddPoConsumeRequirement(reqModel);
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> CreateBatchByOrderId([FromBody] string productionOrderId)
        //{
        //	return await _bProductionOrderListViewServices.CreateBatchByOrderId(productionOrderId);
        //}

        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetEquipmentsByOrderId([FromBody] string productionOrderId)
        {
            return await _bProductionOrderListViewServices.GetEquipmentsByOrderId(productionOrderId);
        }

        #region QA

        /// <summary>
        /// 修改QA状态
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateQaStatus([FromBody] UpdateQaStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.UpdateQaStatus(reqModel);
        }

        /// <summary>
        /// 获取工单长文本
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<UpdateQaStatusRequestModel>> GetLtexts([FromBody] UpdateQaStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.GetLtexts(reqModel);
        }

        /// <summary>
        /// 获取工单长文本
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialProcessDataEntity>>> GetCookieOrderLtexts([FromBody] UpdateQaStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.GetCookieOrderLtexts(reqModel);
        }

        /// <summary>
        /// 修改工单长文本
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateLtexts([FromBody] UpdateQaStatusRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.UpdateLtexts(reqModel);
        }

        #endregion
        /// <summary>
        /// 重新解析生成批次
        /// </summary>
        /// <param name="productionOrderIds"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> RebuildBatch([FromBody] List<string> productionOrderIds)
        {
            return await _bProductionOrderListViewServices.RebuildBatch(productionOrderIds);
        }

        /// <summary>
        /// 重新绑定配方
        /// </summary>
        /// <param name="productionOrderIds"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> BindPoRecipe([FromBody] List<string> productionOrderIds)
        {
            return await _bProductionOrderListViewServices.BindPoRecipe(productionOrderIds);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productionOrderIds"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<OrderTreeModel>>> PackReport([FromBody] List<string> productionOrderIds)
        {
            return await _bProductionOrderListViewServices.PackReport(productionOrderIds);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SavePackReport([FromBody] List<OrderTreeModel> reqModel)
        {
            return await _bProductionOrderListViewServices.SavePackReport(reqModel);
        }


        [HttpPost]
        public async Task<MessageModel<string>> CheckLongText([FromBody] string productionOrderId)
        {
            return await _bProductionOrderListViewServices.CheckLongText(productionOrderId);
        }

        [HttpPost]
        public async Task<MessageModel<ParameterDownloadModel>> GetPoRecipeData([FromBody] string productionOrderId)
        {
            return await _bProductionOrderListViewServices.GetPoRecipeData(productionOrderId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> SendLabelPrintToColos([FromBody] COLOS_LabelPrint_r reqModel)
        {
            return await _iinterfaceServices.SendLabelPrintToColos(reqModel);
        }

        [HttpGet("{productionOrderId}/{batchCode}")]
        public async Task<MessageModel<string>> GetQrCode(string productionOrderId, string batchCode)
        {
            return await _iinterfaceServices.GetQrCode(productionOrderId, batchCode);
        }

        #region 喉头产出

        /// <summary>
        /// 获取设备下拉选
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<Select>>> GetEquipmentsSelect()
        {
            var data = await _bProductionOrderListViewServices.GetEquipmentsSelect();
            return Success(data);
        }

        /// <summary>
        /// 获取产出界面数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<ProduceOpenModel>> ProduceOpen([FromBody] StartPoRequestModel reqModel)
        {
            return await _bProductionOrderListViewServices.ProduceOpen(reqModel);
        }

        #endregion

    }
    //public class BProductionOrderListViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}