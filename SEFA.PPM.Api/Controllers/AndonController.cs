using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;

namespace SEFA.PPM.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class AndonController : BaseApiController
    {
        /// <summary>
        ///
        /// </summary>
        private readonly IAndonServices _andonServices;

        public AndonController(IAndonServices andonServices)
        {
            _andonServices = andonServices;
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckRawMaterialInventory(int days)
        {
            return await _andonServices.CheckRawMaterialInventory(days);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckPackagingInventory(int days)
        {
            return await _andonServices.CheckPackagingInventory(days);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckOrderExecutionStatus(string areaCode)
        {
            return await _andonServices.CheckOrderExecutionStatus(areaCode);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckMaterialTimeliness(int days, string locations)
        {
            return await _andonServices.CheckMaterialTimeliness(days, locations);
        }

        [HttpPost]
        public async Task<MessageModel<dynamic>> CheckInspectionTime(string logsheetId)
        {
            return await _andonServices.CheckInspectionTime(logsheetId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckResubmissionDuration(string logsheetId, DateTime inspectionTime)
        {
            return await _andonServices.CheckResubmissionDuration(logsheetId, inspectionTime);
        }

        [HttpPost]
        public async Task<MessageModel<dynamic>> CheckFeedingTime(string batchId, string equipmentId, DateTime applicationTime)
        {
            return await _andonServices.CheckFeedingTime(batchId, equipmentId, applicationTime);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckNextOrderMaterialPrep(string poExecutionId)
        {
            return await _andonServices.CheckNextOrderMaterialPrep(poExecutionId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckInspectionResult(string logsheetId)
        {
            return await _andonServices.CheckInspectionResult(logsheetId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckRawWaterLevel()
        {
            return await _andonServices.CheckRawWaterLevel();
        }

        [HttpPost]
        public async Task<MessageModel<dynamic>> CheckAfterCIPResumeTime(string equipmentId, DateTime cipTime)
        {
            return await _andonServices.CheckAfterCIPResumeTime(equipmentId, cipTime);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckCompletenessOfPkg(int hour)
        {
            return await _andonServices.CheckCompletenessOfPkg(hour);
        }

        [HttpPost]
        public async Task<MessageModel<string>> EditOrder(string productionOrderNo, string productLine, [FromBody] List<dynamic> editInfo)
        {
            return await _andonServices.EditOrder(productionOrderNo, productLine, editInfo);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CheckQuantityOfRejects(string logsheetId, int hour, int quantity)
        {
            return await _andonServices.CheckQuantityOfRejects(logsheetId, hour, quantity);
        }

        [HttpPost]
        public async Task<MessageModel<bool>> SendLogsheetIdToAndon(string logsheetId)
        {
            return await _andonServices.SendLogsheetIdToAndon(logsheetId);
        }

        [HttpPost]
        public async Task<MessageModel<bool>> SendNotuptostandardLogsheetIdToAndon(string logsheetId, DateTime inspectionTime)
        {
            return await _andonServices.SendNotuptostandardLogsheetIdToAndon(logsheetId, inspectionTime);
        }

        [HttpPost]
        public async Task<MessageModel<bool>> SendBatchIdToAndon(string batchId, string equipmentId)
        {
            return await _andonServices.SendBatchIdToAndon(batchId, equipmentId);
        }

        [HttpPost]
        public async Task<MessageModel<bool>> SendCipEquipmentIdToAndon(string equipmentId, DateTime cipTime)
        {
            return await _andonServices.SendCipEquipmentIdToAndon(equipmentId, cipTime);
        }

        [HttpPost]
        public async Task<MessageModel<bool>> SendQATimeToAndon(string logsheetId, DateTime qaTime)
        {
            return await _andonServices.SendQATimeToAndon(logsheetId, qaTime);
        }

        [HttpPost]
        public async Task<MessageModel<dynamic>> CheckSamplingOnTime(string logsheetId, DateTime qaTime)
        {
            return await _andonServices.CheckSamplingOnTime(logsheetId, qaTime);
        }
    }
}