using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Services;
using SEFA.DFM.Model.Models;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Magicodes.ExporterAndImporter.Excel;
using AutoMapper;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LosstgtControllerPakMatGroup : BaseApiController
    {
        /// <summary>
        /// Losstgt
        /// </summary>
        private readonly ILosstgtServicesPakMatGroup _losstgtServicesPakMatGroup;
        private readonly IMapper _mapper;
        public LosstgtControllerPakMatGroup(ILosstgtServicesPakMatGroup losstgtServicesPakMatGroup, IMapper mapper)
        {
            _losstgtServicesPakMatGroup = losstgtServicesPakMatGroup;
            _mapper = mapper;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<LosstgtGroupModel>>> GetList([FromBody] LosstgtRequestModel reqModel)
        {
            var data = await _losstgtServicesPakMatGroup.GetList(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData([FromBody] LosstgtRequestModel reqModel)
        {
            ExcelExporter exporter = new ExcelExporter();
            var query = await _losstgtServicesPakMatGroup.GetList(reqModel);//准备业务数据
            //业务数据处理
            var queryData = query.Select(item =>
            {
                var dto = new LosstgtExcelDtoYear();
                _mapper.Map(item, dto);
                return dto;
            }).ToList();
            var result = await exporter.ExportAsByteArray<LosstgtExcelDtoYear>(queryData);//生成导出数据
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"包材损耗年目标手动录入数据-{DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")}");
        }

        /// <summary>
        /// 模板下载
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> MtrGroupDownload()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<LosstgtExcelDtoYear>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "包材损耗年目标导入模板");
        }
        [HttpPost]
        public async Task<MessageModel<List<string>>> GetItem()
        {
            var data = await _losstgtServicesPakMatGroup.GetItem();
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<List<string>>> GetItemGroup()
        {
            var data = await _losstgtServicesPakMatGroup.GetItemGroup();
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input">文件的物理路径</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ImportData([FromForm] FileImportDto input)
        {
            var result = await _losstgtServicesPakMatGroup.ImportData(input);
            if (result.Succeed)
                return Success(result.Data, "导入成功");
            else
                return Failed("导入失败：" + result.Error.Text);
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<LosstgtEntity>>> GetPageList([FromBody] LosstgtRequestModel reqModel)
        {
            var data = await _losstgtServicesPakMatGroup.GetPageList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpGet("{id}")]
        public async Task<MessageModel<LosstgtEntity>> GetEntity(string id)
        {
            var data = await _losstgtServicesPakMatGroup.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LosstgtEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                request.LossType = "包材损耗-小分类";
                data.success = await _losstgtServicesPakMatGroup.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                request.LossType = "包材损耗-小分类";
                data.success = await _losstgtServicesPakMatGroup.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] LosstgtEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _losstgtServicesPakMatGroup.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _losstgtServicesPakMatGroup.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LosstgtRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}