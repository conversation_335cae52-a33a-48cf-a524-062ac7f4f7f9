using SEFA.Base.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PPM.IServices.SIM;
using SEFA.PPM.Model.ViewModels.SIM;
using SEFA.PPM.Model.Models.SIM;

namespace SEFA.PPM.Api.Controllers.SIM
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class WorkingHourController : BaseApiController
    {
        /// <summary>
        /// WorkingHour
        /// </summary>
        private readonly IWorkingHourServices _workingHourServices;

        public WorkingHourController(IWorkingHourServices WorkingHourServices)
        {
            _workingHourServices = WorkingHourServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<WorkingHourEntity>>> GetList([FromBody] WorkingHourRequestModel reqModel)
        {
            var data = await _workingHourServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<WorkingHourEntity>>> GetPageList([FromBody] WorkingHourRequestModel reqModel)
        {
            var data = await _workingHourServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<WorkingHourEntity>> GetEntity(string id)
        {
            var data = await _workingHourServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] WorkingHourEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _workingHourServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _workingHourServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] WorkingHourEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _workingHourServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _workingHourServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class WorkingHourRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}