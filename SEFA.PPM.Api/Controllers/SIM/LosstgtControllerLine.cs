using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Services;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Magicodes.ExporterAndImporter.Excel;
using AutoMapper;
using SEFA.DFM.Model.Models;
using SEFA.MKM.Model.Models;
using SEFA.MKM.IServices;
using SEFA.PPM.Model.ViewModels.SIM.View;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LosstgtControllerLine : BaseApiController
    {
        /// <summary>
        /// Losstgt
        /// </summary>
        private readonly ILosstgtServicesLine _losstgtServicesLine;
        private readonly IMapper _mapper;
        private readonly IEquipmentServices _equipmentEntity ;

        public LosstgtControllerLine(ILosstgtServicesLine losstgtServicesLine, IMapper mapper, IEquipmentServices equipmentEntity)
        {
            _losstgtServicesLine = losstgtServicesLine;
            _mapper = mapper;
            _equipmentEntity = equipmentEntity;
        }
        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData([FromBody] LosstgtRequestModel reqModel)
        {
            try
            {
                ExcelExporter exporter = new ExcelExporter();
                var query = await _losstgtServicesLine.LossLineExportData(reqModel);//准备业务数据
                                                                                    //业务数据处理
                var queryData = query.Select(item =>
                {
                    var dto = new LosstgtExcelDtoLine();
                    _mapper.Map(item, dto);
                    return dto;
                }).ToList();
                var result = await exporter.ExportAsByteArray<LosstgtExcelDtoLine>(queryData);//生成导出数据
                var fs = new MemoryStream(result);
                return new XlsxFileResult(stream: fs, fileDownloadName: $"包材损耗年目标-生产线手动录入数据-{DateTime.Now.ToString("yyyyMMddHHmmss")}");
            }
            catch (Exception ex)
            {

                throw new Exception($"包材损耗年目标-生产线手动录入数据导出出现错误:{ex.Message}");
            }
           
        }

        /// <summary>
        /// 模板下载
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> LossLineDownload()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<LosstgtExcelDtoLine>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "包材损耗年目标-生产线导入模板");
        }
        [HttpPost]
        public async Task<MessageModel<List<LosstgtEntity>>> GetList([FromBody] LosstgtRequestModel reqModel)
        {
            var data = await _losstgtServicesLine.GetList(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input">文件的物理路径</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ImportData([FromForm] FileImportDto input)
        {
            var result = await _losstgtServicesLine.ImportData(input);
            if (result.Succeed)
                return Success(result.Data, "导入成功");
            else
                return Failed("导入失败：" + result.Error.Text);
        }
        /// <summary>
        /// 包材损耗-生产线 查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<LossTgtLineViewEntity>>> GetPageList([FromBody] LosstgtViewRequestModel reqModel)
        {
            var data = await _losstgtServicesLine.GetPageList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<List<string>>> GetItem()
        {
            var data = await _losstgtServicesLine.GetItem();
            return Success(data, "获取成功");
        }
        [HttpGet("{id}")]
        public async Task<MessageModel<LosstgtEntity>> GetEntity(string id)
        {
            var data = await _losstgtServicesLine.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LosstgtEntity request)
        {
            var data = new MessageModel<string>();
            request.LossType = "包材损耗-生产线";
            request.Item = "";
            var equit = await _equipmentEntity.FindEntity(request.ModelRef);
            if (equit != null)
            {
                if (equit.Level== "Segment" || equit.Level == "Line")
                {
                    if (equit.Level == "Segment")
                    {
                        request.ModelRef = equit.LineId;
                    }
                    else
                    {
                        request.ModelRef = equit.ID;
                    }
                }
                else
                {
                    return Failed("请选择生产线或工作中心");
                }
            }
            else
            {
                return Failed("模型区域未找到");
            }
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _losstgtServicesLine.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _losstgtServicesLine.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] LosstgtEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _losstgtServicesLine.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _losstgtServicesLine.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LosstgtRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}