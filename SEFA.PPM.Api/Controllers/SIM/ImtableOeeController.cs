using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ImtableOeeController : BaseApiController
    {
        /// <summary>
        /// ImtableOee
        /// </summary>
        private readonly IImtableOeeServices _imtableOeeServices;
    
        public ImtableOeeController(IImtableOeeServices ImtableOeeServices)
        {
            _imtableOeeServices = ImtableOeeServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ImtableOeeEntity>>> GetList([FromBody] ImtableOeeRequestModel reqModel)
        {
            var data = await _imtableOeeServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ImtableOeeEntity>>> GetPageList([FromBody] ImtableOeeRequestModel reqModel)
        {
            var data = await _imtableOeeServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ImtableOeeEntity>> GetEntity(string id)
        {
            var data = await _imtableOeeServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ImtableOeeEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _imtableOeeServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _imtableOeeServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] ImtableOeeEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _imtableOeeServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _imtableOeeServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class ImtableOeeRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}