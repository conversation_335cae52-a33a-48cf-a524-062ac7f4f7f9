using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class ImtableCokadjustmentController : BaseApiController
    {
        /// <summary>
        /// ImtableCokadjustment
        /// </summary>
        private readonly IImtableCokadjustmentServices _imtableCokadjustmentServices;

        public ImtableCokadjustmentController(IImtableCokadjustmentServices ImtableCokadjustmentServices)
        {
            _imtableCokadjustmentServices = ImtableCokadjustmentServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ImtableCokadjustmentEntity>>> GetList([FromBody] ImtableCokadjustmentRequestModel reqModel)
        {
            var data = await _imtableCokadjustmentServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SyncLogsheetData()
        {
            var sucess = await _imtableCokadjustmentServices.SyncLogsheetData();
            if (sucess)
            {
                return Success("", "保存成功");
            }
            else
            {
                return Failed("保存失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveCokadjustment(DateTime? startTime, DateTime? endTime)
        {
            if (startTime == null || endTime == null)
            {
                endTime = DateTime.Now.Date.AddSeconds(-1);
                startTime = endTime.Value.Date;
            }
            return await _imtableCokadjustmentServices.SaveCokadjustment(startTime.Value, endTime.Value);
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ImtableCokadjustmentEntity>>> GetPageList([FromBody] ImtableCokadjustmentRequestModel reqModel)
        {
            var data = await _imtableCokadjustmentServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ImtableCokadjustmentEntity>> GetEntity(string id)
        {
            var data = await _imtableCokadjustmentServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ImtableCokadjustmentEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _imtableCokadjustmentServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _imtableCokadjustmentServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] ImtableCokadjustmentEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _imtableCokadjustmentServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _imtableCokadjustmentServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        /// <summary>
        /// 煮缸调节率（QA质检单）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AdjustmentRateModel>> AdjustmentRate(ImtableCokadjustmentRequestModel reqModel)
        {
            var data = await _imtableCokadjustmentServices.AdjustmentRate(reqModel);
            return Success(data, "获取成功");
        }
    }
    //public class ImtableCokadjustmentRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}