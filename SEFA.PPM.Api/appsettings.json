{
  "urls": "http://*:9292", //web服务端口，如果用IIS部署，把这个去掉
  "Logging": {
    "LogLevel": {
      "Default": "Information", //加入Default否则log4net本地写入不了日志
      "SEFA.Base.AuthHelper.ApiResponseHandler": "Error"
    },
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning",
        "Microsoft.Hosting.Lifetime": "Debug"
      }
    },
    "Log4Net": {
      "Name": "SEFA.PPM"
    }
  },
  "AllowedHosts": "*",
  "Redis": {
    "ConnectionString": "*************:16379,password=admin@******"
  },
  "RabbitMQ": {
    "Enabled": false,
    "Connection": "*************",
    "UserName": "",
    "Password": "!",
    "RetryCount": 3
  },
  "Kafka": {
    "Enabled": false,
    "Servers": "localhost:9092",
    "Topic": "sefa",
    "GroupId": "sefa-consumer",
    "NumPartitions": 3 //主题分区数量
  },
  "FileSystem": {
    "Addr": "D:/uploadfile/"
  },
  "EventBus": {
    "Enabled": false,
    "SubscriptionClientName": "SEFA.PPM"
  },
  "AppSettings": {
    "RedisCachingAOP": {
      "Enabled": false
    },
    "MemoryCachingAOP": {
      "Enabled": true
    },
    "LogAOP": {
      "Enabled": true
    },
    "TranAOP": {
      "Enabled": false
    },
    "SqlAOP": {
      "Enabled": true,
      "OutToLogFile": {
        "Enabled": false
      },
      "OutToConsole": {
        "Enabled": true
      }
    },
    "LogToDb": {
      "Enabled": false
    },
    "Date": "2022-05-20",
    "SeedDBEnabled": false, //只生成表结构
    "SeedDBDataEnabled": false, //生成表,并初始化数据
    "Author": "SEFA.PPM",
    "SvcName": "Production", //
    "UseLoadTest": false
  },

  // 请配置MainDB为你想要的主库的ConnId值,并设置对应的Enabled为true；
  // *** 单库操作，把 MutiDBEnabled 设为false ***；
  // *** 多库操作，把 MutiDBEnabled 设为true，其他的从库Enabled也为true **；
  

  "MainDB": "SEFAMES_MSSQL_1", //当前项目的主库，所对应的连接字符串的Enabled必须为true
  "MutiDBEnabled": true, //是否开启多库模式
  "CQRSEnabled": false, //是否开启读写分离模式,必须是单库模式，且数据库类型一致，比如都是SqlServer
  "DBS": [
    /*
      对应下边的 DBType
      MySql = 0,
      SqlServer = 1,
      Sqlite = 2,
      Oracle = 3,
      PostgreSQL = 4,
      Dm = 5,//达梦
      Kdbndp = 6,//人大金仓
    */

    {
      "ConnId": "SEFAMES_MSSQL_1",
      "DBType": 1,
      "Enabled": true,
      "HitRate": 50,
      //"Connection": "Data Source=localhost;Initial Catalog=SEFAMES;Password=**;User ID=**;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
      "ProviderName": "System.Data.SqlClient"
    },
    {
      "ConnId": "DATAWAREHOUSE",
      "DBType": 1,
      "Enabled": true,
      "HitRate": 40,
      "Connection": "Data Source=localhost;Initial Catalog=DataWarehouse;Password=**;User ID=**;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
      "ProviderName": "System.Data.SqlClient"
    },
    {
      "ConnId": "SEFAMES_SQLITE",
      "DBType": 2,
      "Enabled": false,
      "HitRate": 30, // 值越大，优先级越高
      "Connection": "SEFAMES.db" //sqlite只写数据库名就行
    },
    {
      "ConnId": "SEFAMES_MYSQL",
      "DBType": 0,
      "Enabled": false,
      "HitRate": 20,
      "Connection": "server=.;Database=ddd;Uid=root;Pwd=******;Port=10060;Allow User Variables=True;"
    },
    {
      "ConnId": "SEFAMES_MYSQL_2",
      "DBType": 0,
      "Enabled": false,
      "HitRate": 20,
      "Connection": "server=.;Database=001;Uid=root;Pwd=******;Port=3096;Allow User Variables=True;"
    },
    {
      "ConnId": "SEFAMES_ORACLE",
      "DBType": 3,
      "Enabled": false,
      "HitRate": 10,
      "Connection": "Data Source=127.0.0.1/ops;User ID=OPS;Password=******;Persist Security Info=True;Connection Timeout=60;"
    },
    {
      "ConnId": "SEFAMES_DM",
      "DBType": 5,
      "Enabled": false,
      "HitRate": 10,
      "Connection": "PORT=5236;DATABASE=DAMENG;HOST=localhost;PASSWORD=******;USER ID=******;"
    },
    {
      "ConnId": "SEFAMES_KDBNDP",
      "DBType": 6,
      "Enabled": false,
      "HitRate": 10,
      "Connection": "Server=127.0.0.1;Port=54321;UID=SYSTEM;PWD=system;database=SQLSUGAR4XTEST1;"
    }
  ],
  "Audience": {
    "Secret": "sdfsdfsrty45634kkhllghtdgdfss345t678fs", //不要太短，16位+
    "SecretFile": "C:\\my-file\\FA.audience.secret.txt", //安全。内容就是Secret
    "Issuer": "FA",
    "Audience": "wr"
  },
  "Mongo": {
    "ConnectionString": "mongodb://nosql.data",
    "Database": "SEFAMESDb"
  },
  "SapConnection": {
    "ConnectionString": "AppServerHost=**p.aac.com; SystemNumber=01; User=MES3; Password=********; Client=800; Language=ZH; PoolSize=5; Trace=8",
    "FactoryCode": "2050"
  },
  "Startup": {
    "Cors": {
      "PolicyName": "CorsIpAccess", //策略名称
      "EnableAllIPs": false, //当为true时，开放所有IP均可访问。
      // 支持多个域名端口，注意端口号后不要带/斜杆：比如localhost:8000/，是错的
      // 注意，http://127.0.0.1:1818 和 http://localhost:1818 是不一样的
      "IPs": "http://127.0.0.1:2364,http://localhost:2364"
    },
    "AppConfigAlert": {
      "Enabled": true
    },
    "ApiName": "SEFA.PPM",
    "IdentityServer4": {
      "Enabled": false, // 这里默认是false，表示使用jwt，如果设置为true，则表示系统使用Ids4模式
      "AuthorizationUrl": "http://localhost:5004", // 认证中心域名
      "ApiName": "sefa.ppm.api" // 资源服务器
    },
    "RedisMq": {
      "Enabled": false //redis 消息队列
    },
    "MiniProfiler": {
      "Enabled": true //性能分析开启
    },
    "Nacos": {
      "Enabled": false //Nacos注册中心
    }
  },
  "Middleware": {
    "RequestResponseLog": {
      "Enabled": false
    },
    "IPLog": {
      "Enabled": true
    },
    "RecordAccessLogs": {
      "Enabled": true,
      "IgnoreApis": "/api/permission/getnavigationbar,/api/monitor/getids4users,/api/monitor/getaccesslogs,/api/monitor/server,/api/monitor/getactiveusers,/api/monitor/server,"
    },
    "SignalR": {
      "Enabled": false
    },
    "QuartzNetJob": {
      "Enabled": true
    },
    "Consul": {
      "Enabled": false
    },
    "IpRateLimit": {
      "Enabled": true
    }
  },
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": true, //False: globally executed, true: executed for each
    "StackBlockedRequests": false, //False: Number of rejections should be recorded on another counter
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "IpWhitelist": [], //白名单
    "EndpointWhitelist": [ "get:/api/xxx", "*:/api/yyy" ],
    "ClientWhitelist": [ "dev-client-1", "dev-client-2" ],
    "QuotaExceededResponse": {
      "Content": "{{\"status\":429,\"msg\":\"访问过于频繁，请稍后重试\",\"success\":false}}",
      "ContentType": "application/json",
      "StatusCode": 429
    },
    "HttpStatusCode": 429, //返回状态码
    "GeneralRules": [ //api规则,结尾一定要带*
      {
        "Endpoint": "*:/api/dfm*",
        "Period": "1m",
        "Limit": 20
      },
      {
        "Endpoint": "*/api/*",
        "Period": "1s",
        "Limit": 3
      },
      {
        "Endpoint": "*/api/*",
        "Period": "1m",
        "Limit": 30
      },
      {
        "Endpoint": "*/api/*",
        "Period": "12h",
        "Limit": 500
      }
    ]

  },
  "ConsulSetting": {
    "ServiceName": "DFMService",
    "ServiceIP": "localhost",
    "ServicePort": "9292",
    "ServiceHealthCheck": "/healthcheck",
    "ConsulAddress": "http://localhost:8500"
  },
  "nacos": {
    "ServerAddresses": [ "http://localhost:8848" ], // nacos 连接地址
    "DefaultTimeOut": 15000, // 默认超时时间
    "Namespace": "public", // 命名空间
    "ListenInterval": 10000, // 监听的频率
    "ServiceName": "SEFA.PPM.Api", // 服务名
    "Port": "9292", // 服务端口号
    "RegisterEnabled": true // 是否直接注册nacos
  },
  "LogFiedOutPutConfigs": {
    "tcpAddressHost": "", // 输出elk的tcp连接地址
    "tcpAddressPort": 0, // 输出elk的tcp端口号
    "ConfigsInfo": [ // 配置的输出elk节点内容 常用语动态标识
      {
        "FiedName": "applicationName",
        "FiedValue": "SEFA.PPM.Api"
      }
    ]
  },
  "RegisterDlls": [
    {
      "dllName": "SEFA.PPM.Services.dll"
    },
    {
      "dllName": "SEFA.PPM.Repository.dll"
    }
  ],
  "ServerList": [
    {
      "ServerName": "DFM",
      "Url": "http://localhost:9292/"
    },
    {
      "ServerName": "ORDER",
      "Url": "http://localhost:9290/"
    }
  ],
  "XmlList": {
    "ControllerXml": "SEFA.PPM.xml",
    "ModelXml": "SEFA.PPM.Model.xml"
  }
}
